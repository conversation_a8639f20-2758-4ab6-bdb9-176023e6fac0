package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgClassMapper;
import com.cl.project.business.mapper.KgStudentMapper;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.service.IKgClassService;

/**
 * 班级信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgClassServiceImpl implements IKgClassService
{

    @Autowired
    private KgClassMapper kgClassMapper;
    @Override
    public List<KgClass> selectClassesByCourseWithSchedule(Long courseId) {
        return kgClassMapper.selectClassesByCourseWithSchedule(courseId);
    }

    @Autowired
    private KgStudentMapper kgStudentMapper;

    /**
     * 查询班级信息
     * 
     * @param classId 班级信息ID
     * @return 班级信息
     */
    @Override
    public KgClass selectKgClassById(Long classId)
    {
        return kgClassMapper.selectKgClassById(classId);
    }
    
    /**
     * 根据钉钉部门ID查询班级信息
     * 
     * @param dingtalkDeptId 钉钉部门ID
     * @return 班级信息
     */
    @Override
    public KgClass selectKgClassByDingtalkDeptId(Long dingtalkDeptId)
    {
        return kgClassMapper.selectKgClassByDingtalkDeptId(dingtalkDeptId);
    }

    /**
     * 查询班级信息列表
     * 
     * @param kgClass 班级信息
     * @return 班级信息
     */
    @Override
    public List<KgClass> selectKgClassList(KgClass kgClass)
    {
        return kgClassMapper.selectKgClassList(kgClass);
    }

    /**
     * 新增班级信息
     * 
     * @param kgClass 班级信息
     * @return 结果
     */
    @Override
    public int insertKgClass(KgClass kgClass)
    {
        kgClass.setCreateTime(DateUtils.getNowDate());
        return kgClassMapper.insertKgClass(kgClass);
    }

    /**
     * 修改班级信息
     * 
     * @param kgClass 班级信息
     * @return 结果
     */
    @Override
    public int updateKgClass(KgClass kgClass)
    {
        kgClass.setUpdateTime(DateUtils.getNowDate());
        return kgClassMapper.updateKgClass(kgClass);
    }

    /**
     * 批量删除班级信息
     * 
     * @param classIds 需要删除的班级信息ID
     * @return 结果
     */
    @Override
    public int deleteKgClassByIds(Long[] classIds)
    {
        return kgClassMapper.deleteKgClassByIds(classIds);
    }

    /**
     * 删除班级信息信息
     *
     * @param classId 班级信息ID
     * @return 结果
     */
    @Override
    public int deleteKgClassById(Long classId)
    {
        return kgClassMapper.deleteKgClassById(classId);
    }

    /**
     * 更新班级当前人数
     *
     * @param classId 班级ID
     * @return 结果
     */
    @Override
    public int updateClassCurrentCount(Long classId)
    {
        if (classId == null) {
            return 0;
        }

        // 查询班级下正常状态的学生数量
        KgStudent queryStudent = new KgStudent();
        queryStudent.setClassId(classId);
        queryStudent.setStatus("0"); // 正常状态
        List<KgStudent> students = kgStudentMapper.selectKgStudentList(queryStudent);

        // 更新班级当前人数
        KgClass updateClass = new KgClass();
        updateClass.setClassId(classId);
        updateClass.setCurrentCount((long) students.size());

        return kgClassMapper.updateKgClass(updateClass);
    }

    /**
     * 批量更新所有班级当前人数
     *
     * @return 更新的班级数量
     */
    @Override
    public int updateAllClassCurrentCount()
    {
        // 查询所有正常状态的班级
        KgClass queryClass = new KgClass();
        queryClass.setStatus("0"); // 正常状态
        List<KgClass> classList = kgClassMapper.selectKgClassList(queryClass);

        int updateCount = 0;
        for (KgClass kgClass : classList) {
            int result = updateClassCurrentCount(kgClass.getClassId());
            if (result > 0) {
                updateCount++;
            }
        }

        return updateCount;
    }
}
