package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 班级课程时间对象 kg_class_schedule
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public class KgClassSchedule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 排班ID */
    private Long scheduleId;

    /** 班级ID，关联kg_class.class_id */
    @Excel(name = "班级ID，关联kg_class.class_id")
    private Long classId;

    /** 课程ID，关联kg_course.course_id */
    @Excel(name = "课程ID，关联kg_course.course_id")
    private Long courseId;

    /** 授课教师ID，关联kg_teacher.teacher_id */
    @Excel(name = "授课教师ID，关联kg_teacher.teacher_id")
    private Long teacherId;

    /** 星期几（1-7，1=周一） */
    @Excel(name = "星期几", readConverterExp = "1=-7，1=周一")
    private Long dayOfWeek;

    /** 开始时间（HH:mm:ss格式） */
    @Excel(name = "开始时间")
    private String startTime;

    /** 结束时间（HH:mm:ss格式） */
    @Excel(name = "结束时间")
    private String endTime;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 失效日期（NULL表示长期有效） */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期", readConverterExp = "N=ULL表示长期有效")
    private Date expiryDate;

    /** 是否启用（0禁用 1启用） */
    @Excel(name = "是否启用", readConverterExp = "0=禁用,1=启用")
    private Long isActive;

    /** 类型（regular常规、substitute代课、makeup补课） */
    @Excel(name = "类型", readConverterExp = "r=egular常规、substitute代课、makeup补课")
    private String scheduleType;

    /** 公司ID */
    @Excel(name = "公司ID")
    private String comId;

    public void setScheduleId(Long scheduleId) 
    {
        this.scheduleId = scheduleId;
    }

    public Long getScheduleId() 
    {
        return scheduleId;
    }
    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setDayOfWeek(Long dayOfWeek) 
    {
        this.dayOfWeek = dayOfWeek;
    }

    public Long getDayOfWeek() 
    {
        return dayOfWeek;
    }
    public void setStartTime(String startTime) 
    {
        this.startTime = startTime;
    }

    public String getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(String endTime) 
    {
        this.endTime = endTime;
    }

    public String getEndTime() 
    {
        return endTime;
    }

    public void setEffectiveDate(Date effectiveDate) 
    {
        this.effectiveDate = effectiveDate;
    }

    public Date getEffectiveDate() 
    {
        return effectiveDate;
    }
    public void setExpiryDate(Date expiryDate) 
    {
        this.expiryDate = expiryDate;
    }

    public Date getExpiryDate() 
    {
        return expiryDate;
    }
    public void setIsActive(Long isActive) 
    {
        this.isActive = isActive;
    }

    public Long getIsActive() 
    {
        return isActive;
    }
    public void setScheduleType(String scheduleType) 
    {
        this.scheduleType = scheduleType;
    }

    public String getScheduleType() 
    {
        return scheduleType;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("scheduleId", getScheduleId())
            .append("classId", getClassId())
            .append("courseId", getCourseId())
            .append("teacherId", getTeacherId())
            .append("dayOfWeek", getDayOfWeek())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("effectiveDate", getEffectiveDate())
            .append("expiryDate", getExpiryDate())
            .append("isActive", getIsActive())
            .append("scheduleType", getScheduleType())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
