package com.cl.project.business.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgClassSchedule;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.service.IKgClassScheduleService;
import com.cl.project.business.service.IKgStudentService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 班级课程时间Controller
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
@RequestMapping("/business/schedule")
public class KgClassScheduleController extends BaseController
{
    @Autowired
    private IKgClassScheduleService kgClassScheduleService;
    
    @Autowired
    private IKgStudentService kgStudentService;

    /**
     * 查询班级课程时间列表
     */
    @GetMapping("/list")
    public TableDataInfo list(KgClassSchedule kgClassSchedule)
    {
        startPage();
        List<KgClassSchedule> list = kgClassScheduleService.selectKgClassScheduleList(kgClassSchedule);
        return getDataTable(list);
    }

    /**
     * 导出班级课程时间列表
     */
    @Log(title = "班级课程时间", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgClassSchedule kgClassSchedule)
    {
        List<KgClassSchedule> list = kgClassScheduleService.selectKgClassScheduleList(kgClassSchedule);
        ExcelUtil<KgClassSchedule> util = new ExcelUtil<KgClassSchedule>(KgClassSchedule.class);
        return util.exportExcel(list, "schedule");
    }

    /**
     * 获取班级课程时间详细信息
     */
    @GetMapping(value = "/{scheduleId}")
    public AjaxResult getInfo(@PathVariable("scheduleId") Long scheduleId)
    {
        return AjaxResult.success(kgClassScheduleService.selectKgClassScheduleById(scheduleId));
    }

    /**
     * 新增班级课程时间
     */
    @Log(title = "班级课程时间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgClassSchedule kgClassSchedule)
    {
        return toAjax(kgClassScheduleService.insertKgClassSchedule(kgClassSchedule));
    }

    /**
     * 修改班级课程时间
     */
    @Log(title = "班级课程时间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgClassSchedule kgClassSchedule)
    {
        return toAjax(kgClassScheduleService.updateKgClassSchedule(kgClassSchedule));
    }

    /**
     * 删除班级课程时间
     */
    @Log(title = "班级课程时间", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scheduleIds}")
    public AjaxResult remove(@PathVariable Long[] scheduleIds)
    {
        return toAjax(kgClassScheduleService.deleteKgClassScheduleByIds(scheduleIds));
    }

    /**
     * 根据教师ID获取排班信息
     */
    @GetMapping("/teacher")
    public AjaxResult getTeacherSchedule(@RequestParam Long teacherId,
                                        @RequestParam(required = false) String startDate,
                                        @RequestParam(required = false) String endDate)
    {
        List<KgClassSchedule> list = kgClassScheduleService.selectScheduleByTeacherId(teacherId, startDate, endDate);
        return AjaxResult.success(list);
    }

    /**
     * 根据班级ID获取课表信息
     */
    @GetMapping("/class")
    public AjaxResult getClassSchedule(@RequestParam Long classId,
                                      @RequestParam(defaultValue = "current") String viewType)
    {
        List<KgClassSchedule> list = kgClassScheduleService.selectScheduleByClassId(classId, viewType);
        return AjaxResult.success(list);
    }

    /**
     * 检查排班时间冲突
     */
    @PostMapping("/checkConflict")
    public AjaxResult checkScheduleConflict(@RequestBody KgClassSchedule kgClassSchedule)
    {
        List<KgClassSchedule> conflicts = kgClassScheduleService.checkScheduleConflict(kgClassSchedule);
        return AjaxResult.success(conflicts);
    }

    /**
     * 获取排班统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getScheduleStats(@RequestParam(required = false) Long scheduleId,
                                      @RequestParam(required = false) Long teacherId,
                                      @RequestParam(required = false) Long classId)
    {
        java.util.Map<String, Object> stats = kgClassScheduleService.getScheduleStats(scheduleId, teacherId, classId);
        return AjaxResult.success(stats);
    }

    /**
     * 获取教师工作量统计
     */
    @GetMapping("/teacher/{teacherId}/workload")
    public AjaxResult getTeacherWorkload(@PathVariable Long teacherId,
                                        @RequestParam String startDate,
                                        @RequestParam String endDate)
    {
        java.util.Map<String, Object> workload = kgClassScheduleService.getTeacherWorkload(teacherId, startDate, endDate);
        return AjaxResult.success(workload);
    }

    /**
     * 获取班级课程安排统计
     */
    @GetMapping("/class/{classId}/stats")
    public AjaxResult getClassCourseStats(@PathVariable Long classId)
    {
        java.util.Map<String, Object> stats = kgClassScheduleService.getClassCourseStats(classId);
        return AjaxResult.success(stats);
    }

    /**
     * 获取可用教师列表
     */
    @GetMapping("/availableTeachers")
    public AjaxResult getAvailableTeachers(@RequestParam Long dayOfWeek,
                                          @RequestParam String startTime,
                                          @RequestParam String endTime,
                                          @RequestParam(required = false) Long excludeScheduleId)
    {
        List<Long> teacherIds = kgClassScheduleService.getAvailableTeachers(dayOfWeek, startTime, endTime, excludeScheduleId);
        return AjaxResult.success(teacherIds);
    }

    /**
     * 复制排班
     */
    @Log(title = "班级课程时间", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{scheduleId}")
    public AjaxResult copySchedule(@PathVariable Long scheduleId, @RequestBody List<String> targetDates)
    {
        int result = kgClassScheduleService.copySchedule(scheduleId, targetDates);
        return toAjax(result);
    }

    /**
     * 导入排班数据
     */
    @Log(title = "班级课程时间", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        // 这里需要使用ExcelUtil来解析Excel文件
        // 简化实现：返回错误信息
        return AjaxResult.error("导入功能尚未实现，需要集成ExcelUtil");
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        // 这里需要实现模板下载功能
        // ExcelUtil的使用
    }

    /**
     * 生成考勤模板
     */
    @PostMapping("/generateAttendance")
    public AjaxResult generateAttendanceTemplate(@RequestParam String startDate,
                                                @RequestParam String endDate,
                                                @RequestParam(required = false) Long classId,
                                                @RequestParam(required = false) Long teacherId)
    {
        int result = kgClassScheduleService.generateAttendanceTemplate(startDate, endDate, classId, teacherId);
        return toAjax(result);
    }

    /**
     * 生成考勤模板
     */
    @PostMapping("/generateAttendanceTemplate")
    public AjaxResult generateAttendanceTemplate(@RequestBody Map<String, Object> params)
    {
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        Long classId = params.get("classId") != null ? Long.valueOf(params.get("classId").toString()) : null;
        Long teacherId = params.get("teacherId") != null ? Long.valueOf(params.get("teacherId").toString()) : null;
        
        int result = kgClassScheduleService.generateAttendanceTemplate(startDate, endDate, classId, teacherId);
        return AjaxResult.success("成功生成" + result + "个考勤模板");
    }

    // ==================== 视图查询接口 ====================

    /**
     * 查询排班信息列表（含关联信息）
     */
    @GetMapping("/view/list")
    public TableDataInfo listView(KgClassSchedule kgClassSchedule, 
                                  @RequestParam(value = "noPaging", required = false, defaultValue = "false") Boolean noPaging)
    {
        // 如果不需要分页（如周视图），则不应用分页
        if (!noPaging) {
            startPage();
        }
        List<java.util.Map<String, Object>> list = kgClassScheduleService.selectScheduleViewList(kgClassSchedule);
        return getDataTable(list);
    }

    /**
     * 根据排班ID获取详细信息（含关联信息）
     */
    @GetMapping("/view/{scheduleId}")
    public AjaxResult getViewInfo(@PathVariable("scheduleId") Long scheduleId)
    {
        return AjaxResult.success(kgClassScheduleService.selectScheduleViewById(scheduleId));
    }

    /**
     * 根据教师ID查询排班（含关联信息）
     */
    @GetMapping("/view/teacher/{teacherId}")
    public AjaxResult getByTeacherView(@PathVariable Long teacherId)
    {
        List<java.util.Map<String, Object>> list = kgClassScheduleService.selectScheduleViewByTeacherId(teacherId);
        return AjaxResult.success(list);
    }

    /**
     * 根据班级ID查询课表（含关联信息）
     */
    @GetMapping("/view/class/{classId}")
    public AjaxResult getByClassView(@PathVariable Long classId)
    {
        List<java.util.Map<String, Object>> list = kgClassScheduleService.selectScheduleViewByClassId(classId);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取班级学生列表
     */
    @GetMapping("/class/{classId}/students")
    public AjaxResult getClassStudents(@PathVariable Long classId)
    {
        KgStudent queryParam = new KgStudent();
        queryParam.setClassId(classId);
        List<KgStudent> students = kgStudentService.selectKgStudentList(queryParam);
        return AjaxResult.success(students);
    }
    
    /**
     * 获取班级课程统计信息（兼容前端路径）
     */
    @GetMapping("/class-course-stats")
    public AjaxResult getClassCourseStatsCompat(@RequestParam Long classId)
    {
        java.util.Map<String, Object> stats = kgClassScheduleService.getClassCourseStats(classId);
        return AjaxResult.success(stats);
    }
    
    /**
     * 根据排班信息查询对应的考勤记录
     * 
     * @param scheduleId 排班ID
     * @param attendanceDate 考勤日期(可选，如果不提供则查询最近的考勤记录)
     * @return 考勤记录列表
     */
    @GetMapping("/attendance/{scheduleId}")
    public AjaxResult getAttendanceBySchedule(@PathVariable("scheduleId") Long scheduleId,
                                            @RequestParam(value = "attendanceDate", required = false) String attendanceDate) 
    {
        List<java.util.Map<String, Object>> attendanceList = kgClassScheduleService.getAttendanceBySchedule(scheduleId, attendanceDate);
        return AjaxResult.success(attendanceList);
    }

    /**
     * 根据排班信息查询对应的学生名单
     * 
     * @param scheduleId 排班ID  
     * @return 学生名单列表
     */
    @GetMapping("/students/{scheduleId}")
    public AjaxResult getStudentsBySchedule(@PathVariable("scheduleId") Long scheduleId) 
    {
        List<java.util.Map<String, Object>> studentList = kgClassScheduleService.getStudentsBySchedule(scheduleId);
        return AjaxResult.success(studentList);
    }
    
    /**
     * 根据排班生成考勤记录模板
     */
    @PostMapping("/generateAttendance/{scheduleId}")
    public AjaxResult generateAttendanceTemplate(@PathVariable Long scheduleId, @RequestParam String attendanceDate) {
        try {
            int result = kgClassScheduleService.generateAttendanceTemplate(scheduleId, attendanceDate);
            if (result > 0) {
                return AjaxResult.success("考勤记录生成成功，共生成" + result + "条记录");
            } else {
                return AjaxResult.error("没有找到需要生成考勤记录的学生");
            }
        } catch (Exception e) {
            return AjaxResult.error("生成考勤记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量生成考勤记录模板（按日期范围）
     */
    @PostMapping("/batchGenerateAttendance/{scheduleId}")
    public AjaxResult batchGenerateAttendanceTemplate(
            @PathVariable Long scheduleId, 
            @RequestParam String startDate, 
            @RequestParam String endDate) {
        try {
            int result = kgClassScheduleService.batchGenerateAttendanceTemplate(scheduleId, startDate, endDate);
            return AjaxResult.success("批量生成考勤记录成功，共生成" + result + "条记录");
        } catch (Exception e) {
            return AjaxResult.error("批量生成考勤记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 学生签到（更新考勤状态）
     */
    @PostMapping("/checkIn")
    public AjaxResult studentCheckIn(@RequestBody java.util.Map<String, Object> params) {
        try {
            Long attendanceId = Long.valueOf(params.get("attendanceId").toString());
            String checkInMethod = params.get("checkInMethod").toString(); // face/manual
            String attendanceStatus = params.getOrDefault("attendanceStatus", "present").toString();
            
            int result = kgClassScheduleService.studentCheckIn(attendanceId, checkInMethod, attendanceStatus);
            if (result > 0) {
                return AjaxResult.success("签到成功，请等待管理员确认");
            } else {
                return AjaxResult.error("签到失败，请重试");
            }
        } catch (Exception e) {
            return AjaxResult.error("签到失败：" + e.getMessage());
        }
    }
    
    /**
     * 管理员确认考勤
     */
    @PostMapping("/confirmAttendance")
    public AjaxResult confirmAttendance(@RequestBody java.util.Map<String, Object> params) {
        try {
            Long attendanceId = Long.valueOf(params.get("attendanceId").toString());
            Long confirmedBy = Long.valueOf(params.get("confirmedBy").toString());
            String finalStatus = params.getOrDefault("finalStatus", "present").toString();
            
            int result = kgClassScheduleService.confirmAttendance(attendanceId, confirmedBy, finalStatus);
            if (result > 0) {
                return AjaxResult.success("考勤确认成功");
            } else {
                return AjaxResult.error("确认失败，请重试");
            }
        } catch (Exception e) {
            return AjaxResult.error("确认失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量确认考勤
     */
    @PostMapping("/batchConfirmAttendance")
    public AjaxResult batchConfirmAttendance(@RequestBody java.util.Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            java.util.List<Long> attendanceIds = (java.util.List<Long>) params.get("attendanceIds");
            Long confirmedBy = Long.valueOf(params.get("confirmedBy").toString());
            
            int result = kgClassScheduleService.batchConfirmAttendance(attendanceIds, confirmedBy);
            return AjaxResult.success("批量确认成功，共确认" + result + "条记录");
        } catch (Exception e) {
            return AjaxResult.error("批量确认失败：" + e.getMessage());
        }
    }
    
    /**
     * 模板记录签到（更新现有记录）
     */
    @Log(title = "模板记录签到", businessType = BusinessType.UPDATE)
    @PostMapping("/templateCheckIn")
    public AjaxResult templateCheckIn(@RequestBody java.util.Map<String, Object> params) {
        try {
            Long attendanceId = Long.valueOf(params.get("attendanceId").toString());
            String attendanceStatus = params.getOrDefault("attendanceStatus", "present").toString();
            String checkInMethod = params.getOrDefault("checkInMethod", "manual").toString();
            
            int result = kgClassScheduleService.templateCheckIn(attendanceId, attendanceStatus, checkInMethod);
            if (result > 0) {
                return AjaxResult.success("签到成功");
            } else {
                return AjaxResult.error("签到失败，请重试");
            }
        } catch (Exception e) {
            return AjaxResult.error("签到失败：" + e.getMessage());
        }
    }
}
