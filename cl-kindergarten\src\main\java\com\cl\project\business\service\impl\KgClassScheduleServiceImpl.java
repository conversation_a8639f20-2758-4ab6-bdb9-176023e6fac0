package com.cl.project.business.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgClassScheduleMapper;
import com.cl.project.business.domain.KgClassSchedule;
import com.cl.project.business.service.IKgClassScheduleService;

/**
 * 班级课程时间Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
public class KgClassScheduleServiceImpl implements IKgClassScheduleService 
{
    @Autowired
    private KgClassScheduleMapper kgClassScheduleMapper;

    /**
     * 查询班级课程时间
     * 
     * @param scheduleId 班级课程时间ID
     * @return 班级课程时间
     */
    @Override
    public KgClassSchedule selectKgClassScheduleById(Long scheduleId)
    {
        return kgClassScheduleMapper.selectKgClassScheduleById(scheduleId);
    }

    /**
     * 查询班级课程时间列表
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 班级课程时间
     */
    @Override
    public List<KgClassSchedule> selectKgClassScheduleList(KgClassSchedule kgClassSchedule)
    {
        return kgClassScheduleMapper.selectKgClassScheduleList(kgClassSchedule);
    }

    /**
     * 新增班级课程时间
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 结果
     */
    @Override
    public int insertKgClassSchedule(KgClassSchedule kgClassSchedule)
    {
        kgClassSchedule.setCreateTime(DateUtils.getNowDate());
        return kgClassScheduleMapper.insertKgClassSchedule(kgClassSchedule);
    }

    /**
     * 修改班级课程时间
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 结果
     */
    @Override
    public int updateKgClassSchedule(KgClassSchedule kgClassSchedule)
    {
        kgClassSchedule.setUpdateTime(DateUtils.getNowDate());
        return kgClassScheduleMapper.updateKgClassSchedule(kgClassSchedule);
    }

    /**
     * 批量删除班级课程时间
     * 
     * @param scheduleIds 需要删除的班级课程时间ID
     * @return 结果
     */
    @Override
    public int deleteKgClassScheduleByIds(Long[] scheduleIds)
    {
        return kgClassScheduleMapper.deleteKgClassScheduleByIds(scheduleIds);
    }

    /**
     * 删除班级课程时间信息
     * 
     * @param scheduleId 班级课程时间ID
     * @return 结果
     */
    @Override
    public int deleteKgClassScheduleById(Long scheduleId)
    {
        return kgClassScheduleMapper.deleteKgClassScheduleById(scheduleId);
    }

    // ==================== 扩展方法实现 ====================

    /**
     * 根据教师ID查询排班信息
     */
    @Override
    public List<KgClassSchedule> selectScheduleByTeacherId(Long teacherId, String startDate, String endDate)
    {
        KgClassSchedule queryParam = new KgClassSchedule();
        queryParam.setTeacherId(teacherId);
        queryParam.setIsActive(1L); // 只查询启用的排班
        
        // 如果有日期参数，可以在mapper中添加日期过滤逻辑
        List<KgClassSchedule> list = kgClassScheduleMapper.selectKgClassScheduleList(queryParam);
        
        // 这里可以添加日期过滤逻辑或在mapper层实现
        return list;
    }

    /**
     * 根据班级ID查询课表信息
     */
    @Override
    public List<KgClassSchedule> selectScheduleByClassId(Long classId, String viewType)
    {
        KgClassSchedule queryParam = new KgClassSchedule();
        queryParam.setClassId(classId);
        
        if ("current".equals(viewType)) {
            queryParam.setIsActive(1L); // 只查询当前有效的
        }
        
        return kgClassScheduleMapper.selectKgClassScheduleList(queryParam);
    }

    /**
     * 检查排班时间冲突
     */
    @Override
    public List<KgClassSchedule> checkScheduleConflict(KgClassSchedule kgClassSchedule)
    {
        // 检查教师时间冲突
        KgClassSchedule queryParam = new KgClassSchedule();
        queryParam.setTeacherId(kgClassSchedule.getTeacherId());
        queryParam.setDayOfWeek(kgClassSchedule.getDayOfWeek());
        queryParam.setIsActive(1L);
        
        List<KgClassSchedule> existingSchedules = kgClassScheduleMapper.selectKgClassScheduleList(queryParam);
        List<KgClassSchedule> conflicts = new ArrayList<>();
        
        // 简单的时间冲突检查逻辑（实际项目中可能需要更复杂的逻辑）
        for (KgClassSchedule existing : existingSchedules) {
            // 如果是修改操作，排除自己
            if (kgClassSchedule.getScheduleId() != null && 
                kgClassSchedule.getScheduleId().equals(existing.getScheduleId())) {
                continue;
            }
            
            // 检查时间重叠（这里需要根据实际的startTime和endTime字段进行判断）
            // 简化处理：如果在同一天、同一教师，就认为可能冲突
            conflicts.add(existing);
        }
        
        return conflicts;
    }

    /**
     * 获取排班统计信息
     */
    @Override
    public Map<String, Object> getScheduleStats(Long scheduleId, Long teacherId, Long classId)
    {
        Map<String, Object> stats = new HashMap<>();
        
        KgClassSchedule queryParam = new KgClassSchedule();
        if (teacherId != null) queryParam.setTeacherId(teacherId);
        if (classId != null) queryParam.setClassId(classId);
        queryParam.setIsActive(1L);
        
        List<KgClassSchedule> schedules = kgClassScheduleMapper.selectKgClassScheduleList(queryParam);
        
        stats.put("totalCount", schedules.size());
        stats.put("activeCount", schedules.size()); // 已经过滤了active的
        stats.put("weeklyHours", schedules.size() * 1); // 假设每节课1小时
        
        return stats;
    }

    /**
     * 获取教师工作量统计
     */
    @Override
    public Map<String, Object> getTeacherWorkload(Long teacherId, String startDate, String endDate)
    {
        Map<String, Object> workload = new HashMap<>();
        
        KgClassSchedule queryParam = new KgClassSchedule();
        queryParam.setTeacherId(teacherId);
        queryParam.setIsActive(1L);
        
        List<KgClassSchedule> schedules = kgClassScheduleMapper.selectKgClassScheduleList(queryParam);
        
        workload.put("weeklyClasses", schedules.size());
        workload.put("weeklyHours", schedules.size() * 1); // 假设每节课1小时
        workload.put("classCount", schedules.stream().mapToLong(KgClassSchedule::getClassId).distinct().count());
        workload.put("courseCount", schedules.stream().mapToLong(KgClassSchedule::getCourseId).distinct().count());
        
        return workload;
    }

    /**
     * 获取班级课程安排统计
     */
    @Override
    public Map<String, Object> getClassCourseStats(Long classId)
    {
        Map<String, Object> stats = new HashMap<>();
        
        KgClassSchedule queryParam = new KgClassSchedule();
        queryParam.setClassId(classId);
        queryParam.setIsActive(1L);
        
        List<KgClassSchedule> schedules = kgClassScheduleMapper.selectKgClassScheduleList(queryParam);
        
        stats.put("totalClasses", schedules.size());
        stats.put("courseCount", schedules.stream().mapToLong(KgClassSchedule::getCourseId).distinct().count());
        stats.put("teacherCount", schedules.stream().mapToLong(KgClassSchedule::getTeacherId).distinct().count());
        
        return stats;
    }

    /**
     * 获取某个时间段可用的教师列表
     */
    @Override
    public List<Long> getAvailableTeachers(Long dayOfWeek, String startTime, String endTime, Long excludeScheduleId)
    {
        // 这个方法需要结合teacher表来实现
        // 简化实现：返回空列表，实际项目中需要查询所有教师然后排除有冲突的
        return new ArrayList<>();
    }

    /**
     * 复制排班到其他时间
     */
    @Override
    public int copySchedule(Long sourceScheduleId, List<String> targetDates)
    {
        KgClassSchedule sourceSchedule = kgClassScheduleMapper.selectKgClassScheduleById(sourceScheduleId);
        if (sourceSchedule == null) {
            return 0;
        }
        
        int count = 0;
        for (String targetDate : targetDates) {
            KgClassSchedule newSchedule = new KgClassSchedule();
            // 复制基本信息
            newSchedule.setClassId(sourceSchedule.getClassId());
            newSchedule.setCourseId(sourceSchedule.getCourseId());
            newSchedule.setTeacherId(sourceSchedule.getTeacherId());
            newSchedule.setDayOfWeek(sourceSchedule.getDayOfWeek());
            newSchedule.setStartTime(sourceSchedule.getStartTime());
            newSchedule.setEndTime(sourceSchedule.getEndTime());
            newSchedule.setIsActive(1L);
            newSchedule.setScheduleType("regular");
            newSchedule.setComId(sourceSchedule.getComId());
            newSchedule.setCreateTime(DateUtils.getNowDate());
            
            // 设置新的生效日期
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                newSchedule.setEffectiveDate(sdf.parse(targetDate));
            } catch (Exception e) {
                continue; // 跳过无效日期
            }
            
            count += kgClassScheduleMapper.insertKgClassSchedule(newSchedule);
        }
        
        return count;
    }

    /**
     * 批量导入排班
     */
    @Override
    public String importSchedule(List<KgClassSchedule> scheduleList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(scheduleList) || scheduleList.size() == 0)
        {
            return "导入排班数据不能为空！";
        }
        
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (KgClassSchedule schedule : scheduleList)
        {
            try
            {
                // 验证必要字段
                if (schedule.getClassId() == null || schedule.getCourseId() == null || 
                    schedule.getTeacherId() == null || schedule.getDayOfWeek() == null)
                {
                    failureNum++;
                    failureMsg.append("<br/>排班信息不完整");
                    continue;
                }
                
                // 检查是否存在
                KgClassSchedule existSchedule = null;
                if (schedule.getScheduleId() != null)
                {
                    existSchedule = kgClassScheduleMapper.selectKgClassScheduleById(schedule.getScheduleId());
                }
                
                if (StringUtils.isNull(existSchedule))
                {
                    schedule.setCreateBy(operName);
                    schedule.setCreateTime(DateUtils.getNowDate());
                    this.insertKgClassSchedule(schedule);
                    successNum++;
                    successMsg.append("<br/>" + "排班导入成功");
                }
                else if (isUpdateSupport)
                {
                    schedule.setUpdateBy(operName);
                    schedule.setUpdateTime(DateUtils.getNowDate());
                    this.updateKgClassSchedule(schedule);
                    successNum++;
                    successMsg.append("<br/>" + "排班更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + "排班已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>导入失败：" + e.getMessage();
                failureMsg.append(msg);
            }
        }
        
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            return failureMsg.toString();
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    /**
     * 生成考勤模板（基于排班）
     */
    @Override
    public int generateAttendanceTemplate(String startDate, String endDate, Long classId, Long teacherId)
    {
        // 这个方法需要结合考勤表来实现
        // 简化实现：根据排班信息生成考勤记录
        KgClassSchedule queryParam = new KgClassSchedule();
        if (classId != null) queryParam.setClassId(classId);
        if (teacherId != null) queryParam.setTeacherId(teacherId);
        queryParam.setIsActive(1L);
        
        List<KgClassSchedule> schedules = kgClassScheduleMapper.selectKgClassScheduleList(queryParam);
        // 这里应该调用考勤服务来生成考勤模板
        // 返回生成的记录数
        return schedules.size();
    }

    // ==================== 视图查询方法实现 ====================

    /**
     * 从视图查询排班信息列表（包含关联数据）
     */
    @Override
    public List<java.util.Map<String, Object>> selectScheduleViewList(KgClassSchedule kgClassSchedule) {
        return kgClassScheduleMapper.selectScheduleViewList(kgClassSchedule);
    }

    /**
     * 从视图查询单个排班信息（包含关联数据）
     */
    @Override
    public java.util.Map<String, Object> selectScheduleViewById(Long scheduleId) {
        return kgClassScheduleMapper.selectScheduleViewById(scheduleId);
    }

    /**
     * 根据教师ID从视图查询排班
     */
    @Override
    public List<java.util.Map<String, Object>> selectScheduleViewByTeacherId(Long teacherId) {
        return kgClassScheduleMapper.selectScheduleViewByTeacherId(teacherId);
    }

    /**
     * 根据班级ID从视图查询课表
     */
    @Override
    public List<java.util.Map<String, Object>> selectScheduleViewByClassId(Long classId) {
        return kgClassScheduleMapper.selectScheduleViewByClassId(classId);
    }
    
    /**
     * 根据排班信息查询对应的考勤记录
     */
    @Override
    public List<java.util.Map<String, Object>> getAttendanceBySchedule(Long scheduleId, String attendanceDate) {
        return kgClassScheduleMapper.getAttendanceBySchedule(scheduleId, attendanceDate);
    }
    
    /**
     * 根据排班信息查询对应的学生名单
     */
    @Override
    public List<java.util.Map<String, Object>> getStudentsBySchedule(Long scheduleId) {
        return kgClassScheduleMapper.getStudentsBySchedule(scheduleId);
    }
    
    /**
     * 根据排班生成考勤记录模板
     */
    @Override
    public int generateAttendanceTemplate(Long scheduleId, String attendanceDate) {
        return kgClassScheduleMapper.generateAttendanceTemplate(scheduleId, attendanceDate);
    }
    
    /**
     * 批量生成考勤记录模板
     */
    @Override
    public int batchGenerateAttendanceTemplate(Long scheduleId, String startDate, String endDate) {
        return kgClassScheduleMapper.batchGenerateAttendanceTemplate(scheduleId, startDate, endDate);
    }
    
    /**
     * 学生签到
     */
    @Override
    public int studentCheckIn(Long attendanceId, String checkInMethod, String attendanceStatus) {
        return kgClassScheduleMapper.studentCheckIn(attendanceId, checkInMethod, attendanceStatus);
    }
    
    /**
     * 管理员确认考勤
     */
    @Override
    public int confirmAttendance(Long attendanceId, Long confirmedBy, String finalStatus) {
        return kgClassScheduleMapper.confirmAttendance(attendanceId, confirmedBy, finalStatus);
    }
    
    /**
     * 批量确认考勤
     */
    @Override
    public int batchConfirmAttendance(List<Long> attendanceIds, Long confirmedBy) {
        return kgClassScheduleMapper.batchConfirmAttendance(attendanceIds, confirmedBy);
    }
    
    @Override
    public int templateCheckIn(Long attendanceId, String attendanceStatus, String checkInMethod) {
        return kgClassScheduleMapper.templateCheckIn(attendanceId, attendanceStatus, checkInMethod);
    }
}
