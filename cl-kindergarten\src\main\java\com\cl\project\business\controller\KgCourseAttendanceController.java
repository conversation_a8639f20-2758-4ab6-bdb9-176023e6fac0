package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgCourseAttendance;
import com.cl.project.business.service.IKgCourseAttendanceService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 托管考勤记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/course-attendance")
public class KgCourseAttendanceController extends BaseController
{
    @Autowired
    private IKgCourseAttendanceService kgCourseAttendanceService;

    /**
     * 查询托管考勤记录列表
     */
    @SaCheckPermission("kg:course:attendance:list")
    @GetMapping("/list")
    public TableDataInfo list(KgCourseAttendance kgCourseAttendance)
    {
        startPage();
        List<KgCourseAttendance> list = kgCourseAttendanceService.selectKgCourseAttendanceList(kgCourseAttendance);
        return getDataTable(list);
    }

    /**
     * 导出托管考勤记录列表
     */
    @SaCheckPermission("kg:course:attendance:query")
    @Log(title = "托管考勤记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgCourseAttendance kgCourseAttendance)
    {
        List<KgCourseAttendance> list = kgCourseAttendanceService.selectKgCourseAttendanceList(kgCourseAttendance);
        ExcelUtil<KgCourseAttendance> util = new ExcelUtil<KgCourseAttendance>(KgCourseAttendance.class);
        return util.exportExcel(list, "attendance");
    }

    /**
     * 获取托管考勤记录详细信息
     */
    @SaCheckPermission("kg:course:attendance:query")
    @GetMapping(value = "/{attendanceId}")
    public AjaxResult getInfo(@PathVariable("attendanceId") Long attendanceId)
    {
        return AjaxResult.success(kgCourseAttendanceService.selectKgCourseAttendanceById(attendanceId));
    }

    /**
     * 新增托管考勤记录
     */
    @SaCheckPermission("kg:course:attendance:checkin")
    @Log(title = "托管考勤记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgCourseAttendance kgCourseAttendance)
    {
        return toAjax(kgCourseAttendanceService.insertKgCourseAttendance(kgCourseAttendance));
    }

    /**
     * 托管考勤签到
     */
    // @SaCheckPermission("kg:course:attendance:checkin")
    @Log(title = "托管考勤签到", businessType = BusinessType.INSERT)
    @PostMapping("/checkin")
    public AjaxResult checkin(@RequestBody KgCourseAttendance kgCourseAttendance)
    {
        // 签到时设置为未确认状态和手动记录类型
        kgCourseAttendance.setIsConfirmed(0L);
        kgCourseAttendance.setConfirmedBy(null);
        kgCourseAttendance.setConfirmedTime(null);
        return toAjax(kgCourseAttendanceService.insertKgCourseAttendance(kgCourseAttendance));
    }

    /**
     * 修改托管考勤记录
     */
    @SaCheckPermission("kg:course:attendance:confirm")
    @Log(title = "托管考勤记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgCourseAttendance kgCourseAttendance)
    {
        return toAjax(kgCourseAttendanceService.updateKgCourseAttendance(kgCourseAttendance));
    }

    /**
     * 删除托管考勤记录
     */
    @SaCheckPermission("kg:course:attendance:checkin")
    @Log(title = "托管考勤记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attendanceIds}")
    public AjaxResult remove(@PathVariable Long[] attendanceIds)
    {
        return toAjax(kgCourseAttendanceService.deleteKgCourseAttendanceByIds(attendanceIds));
    }

    /**
     * 确认单个考勤记录
     */
    // @SaCheckPermission("kg:course:attendance:confirm")
    @Log(title = "托管考勤记录", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{attendanceId}")
    public AjaxResult confirmAttendance(@PathVariable Long attendanceId)
    {
        Long confirmedBy = getUserId(); // 获取当前登录用户ID
        return toAjax(kgCourseAttendanceService.confirmCourseAttendance(attendanceId, confirmedBy));
    }

    /**
     * 批量确认考勤记录
     */
    // @SaCheckPermission("kg:course:attendance:confirm")
    @Log(title = "托管考勤记录", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public AjaxResult batchConfirmAttendance(@RequestBody List<Long> attendanceIds)
    {
        Long confirmedBy = getUserId(); // 获取当前登录用户ID
        return toAjax(kgCourseAttendanceService.batchConfirmCourseAttendance(attendanceIds, confirmedBy));
    }
}
