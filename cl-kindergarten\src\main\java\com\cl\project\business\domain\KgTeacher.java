package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 教师信息对象 kg_teacher
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgTeacher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 教师ID */
    private Long teacherId;

    /** 关联用户ID，关联sys_user.user_id */
    @Excel(name = "关联用户ID，关联sys_user.user_id")
    private Long userId;

    /** 教师编号 */
    @Excel(name = "教师编号")
    private String teacherCode;

    /** 教师姓名 */
    @Excel(name = "教师姓名")
    private String teacherName;

    /** 性别（0男 1女） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String gender;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 学历 */
    @Excel(name = "学历")
    private String education;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date hireDate;

    /** 职位 */
    @Excel(name = "职位")
    private String position;

    /** 基本工资 */
    @Excel(name = "基本工资")
    private BigDecimal baseSalary;

    /** 人脸识别ID，用于刷脸打卡 */
    @Excel(name = "人脸识别ID，用于刷脸打卡")
    private String faceId;

    /** 绑定的微信openid */
    @Excel(name = "绑定的微信openid")
    private String wechatOpenid;

    /** 钉钉用户ID */
    @Excel(name = "钉钉用户ID")
    private String dingtalkUserId;

    /** 头像URL */
    @Excel(name = "头像URL")
    private String avatar;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 工号 */
    @Excel(name = "工号")
    private String jobNumber;

    /** 是否已绑定微信（0否 1是） */
    @Excel(name = "是否已绑定微信", readConverterExp = "0=否,1=是")
    private Long isWechatBound;

    /** 微信绑定时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "微信绑定时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date wechatBindTime;

    /** 状态（0在职 1离职） */
    @Excel(name = "状态", readConverterExp = "0=在职,1=离职")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;
    /** 工资ID  显示使用*/
    private Long salaryId;

    public Long getSalaryId() {
        return salaryId;
    }
    public void setSalaryId(Long salary ) {
        this.salaryId = salary;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setTeacherCode(String teacherCode) 
    {
        this.teacherCode = teacherCode;
    }

    public String getTeacherCode() 
    {
        return teacherCode;
    }
    public void setTeacherName(String teacherName) 
    {
        this.teacherName = teacherName;
    }

    public String getTeacherName() 
    {
        return teacherName;
    }
    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }
    public void setMajor(String major) 
    {
        this.major = major;
    }

    public String getMajor() 
    {
        return major;
    }
    public void setHireDate(Date hireDate) 
    {
        this.hireDate = hireDate;
    }

    public Date getHireDate() 
    {
        return hireDate;
    }
    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }
    public void setBaseSalary(BigDecimal baseSalary) 
    {
        this.baseSalary = baseSalary;
    }

    public BigDecimal getBaseSalary() 
    {
        return baseSalary;
    }
    public void setFaceId(String faceId) 
    {
        this.faceId = faceId;
    }

    public String getFaceId() 
    {
        return faceId;
    }
    public void setWechatOpenid(String wechatOpenid) 
    {
        this.wechatOpenid = wechatOpenid;
    }

    public String getWechatOpenid() 
    {
        return wechatOpenid;
    }
    
    public void setDingtalkUserId(String dingtalkUserId) 
    {
        this.dingtalkUserId = dingtalkUserId;
    }

    public String getDingtalkUserId() 
    {
        return dingtalkUserId;
    }
    
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }
    
    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }
    
    public void setJobNumber(String jobNumber) 
    {
        this.jobNumber = jobNumber;
    }

    public String getJobNumber() 
    {
        return jobNumber;
    }
    
    public void setIsWechatBound(Long isWechatBound) 
    {
        this.isWechatBound = isWechatBound;
    }

    public Long getIsWechatBound() 
    {
        return isWechatBound;
    }
    public void setWechatBindTime(Date wechatBindTime) 
    {
        this.wechatBindTime = wechatBindTime;
    }

    public Date getWechatBindTime() 
    {
        return wechatBindTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teacherId", getTeacherId())
            .append("userId", getUserId())
            .append("teacherCode", getTeacherCode())
            .append("teacherName", getTeacherName())
            .append("gender", getGender())
            .append("phone", getPhone())
            .append("idCard", getIdCard())
            .append("education", getEducation())
            .append("major", getMajor())
            .append("hireDate", getHireDate())
            .append("position", getPosition())
            .append("baseSalary", getBaseSalary())
            .append("faceId", getFaceId())
            .append("wechatOpenid", getWechatOpenid())
            .append("isWechatBound", getIsWechatBound())
            .append("wechatBindTime", getWechatBindTime())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
