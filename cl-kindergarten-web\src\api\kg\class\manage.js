import request from '@/utils/request'

// 查询班级列表
export function listClass(query) {
  return request({
    url: '/business/class/list',
    method: 'get',
    params: query
  })
}

// 查询班级详细
export function getClass(classId) {
  return request({
    url: '/business/class/' + classId,
    method: 'get'
  })
}

// 新增班级
export function addClass(data) {
  return request({
    url: '/business/class',
    method: 'post',
    data: data
  })
}

// 修改班级
export function updateClass(data) {
  return request({
    url: '/business/class',
    method: 'put',
    data: data
  })
}

// 删除班级
export function delClass(classId) {
  return request({
    url: '/business/class/' + classId,
    method: 'delete'
  })
}

// 导出班级
export function exportClass(query) {
  return request({
    url: '/business/class/export',
    method: 'get',
    params: query
  })
}

// 获取所有班级选项（用于下拉框）
export function getClassOptions() {
  return request({
    url: '/business/class/options',
    method: 'get'
  })
}

// 获取所有班级列表（用于选择）
export function listAllClass() {
  return request({
    url: '/business/class/listAll',
    method: 'get'
  })
}


export function listByCourseWithSchedule(params) {
  return request({
    url: '/business/class/listByCourseWithSchedule',
    method: 'get',
    params
  });
}