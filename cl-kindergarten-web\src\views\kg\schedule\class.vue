<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>班级课表查看</h2>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-printer" @click="handlePrint">打印课表</el-button>
        <el-button type="success" icon="el-icon-download" @click="handleExport">导出课表</el-button>
      </div>
    </div>

    <!-- 班级选择 -->
    <el-card class="class-select-card">
      <el-form :inline="true">
        <el-form-item label="选择班级：">
          <el-select v-model="selectedClassId" placeholder="请选择班级" @change="loadClassSchedule">
            <el-option
              v-for="classItem in classList"
              :key="classItem.classId"
              :label="classItem.className"
              :value="classItem.classId">
              <span style="float: left">{{ classItem.className }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ classItem.studentCount }}人</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="查看类型：">
          <el-radio-group v-model="viewType" @change="loadClassSchedule">
            <el-radio label="current">当前课表</el-radio>
            <!-- <el-radio label="history">历史课表</el-radio> -->
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 班级信息卡片 -->
    <el-card v-if="selectedClass" class="class-info-card">
      <div class="class-info">
        <div class="class-icon">
          <i class="el-icon-school" style="font-size: 40px; color: #409eff;"></i>
        </div>
        <div class="class-details">
          <h3>{{ selectedClass.className }}</h3>
          <p><i class="el-icon-user"></i> 学生人数：{{ selectedClass.studentCount }}人</p>
          <p><i class="el-icon-date"></i> 开班时间：{{ selectedClass.createTime }}</p>
          <p><i class="el-icon-office-building"></i> 教室位置：{{ selectedClass.classroom ? selectedClass.classroom : '暂未分配' }}</p>
        </div>
        <div class="class-stats">
          <div class="stat-item">
            <div class="stat-value">{{ weeklyHours }}</div>
            <div class="stat-label">本周课时(节)</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ teacherCount }}</div>
            <div class="stat-label">任课教师(人)</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ courseCount }}</div>
            <div class="stat-label">开设课程(门)</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 课表显示 -->
    <el-card v-if="selectedClassId" class="schedule-display-card">
      <div slot="header" class="card-header">
        <span>{{ selectedClass && selectedClass.className ? selectedClass.className + '课程表' : '班级课程表' }}</span>
        <div class="header-tools">
          <el-button-group>
            <el-button 
              :type="displayMode === 'table' ? 'primary' : 'default'" 
              size="small" 
              @click="displayMode = 'table'">
              <i class="el-icon-menu"></i> 表格视图
            </el-button>
            <el-button 
              :type="displayMode === 'timeline' ? 'primary' : 'default'" 
              size="small" 
              @click="displayMode = 'timeline'">
              <i class="el-icon-time"></i> 时间轴
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-if="displayMode === 'table'" class="table-view">
        <table class="class-schedule-table">
          <thead>
            <tr>
              <th class="time-header">时间</th>
              <th v-for="day in weekDays" :key="day.value" class="day-header">
                {{ day.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="timeSlot in timeSlots" :key="timeSlot.value">
              <td class="time-cell">
                <div class="time-info">
                  <div class="time-label">{{ timeSlot.label }}</div>
                  <div class="time-period">{{ timeSlot.period }}</div>
                </div>
              </td>
              <td v-for="day in weekDays" :key="day.value" class="schedule-cell">
                <div 
                  v-for="schedule in getClassScheduleByDayTime(day.value, timeSlot.value)" 
                  :key="schedule.scheduleId"
                  class="class-schedule-item"
                  :class="[getClassScheduleClass(schedule), getCourseTypeClass(schedule)]"
                  @click="viewScheduleDetail(schedule)">
                  
                  <div class="course-info">
                    <div class="course-name">{{ formatScheduleInfo(schedule).courseName }}</div>
                    <div class="teacher-name">
                      <i class="el-icon-user-solid"></i> {{ formatScheduleInfo(schedule).teacherName }}
                    </div>
                    <div class="time-range">{{ formatScheduleInfo(schedule).timeRange }}</div>
                  </div>
                </div>
                <div v-if="!getClassScheduleByDayTime(day.value, timeSlot.value).length" class="empty-schedule">
                  <i class="el-icon-plus" style="color: #ddd;"></i>
                  <div style="margin-top: 4px; font-size: 12px;">无课程</div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 时间轴视图 -->
      <div v-if="displayMode === 'timeline'" class="timeline-view">
        <el-timeline>
          <el-timeline-item
            v-for="schedule in sortedScheduleList"
            :key="schedule.scheduleId"
            :timestamp="schedule.fullTimeDisplay"
            placement="top">
            <el-card class="timeline-card">
              <div class="timeline-content">
                <div class="timeline-header">
                  <h4>{{ schedule.courseName }}</h4>
                  <el-tag v-if="schedule.scheduleType === 'regular'" type="success" size="small">常规课程</el-tag>
                  <el-tag v-else-if="schedule.scheduleType === 'substitute'" type="warning" size="small">代课</el-tag>
                  <el-tag v-else-if="schedule.scheduleType === 'makeup'" type="info" size="small">补课</el-tag>
                </div>
                <div class="timeline-body">
                  <p><i class="el-icon-user-solid"></i> 任课教师：{{ schedule.teacherName }}</p>
                  <p><i class="el-icon-time"></i> 上课时间：{{ schedule.timeRange }}</p>
                  <p><i class="el-icon-calendar"></i> 星期：{{ schedule.dayName }}</p>
                  <p v-if="schedule.remark"><i class="el-icon-document"></i> 备注：{{ schedule.remark }}</p>
                </div>
                <div class="timeline-actions">
                  <el-button type="text" size="small" @click="viewStudentList(schedule)">
                    <i class="el-icon-user"></i> 学生名单
                  </el-button>
                  <el-button type="text" size="small" @click="viewAttendance(schedule)">
                    <i class="el-icon-s-check"></i> 考勤记录
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 学生列表 -->
    <el-card v-if="selectedClassId" class="student-list-card">
      <div slot="header">
        <span>班级学生名单</span>
        <el-button type="text" style="float: right;" @click="refreshStudentList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      <div class="student-grid">
        <div 
          v-for="student in studentList" 
          :key="student.studentId" 
          class="student-card"
          @click="viewStudentDetail(student)">
          <el-avatar :size="50" :src="student.avatar">
            {{ student.studentName.charAt(0) }}
          </el-avatar>
          <div class="student-info">
            <div class="student-name">{{ student.studentName }}</div>
            <div class="student-status">
              <el-tag v-if="student.status === 'active'" type="success" size="mini">在读</el-tag>
              <el-tag v-else-if="student.status === 'leave'" type="warning" size="mini">请假</el-tag>
              <el-tag v-else type="info" size="mini">暂停</el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 课程详情对话框 -->
    <el-dialog title="课程详情" :visible.sync="scheduleDetailVisible" width="600px">
      <div v-if="currentScheduleDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课程名称">{{ currentScheduleDetail.courseName }}</el-descriptions-item>
          <el-descriptions-item label="任课教师">{{ currentScheduleDetail.teacherName }}</el-descriptions-item>
          <el-descriptions-item label="上课时间">{{ currentScheduleDetail.timeRange }}</el-descriptions-item>
          <el-descriptions-item label="星期">{{ currentScheduleDetail.dayName }}</el-descriptions-item>
          <el-descriptions-item label="课程类型">
            <el-tag v-if="currentScheduleDetail.scheduleType === 'regular'" type="success">常规课程</el-tag>
            <el-tag v-else-if="currentScheduleDetail.scheduleType === 'substitute'" type="warning">代课</el-tag>
            <el-tag v-else-if="currentScheduleDetail.scheduleType === 'makeup'" type="info">补课</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="生效日期">{{ currentScheduleDetail.effectiveDate }}</el-descriptions-item>
          <el-descriptions-item label="课程描述" :span="2">{{ currentScheduleDetail.courseDescription ? currentScheduleDetail.courseDescription : '暂无描述' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentScheduleDetail.remark ? currentScheduleDetail.remark : '无' }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <el-button type="primary" @click="viewAttendance(currentScheduleDetail)">
            <i class="el-icon-s-check"></i> 查看考勤
          </el-button>
          <el-button type="success" @click="viewStudentList(currentScheduleDetail)">
            <i class="el-icon-user"></i> 学生名单
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 学生详情对话框 -->
    <el-dialog title="学生详情" :visible.sync="studentDetailVisible" width="500px">
      <div v-if="currentStudentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ currentStudentDetail.studentName }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ currentStudentDetail.gender === 'M' ? '男' : '女' }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ currentStudentDetail.age }}岁</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="currentStudentDetail.status === 'active'" type="success">在读</el-tag>
            <el-tag v-else-if="currentStudentDetail.status === 'leave'" type="warning">请假</el-tag>
            <el-tag v-else type="info">暂停</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="联系电话" :span="2">{{ currentStudentDetail.phone ? currentStudentDetail.phone : '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="家长姓名" :span="2">{{ currentStudentDetail.parentName ? currentStudentDetail.parentName : '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="入学时间" :span="2">{{ currentStudentDetail.enrollmentDate }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 考勤记录对话框 -->
    <el-dialog title="考勤记录" :visible.sync="attendanceDialogVisible" width="80%" @close="closeAttendanceDialog">
      <div slot="title">
        <span>考勤记录 - {{ currentViewSchedule ? currentViewSchedule.courseName : '' }} ({{ currentViewSchedule ? currentViewSchedule.className : '' }})</span>
      </div>
      
      <!-- 查询表单 -->
      <el-form :inline="true" :model="attendanceQuery" class="demo-form-inline">
        <el-form-item label="考勤日期:">
          <el-date-picker
            v-model="attendanceQuery.attendanceDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            @change="handleAttendanceDateChange"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadAttendanceData()">查询</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 考勤数据表格 -->
      <el-table :data="attendanceList" border style="width: 100%" max-height="400">
        <el-table-column prop="studentName" label="学生姓名" width="120"/>
        <el-table-column prop="attendanceDate" label="考勤日期" width="120"/>
        <el-table-column label="开始时间" width="100" align="center">
          <template slot-scope="scope">
            {{ formatTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" width="100" align="center">
          <template slot-scope="scope">
            {{ formatTime(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column label="考勤状态" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getAttendanceStatusType(scope.row.attendanceStatus, scope.row.isConfirmed)">
              {{ scope.row.statusDisplay }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="确认时间" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.confirmedTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="checkInMethod" label="签到方式" width="100" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.checkInMethod === 'face'" type="success">人脸</el-tag>
            <el-tag v-else-if="scope.row.checkInMethod === 'manual'" type="primary">手动</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="studentPhone" label="联系电话" width="130"/>
        <el-table-column label="确认状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isConfirmed ? 'success' : 'info'">
              {{ scope.row.isConfirmed ? '已确认' : '待确认' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeAttendanceDialog()">关闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 学生名单对话框 -->
    <el-dialog title="学生名单" :visible.sync="studentsDialogVisible" width="70%" @close="closeStudentsDialog">
      <div slot="title">
        <span>学生名单 - {{ currentViewSchedule ? currentViewSchedule.courseName : '' }} ({{ currentViewSchedule ? currentViewSchedule.className : '' }})</span>
      </div>
      
      <!-- 学生数据表格 -->
      <el-table :data="studentsList" border style="width: 100%" max-height="500">
        <el-table-column type="index" label="序号" width="60" align="center"/>
        <el-table-column prop="studentName" label="学生姓名" width="120"/>
        <el-table-column label="性别" align="center" prop="gender">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.gender"/>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="学生电话" width="130"/>
        <el-table-column prop="parentName" label="家长姓名" width="120"/>
        <el-table-column prop="parentPhone" label="家长电话" width="130"/>
        <el-table-column prop="enrollmentDate" label="报名日期" />
        <el-table-column label="课时情况">
          <template slot-scope="scope">
            <div>
              <span>总课时：{{ scope.row.totalSessions }}</span><br>
              <span>已用：{{ scope.row.usedSessions }}</span><br>
              <span>剩余：{{ scope.row.remainingSessions }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="报名状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enrollmentStatus === 'active' ? 'success' : 'info'">
              {{ scope.row.enrollmentStatus === 'active' ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeStudentsDialog()">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listClass } from "@/api/kg/class/manage";
import { getScheduleByClass, getClassStudents, getClassCourseStats, getAttendanceBySchedule, getStudentsBySchedule } from "@/api/kg/schedule";
import { formatDateTime, formatTime } from "@/utils/ruoyi";

export default {
  name: "ClassSchedule",
  dicts: ['sys_user_sex', 'kg_student_status'],
  data() {
    return {
      // 班级列表
      classList: [],
      // 选中的班级ID
      selectedClassId: null,
      // 选中的班级信息
      selectedClass: null,
      // 班级排班列表
      scheduleList: [],
      // 学生列表
      studentList: [],
      // 查看类型
      viewType: 'current',
      // 显示模式
      displayMode: 'table',
      // 排班详情对话框
      scheduleDetailVisible: false,
      // 当前排班详情
      currentScheduleDetail: null,
      // 学生详情对话框
      studentDetailVisible: false,
      // 当前学生详情
      currentStudentDetail: null,
      // 星期选项
      weekDays: [
        { value: 1, label: '周一' },
        { value: 2, label: '周二' },
        { value: 3, label: '周三' },
        { value: 4, label: '周四' },
        { value: 5, label: '周五' },
        { value: 6, label: '周六' },
        { value: 7, label: '周日' }
      ],
      // 时间段
      timeSlots: [
        { value: '08:00', label: '08:00', period: '上午' },
        { value: '09:00', label: '09:00', period: '上午' },
        { value: '10:00', label: '10:00', period: '上午' },
        { value: '11:00', label: '11:00', period: '上午' },
        { value: '12:00', label: '12:00', period: '中午' },
        { value: '13:00', label: '13:00', period: '下午' },
        { value: '14:00', label: '14:00', period: '下午' },
        { value: '15:00', label: '15:00', period: '下午' },
        { value: '16:00', label: '16:00', period: '下午' },
        { value: '17:00', label: '17:00', period: '下午' }
      ],
      
      // 考勤记录相关
      attendanceDialogVisible: false,
      attendanceList: [],
      attendanceQuery: {
        attendanceDate: ''
      },
      currentViewSchedule: null,
      
      // 学生名单相关
      studentsDialogVisible: false,
      studentsList: []
    };
  },
  created() {
    this.loadClassList();
  },
  computed: {
    // 本周课时
    weeklyHours() {
      if (!this.scheduleList || !Array.isArray(this.scheduleList)) {
        return 0;
      }
      return this.scheduleList.length;
    },
    // 任课教师数
    teacherCount() {
      if (!this.scheduleList || !Array.isArray(this.scheduleList)) {
        return 0;
      }
      const teachers = new Set(this.scheduleList.map(s => s.teacherId));
      return teachers.size;
    },
    // 开设课程数
    courseCount() {
      if (!this.scheduleList || !Array.isArray(this.scheduleList)) {
        return 0;
      }
      const courses = new Set(this.scheduleList.map(s => s.courseId));
      return courses.size;
    },
    // 按时间排序的课表
    sortedScheduleList() {
      if (!this.scheduleList || !Array.isArray(this.scheduleList)) {
        return [];
      }
      
      return this.scheduleList
        .map(schedule => {
          // 添加格式化后的显示信息
          const dayName = this.getDayName(schedule.dayOfWeek || schedule.day_of_week);
          const timeRange = this.formatTimeRange(schedule.startTime || schedule.start_time, schedule.endTime || schedule.end_time);
          const courseName = schedule.courseName || schedule.course_name || '未知课程';
          const teacherName = schedule.teacherName || schedule.teacher_name || '未分配教师';
          
          return {
            ...schedule,
            dayName,
            timeRange,
            courseName,
            teacherName,
            fullTimeDisplay: `${dayName} ${timeRange}`
          };
        })
        .sort((a, b) => {
          // 按星期和开始时间排序
          const dayA = parseInt(a.dayOfWeek || a.day_of_week);
          const dayB = parseInt(b.dayOfWeek || b.day_of_week);
          if (dayA !== dayB) {
            return dayA - dayB;
          }
          
          const startA = a.startTime || a.start_time || '00:00:00';
          const startB = b.startTime || b.start_time || '00:00:00';
          return startA.localeCompare(startB);
        });
    }
  },
  created() {
    this.loadClassList();
  },
  methods: {
    /** 加载班级列表 */
    loadClassList() {
      listClass().then(response => {
        this.classList = response.rows;
      });
    },
    // 加载班级课表
    async loadClassSchedule() {
      if (!this.selectedClassId) {
        return;
      }

      this.loading = true;
      try {
        // 调用视图API获取完整的班级课表数据（含关联信息）
        const response = await getScheduleByClass(this.selectedClassId);
        this.scheduleList = response.data || [];
        
        // 获取班级信息
        this.selectedClass = this.classList.find(c => c.classId === this.selectedClassId);
        
        // 加载班级课程统计
        await this.loadClassStats();
        
        // 同时加载班级学生
        await this.loadClassStudents();
        
      } catch (error) {
        console.error('加载课表失败:', error);
        this.$message.error('加载课表失败：' + error.message);
      } finally {
        this.loading = false;
      }
    },
    // 加载班级学生
    loadClassStudents() {
      getClassStudents(this.selectedClassId).then(response => {
        this.studentList = response.data || [];
      });
    },
    // 加载班级统计信息
    async loadClassStats() {
      if (!this.selectedClassId) return;
      
      try {
        const response = await getClassCourseStats({ classId: this.selectedClassId });
        if (response.data) {
          // 更新computed中使用的数据不需要在这里处理，computed会自动计算
        }
      } catch (error) {
        console.error('加载班级统计失败:', error);
      }
    },
    
    // 根据星期和时间获取班级排班
    getClassScheduleByDayTime(dayOfWeek, timeSlot) {
      // 先获取当天的所有课程
      const daySchedules = this.scheduleList.filter(schedule => {
        const scheduleDayOfWeek = parseInt(schedule.dayOfWeek || schedule.day_of_week);
        return scheduleDayOfWeek === dayOfWeek;
      });
      
      // 为每个课程找到最优时间槽，确保不重复显示
      return daySchedules.filter(schedule => {
        const startTime = schedule.startTime || schedule.start_time;
        const endTime = schedule.endTime || schedule.end_time;
        if (!startTime || !endTime) return false;
        
        const startMinutes = this.timeToMinutes(startTime);
        const bestSlot = this.findBestTimeSlot(startTime, endTime);
        
        return bestSlot === timeSlot;
      });
    },
    
    // 找到课程的最优时间槽
    findBestTimeSlot(startTime, endTime) {
      const startMinutes = this.timeToMinutes(startTime);
      const endMinutes = this.timeToMinutes(endTime);
      
      let bestSlot = null;
      let bestScore = -1;
      
      this.timeSlots.forEach(slot => {
        const slotMinutes = this.timeToMinutes(slot.value + ':00');
        let score = 0;
        
        // 精确匹配得分最高
        const startHour = Math.floor(startMinutes / 60);
        const slotHour = Math.floor(slotMinutes / 60);
        if (startHour === slotHour) {
          score = 100;
        }
        // 课程跨越该时间槽
        else if (slotMinutes >= startMinutes && slotMinutes < endMinutes) {
          score = 80;
        }
        // 时间接近的得分较低
        else {
          const timeDiff = Math.abs(slotMinutes - startMinutes);
          if (timeDiff <= 30) {
            score = 60 - timeDiff;
          } else if (timeDiff <= 60) {
            score = 30 - (timeDiff - 30) / 2;
          }
        }
        
        if (score > bestScore) {
          bestScore = score;
          bestSlot = slot.value;
        }
      });
      
      return bestSlot;
    },
    
    // 获取星期名称
    getDayName(dayOfWeek) {
      const dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      const day = parseInt(dayOfWeek);
      return dayNames[day] || '未知';
    },
    
    // 将时间转换为分钟数
    timeToMinutes(timeString) {
      if (!timeString) return 0;
      const [hours, minutes] = timeString.split(':').map(Number);
      return hours * 60 + minutes;
    },
    
    // 获取班级排班样式
    getClassScheduleClass(schedule) {
      const classes = ['class-schedule-item'];
      if (schedule.scheduleType === 'substitute') {
        classes.push('substitute');
      } else if (schedule.scheduleType === 'makeup') {
        classes.push('makeup');
      }
      return classes;
    },
    
    // 格式化课程信息显示
    formatScheduleInfo(schedule) {
      if (!schedule) return '';
      
      const courseName = schedule.courseName || schedule.course_name || '未知课程';
      const teacherName = schedule.teacherName || schedule.teacher_name || '未分配教师';
      const timeRange = this.formatTimeRange(schedule.startTime || schedule.start_time, schedule.endTime || schedule.end_time);
      
      return {
        courseName,
        teacherName,
        timeRange,
        displayText: `${courseName}\n${teacherName}\n${timeRange}`
      };
    },
    
    // 格式化时间范围
    formatTimeRange(startTime, endTime) {
      if (!startTime || !endTime) return '';
      
      const start = startTime.substring(0, 5); // 只取时分部分
      const end = endTime.substring(0, 5);
      return `${start}-${end}`;
    },
    
    // 获取课程类型样式类
    getCourseTypeClass(schedule) {
      const courseType = schedule.courseType || schedule.course_type || '1';
      let classes = [];
      
      // 添加课程类型样式
      switch (courseType.toString()) {
        case '1':
          classes.push('course-type-care'); // 托管课程
          break;
        case '2':
          classes.push('course-type-interest'); // 兴趣班
          break;
        case '3':
          classes.push('course-type-special'); // 特色课程
          break;
        default:
          classes.push('course-type-care');
      }
      
      // 检查是否为长时间课程（超过3小时）
      const startTime = schedule.startTime || schedule.start_time;
      const endTime = schedule.endTime || schedule.end_time;
      if (startTime && endTime) {
        const startMinutes = this.timeToMinutes(startTime);
        const endMinutes = this.timeToMinutes(endTime);
        const duration = endMinutes - startMinutes;
        if (duration >= 180) { // 3小时以上
          classes.push('long-duration-course');
        }
      }
      
      return classes.join(' ');
    },
    
    // 查看排班详情
    viewScheduleDetail(schedule) {
      this.currentScheduleDetail = schedule;
      this.scheduleDetailVisible = true;
    },
    
    // 查看学生详情
    viewStudentDetail(student) {
      this.currentStudentDetail = student;
      this.studentDetailVisible = true;
    },
    
    // 查看学生名单
    viewStudentList(schedule) {
      this.currentViewSchedule = schedule;
      this.studentsDialogVisible = true;
      this.loadStudentsData(schedule.scheduleId);
    },
    
    // 查看考勤记录
    viewAttendance(schedule) {
      this.currentViewSchedule = schedule;
      this.attendanceDialogVisible = true;
      this.attendanceQuery.attendanceDate = '';
      this.loadAttendanceData();
    },
    
    /** 加载考勤数据 */
    loadAttendanceData() {
      if (!this.currentViewSchedule) return;
      
      getAttendanceBySchedule(this.currentViewSchedule.scheduleId, this.attendanceQuery.attendanceDate).then(response => {
        this.attendanceList = response.data;
      }).catch(error => {
        console.error('加载考勤记录失败:', error);
        this.$message.error('加载考勤记录失败');
      });
    },
    
    /** 加载学生数据 */
    loadStudentsData(scheduleId) {
      getStudentsBySchedule(scheduleId).then(response => {
        this.studentsList = response.data;
      }).catch(error => {
        console.error('加载学生名单失败:', error);
        this.$message.error('加载学生名单失败');
      });
    },
    
    /** 考勤日期变化 */
    handleAttendanceDateChange() {
      this.loadAttendanceData();
    },
    
    /** 关闭考勤对话框 */
    closeAttendanceDialog() {
      this.attendanceDialogVisible = false;
      this.attendanceList = [];
      this.currentViewSchedule = null;
    },
    
    /** 关闭学生名单对话框 */
    closeStudentsDialog() {
      this.studentsDialogVisible = false;
      this.studentsList = [];
      this.currentViewSchedule = null;
    },
    
    /** 获取考勤状态显示类型 */
    getAttendanceStatusType(status, isConfirmed) {
      if (status === 'present' && isConfirmed === 1) {
        return 'success'; // 已签到 - 绿色
      } else if (status === 'present' && isConfirmed === 0) {
        return 'warning'; // 待确认 - 黄色
      } else if (status === 'absent') {
        return 'danger'; // 缺勤 - 红色
      } else if (status === 'late') {
        return 'warning'; // 迟到 - 黄色
      } else if (status === 'early') {
        return 'info'; // 早退 - 蓝色
      }
      return 'info'; // 默认状态
    },
    
    // 引用公共时间格式化函数
    formatDateTime,
    formatTime,
    
    // 刷新学生列表
    refreshStudentList() {
      this.loadClassStudents();
    },
    
    // 打印课表
    handlePrint() {
      if (!this.selectedClassId) {
        this.$message.warning('请先选择班级');
        return;
      }
      window.print();
    },
    /** 导出课表 */
    handleExport() {
      if (!this.selectedClassId) {
        this.$message.warning('请先选择班级');
        return;
      }
      this.$message.success('导出功能开发中...');
    }
  }
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.class-select-card {
  margin-bottom: 20px;
}

.class-info-card {
  margin-bottom: 20px;
}

.class-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.class-details h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.class-details p {
  margin: 5px 0;
  color: #606266;
}

.class-stats {
  margin-left: auto;
  display: flex;
  gap: 40px;
}

.stat-item {
  text-align: center;
  min-width: 80px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

.schedule-display-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-view {
  overflow-x: auto;
}

.class-schedule-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  border: 1px solid #ebeef5;
}

.class-schedule-table th,
.class-schedule-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  vertical-align: top;
}

.time-header,
.day-header {
  background-color: #f5f7fa;
  font-weight: bold;
  font-size: 14px;
}

.time-cell {
  background-color: #fafafa;
  width: 100px;
}

.time-info {
  text-align: center;
}

.time-label {
  font-weight: bold;
  font-size: 14px;
  color: #303133;
}

.time-period {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.schedule-cell {
  height: 120px;
  position: relative;
  background-color: #fff;
  transition: background-color 0.3s;
}

.schedule-cell:hover {
  background-color: #f5f7fa;
}

/* 课程类型样式 */
.schedule-default {
  background: linear-gradient(135deg, #f0f9ff, #e6f4ff);
  border-left: 4px solid #409eff;
}

.schedule-hosted {
  background: linear-gradient(135deg, #f0f9f0, #e8f5e8);
  border-left: 4px solid #67c23a;
}

.schedule-interest {
  background: linear-gradient(135deg, #fef7e6, #fdecc8);
  border-left: 4px solid #e6a23c;
}

.schedule-special {
  background: linear-gradient(135deg, #fef0f0, #fde2e2);
  border-left: 4px solid #f56c6c;
}

/* 课程信息显示 */
.course-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 8px;
  text-align: center;
  line-height: 1.4;
}

.course-name {
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-word;
}

.teacher-name {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.time-range {
  font-size: 11px;
  color: #909399;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
}

/* 空课程槽样式 */
.empty-schedule {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 12px;
  height: 60px;
  margin: 4px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);
  transition: all 0.3s ease;
}

.empty-schedule:hover {
  border-color: #c0c4cc;
  background: linear-gradient(135deg, #f5f7fa 0%, #ecf5ff 100%);
  color: #909399;
  cursor: pointer;
}

/* 特殊排班类型 */
.substitute {
  opacity: 0.8;
  border-style: dashed !important;
}

.substitute::after {
  content: '代课';
  position: absolute;
  top: 2px;
  right: 4px;
  font-size: 10px;
  background: #f56c6c;
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
}

.makeup {
  opacity: 0.9;
}

.makeup::after {
  content: '补课';
  position: absolute;
  top: 2px;
  right: 4px;
  font-size: 10px;
  background: #e6a23c;
  color: white;
  padding: 1px 4px;
}

.class-schedule-item {
  margin: 4px;
  padding: 8px;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  line-height: 1.4;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
  transition: all 0.3s ease;
}

.class-schedule-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.class-schedule-item.substitute {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.class-schedule-item.makeup {
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
}

/* 课程类型样式 */
.class-schedule-item.course-type-care {
  background: linear-gradient(135deg, #00b894 0%, #55a3ff 100%);
  border-left: 4px solid #00b894;
}

.class-schedule-item.course-type-interest {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  border-left: 4px solid #e17055;
}

.class-schedule-item.course-type-special {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  border-left: 4px solid #e84393;
}

/* 长时间课程样式 */
.long-duration-course {
  min-height: 80px;
  position: relative;
}

.long-duration-course::after {
  content: '';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.4), transparent);
  border-radius: 2px;
}

.course-name {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 4px;
}

.teacher-name {
  font-size: 11px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.time-range {
  font-size: 10px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.course-type {
  text-align: center;
}

.empty-slot {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ddd;
  font-size: 24px;
}

.timeline-view {
  padding: 20px;
}

.timeline-card {
  margin-bottom: 10px;
}

.timeline-content {
  padding: 10px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.timeline-header h4 {
  margin: 0;
  color: #303133;
}

.timeline-body p {
  margin: 8px 0;
  color: #606266;
}

.timeline-actions {
  margin-top: 10px;
  text-align: right;
}

.student-list-card {
  margin-bottom: 20px;
}

.student-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 10px 0;
}

.student-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.student-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.student-info {
  flex: 1;
}

.student-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.student-status {
  text-align: left;
}

/* 打印样式 */
@media print {
  .page-header,
  .class-select-card,
  .class-info-card .class-stats,
  .header-actions,
  .header-tools,
  .student-list-card {
    display: none !important;
  }
  
  .class-schedule-table {
    border: 2px solid #000 !important;
  }
  
  .class-schedule-table th,
  .class-schedule-table td {
    border: 1px solid #000 !important;
  }
  
  .class-schedule-item {
    background: #f0f0f0 !important;
    color: #000 !important;
    border: 1px solid #000 !important;
  }
}
</style>
