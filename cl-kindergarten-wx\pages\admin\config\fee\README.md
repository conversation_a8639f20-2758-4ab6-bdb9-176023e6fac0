# 入园费用配置功能

## 功能概述

入园费用配置功能用于管理幼儿园各班级的费用标准，包括餐费、保教费等配置项。

## 功能特性

### 1. 费用配置列表 (`index.vue`)
- **查询筛选**：支持按班级类型筛选配置
- **配置展示**：显示班级类型、各项费用、生效日期、状态等信息
- **操作功能**：支持新增、编辑、删除、启用/停用配置

### 2. 费用配置表单 (`form.vue`)
- **新增配置**：创建新的费用配置
- **编辑配置**：修改现有的费用配置
- **表单验证**：确保数据的完整性和正确性

## 页面结构

```
pages/admin/config/fee/
├── index.vue          # 费用配置列表页面
├── form.vue           # 费用配置表单页面
└── README.md          # 功能说明文档
```

## API接口

### 接口文件：`api/tuitionConfig.js`

- `listTuitionConfig(params)` - 查询费用配置列表
- `getTuitionConfig(configId)` - 查询费用配置详情
- `addTuitionConfig(data)` - 新增费用配置
- `updateTuitionConfig(data)` - 编辑费用配置
- `deleteTuitionConfig(configId)` - 删除费用配置
- `changeStatus(configId, status)` - 启用/停用配置

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| configId | Long | 配置ID |
| classType | String | 班级类型（托班、小班、中班、大班） |
| mealFeePerDay | BigDecimal | 每日餐费 |
| educationFeePerMonth | BigDecimal | 每月保教费 |
| attendanceThreshold | BigDecimal | 出勤率阈值（0-1之间的小数） |
| halfEducationFee | BigDecimal | 半额保教费 |
| effectiveDate | Date | 生效日期 |
| status | String | 状态（0正常 1停用） |
| remark | String | 备注 |

## 使用说明

### 1. 查看费用配置
1. 进入"入园费用配置"页面
2. 可通过班级类型筛选查看特定配置
3. 配置列表显示所有相关信息

### 2. 新增费用配置
1. 点击页面右上角"+"按钮
2. 填写配置表单信息
3. 点击"保存"提交

### 3. 编辑费用配置
1. 在配置列表中点击"编辑"按钮
2. 修改配置信息
3. 点击"保存"提交

### 4. 删除费用配置
1. 在配置列表中点击"删除"按钮
2. 确认删除操作

### 5. 启用/停用配置
1. 在配置列表中点击"启用"或"停用"按钮
2. 确认状态切换操作

## 注意事项

1. **出勤率阈值**：在表单中以百分比形式输入（0-100），系统会自动转换为小数存储
2. **金额字段**：支持小数输入，用于精确的费用计算
3. **班级类型**：目前支持托班、小班、中班、大班四种类型
4. **状态管理**：停用的配置不会参与费用计算
5. **数据验证**：所有必填字段都有相应的验证规则

## 技术实现

- **前端框架**：uni-app
- **UI组件**：uView UI
- **状态管理**：Vue.js data
- **网络请求**：封装的request工具
- **表单验证**：自定义验证逻辑

## 后端接口

后端接口基于Spring Boot实现，主要包括：
- `KgTuitionConfigController` - 控制器
- `IKgTuitionConfigService` - 服务接口
- `KgTuitionConfigServiceImpl` - 服务实现
- `KgTuitionConfig` - 实体类
