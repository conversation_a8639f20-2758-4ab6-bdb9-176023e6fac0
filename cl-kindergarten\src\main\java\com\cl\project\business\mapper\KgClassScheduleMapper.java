package com.cl.project.business.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.cl.project.business.domain.KgClassSchedule;

/**
 * 班级课程时间Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface KgClassScheduleMapper 
{
    /**
     * 查询班级课程时间
     * 
     * @param scheduleId 班级课程时间ID
     * @return 班级课程时间
     */
    public KgClassSchedule selectKgClassScheduleById(Long scheduleId);

    /**
     * 查询班级课程时间列表
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 班级课程时间集合
     */
    public List<KgClassSchedule> selectKgClassScheduleList(KgClassSchedule kgClassSchedule);

    /**
     * 新增班级课程时间
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 结果
     */
    public int insertKgClassSchedule(KgClassSchedule kgClassSchedule);

    /**
     * 修改班级课程时间
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 结果
     */
    public int updateKgClassSchedule(KgClassSchedule kgClassSchedule);

    /**
     * 删除班级课程时间
     * 
     * @param scheduleId 班级课程时间ID
     * @return 结果
     */
    public int deleteKgClassScheduleById(Long scheduleId);

    /**
     * 批量删除班级课程时间
     * 
     * @param scheduleIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgClassScheduleByIds(Long[] scheduleIds);

    // ==================== 视图查询方法 ====================

    /**
     * 从视图查询排班信息列表（包含关联数据）
     * 
     * @param kgClassSchedule 排班查询条件
     * @return 排班信息列表
     */
    public List<java.util.Map<String, Object>> selectScheduleViewList(KgClassSchedule kgClassSchedule);

    /**
     * 从视图查询单个排班信息（包含关联数据）
     * 
     * @param scheduleId 排班ID
     * @return 排班信息
     */
    public java.util.Map<String, Object> selectScheduleViewById(Long scheduleId);

    /**
     * 根据教师ID从视图查询排班
     * 
     * @param teacherId 教师ID
     * @return 排班信息列表
     */
    public List<java.util.Map<String, Object>> selectScheduleViewByTeacherId(Long teacherId);

    /**
     * 根据班级ID从视图查询课表
     * 
     * @param classId 班级ID
     * @return 课表信息列表
     */
    public List<java.util.Map<String, Object>> selectScheduleViewByClassId(Long classId);
    
    /**
     * 根据排班信息查询对应的考勤记录
     * 
     * @param scheduleId 排班ID
     * @param attendanceDate 考勤日期(可选)
     * @return 考勤记录列表
     */
    public List<java.util.Map<String, Object>> getAttendanceBySchedule(@Param("scheduleId") Long scheduleId, @Param("attendanceDate") String attendanceDate);
    
    /**
     * 根据排班信息查询对应的学生名单
     * 
     * @param scheduleId 排班ID
     * @return 学生名单列表
     */
    public List<java.util.Map<String, Object>> getStudentsBySchedule(@Param("scheduleId") Long scheduleId);
    
    /**
     * 根据排班生成考勤记录模板
     */
    int generateAttendanceTemplate(@Param("scheduleId") Long scheduleId, @Param("attendanceDate") String attendanceDate);
    
    /**
     * 批量生成考勤记录模板
     */
    int batchGenerateAttendanceTemplate(@Param("scheduleId") Long scheduleId, @Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 学生签到
     */
    int studentCheckIn(@Param("attendanceId") Long attendanceId, @Param("checkInMethod") String checkInMethod, @Param("attendanceStatus") String attendanceStatus);
    
    /**
     * 管理员确认考勤
     */
    int confirmAttendance(@Param("attendanceId") Long attendanceId, @Param("confirmedBy") Long confirmedBy, @Param("finalStatus") String finalStatus);
    
    /**
     * 批量确认考勤
     */
    int batchConfirmAttendance(@Param("attendanceIds") java.util.List<Long> attendanceIds, @Param("confirmedBy") Long confirmedBy);
    
    /**
     * 模板记录签到（更新现有记录）
     */
    int templateCheckIn(@Param("attendanceId") Long attendanceId, 
                       @Param("attendanceStatus") String attendanceStatus, 
                       @Param("checkInMethod") String checkInMethod);
}
