<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">时间段配置</text>
				</view>
			</view>
		</view>

		<!-- 搜索筛选栏 -->
		<view class="search-filter-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input
					class="search-input"
					placeholder="搜索配置名称"
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>

			<!-- 筛选按钮 -->
			<view class="filter-actions">
				<view class="filter-chip" @click="showFilterPopup = true">
					<text>筛选</text>
				</view>
			</view>
		</view>

		<!-- 时间段配置列表 -->
		<view v-if="timeConfigList.length === 0 && !loading" class="empty-container">
			<view class="empty-illustration">
				<view class="empty-emoji">⏰</view>
			</view>
			<text class="empty-title">暂无时间段配置</text>
			<text class="empty-subtitle">点击右下角+号添加新配置</text>
		</view>

		<view v-if="loading && timeConfigList.length === 0" class="loading-container">
			<u-loading-icon mode="spinner" color="#4CAF50" size="40"></u-loading-icon>
			<text class="loading-text">加载中...</text>
		</view>

		<view v-if="timeConfigList.length > 0" class="content">
			<text class="list-header">时间段配置 ({{ timeConfigList.length }} 条)</text>

			<view
				v-for="item in timeConfigList"
				:key="item.configId"
				class="time-config-card"
				@click="handleDetail(item)"
			>
				<!-- 卡片头部 -->
				<view class="card-header">
					<view class="left-section">
						<view class="config-icon">
							<text class="icon-text">{{ getConfigTypeIcon(item.timeType) }}</text>
						</view>
						<view class="info-section">
							<text class="primary-text">{{ item.configName || '未命名配置' }}</text>
							<text class="secondary-text">{{ getConfigTypeText(item.timeType) }}</text>
						</view>
					</view>
					<view :class="'status-badge ' + getStatusClass(item.isActive)">
						<text class="badge-text">{{ getStatusText(item.isActive) }}</text>
					</view>
				</view>

				<!-- 配置详情 -->
				<view class="card-body">
					<view class="config-details">
						<!-- 时间段信息 -->
						<view class="detail-row">
							<text class="detail-label">时间段：</text>
							<text class="detail-value">{{ item.startTime }} - {{ item.endTime }}</text>
						</view>

						<!-- 创建时间 -->
						<view class="detail-row">
							<text class="detail-label">创建时间：</text>
							<text class="detail-value">{{ formatDate(item.createTime) }}</text>
						</view>

						<!-- 备注 -->
						<view v-if="item.remark" class="detail-row">
							<text class="detail-label">备注：</text>
							<text class="detail-value">{{ item.remark }}</text>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="card-actions">
					<button class="action-btn secondary" @click.stop="handleEdit(item)">
						<text class="btn-text">编辑</text>
					</button>
					<button class="action-btn" :class="item.isActive === 1 ? 'danger' : 'primary'" @click.stop="handleStatusChange(item)">
						<text class="btn-text">{{ item.isActive === 1 ? '禁用' : '启用' }}</text>
					</button>
					<button class="action-btn danger" @click.stop="handleDelete(item)">
						<text class="btn-text">删除</text>
					</button>
				</view>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMore" class="load-more" @click="loadMore">
				<u-loading-icon v-if="loading" mode="spinner" color="#4CAF50" size="24"></u-loading-icon>
				<text class="load-more-text">{{ loading ? '加载中...' : '点击加载更多' }}</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-else-if="timeConfigList.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了</text>
			</view>
		</view>

		<!-- 浮动新增按钮 -->
		<view class="floating-add-btn" @click="showAddDialog">
			<u-icon name="plus" color="#ffffff" size="24"></u-icon>
		</view>

		<!-- 筛选弹窗 -->
		<u-popup v-model="showFilterPopup" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="popup-title">筛选条件</text>
					<view class="popup-close" @click="showFilterPopup = false">
						<u-icon name="close" size="20" color="#666"></u-icon>
					</view>
				</view>

				<view class="filter-content">
					<view class="filter-group">
						<text class="filter-label">时间类型</text>
						<view class="filter-options">
							<view
								:class="['filter-option radio-option', { active: filterForm.timeType === '' }]"
								@click="selectTimeType('')"
							>
								<view class="radio-icon">
									<view v-if="filterForm.timeType === ''" class="radio-dot"></view>
								</view>
								<text class="option-text">全部</text>
							</view>
							<view
								v-for="type in timeTypeOptions"
								:key="type.value"
								:class="['filter-option radio-option', { active: filterForm.timeType === type.value }]"
								@click="selectTimeType(type.value)"
							>
								<view class="radio-icon">
									<view v-if="filterForm.timeType === type.value" class="radio-dot"></view>
								</view>
								<text class="option-text">{{ type.label }}</text>
							</view>
						</view>
					</view>

					<view class="filter-group">
						<text class="filter-label">状态</text>
						<view class="filter-options">
							<view
								v-for="status in statusOptions"
								:key="status.value"
								:class="['filter-option radio-option', { active: filterForm.isActive === status.value }]"
								@click="selectStatus(status.value)"
							>
								<view class="radio-icon">
									<view v-if="filterForm.isActive === status.value" class="radio-dot"></view>
								</view>
								<text class="option-text">{{ status.label }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="filter-actions">
					<button class="filter-btn reset" @click="resetFilter">重置</button>
					<button class="filter-btn confirm" @click="applyFilter">确定</button>
				</view>
			</view>
		</u-popup>

		<!-- 新增/编辑配置弹窗 -->
		<u-popup v-model="showFormDialog" mode="center" width="90%" border-radius="20">
			<view class="form-dialog">
				<view class="dialog-header">
					<text class="dialog-title">{{ isEdit ? '编辑配置' : '新增配置' }}</text>
					<view class="dialog-close" @click="closeFormDialog">
						<u-icon name="close" size="20" color="#666"></u-icon>
					</view>
				</view>

				<view class="dialog-content">
					<view class="form-group">
						<text class="form-label">配置名称 <text class="required">*</text></text>
						<u-input
							v-model="formData.configName"
							placeholder="请输入配置名称"
							:border="true"
							:clearable="true"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">时间类型 <text class="required">*</text></text>
						<view class="radio-group">
							<view
								v-for="type in timeTypeOptions"
								:key="type.value"
								:class="['radio-item', { active: formData.timeType === type.value }]"
								@click="selectFormTimeType(type.value)"
							>
								<view class="radio-circle">
									<view v-if="formData.timeType === type.value" class="radio-dot"></view>
								</view>
								<text class="radio-label">{{ type.label }}</text>
							</view>
						</view>
					</view>



					<!-- 时间段设置 -->
					<view class="form-group">
						<text class="form-label">开始时间 <text class="required">*</text></text>
						<view class="time-picker-input" @click="showStartTimePicker = true">
							<text class="time-text">{{ formData.startTime || '请选择开始时间' }}</text>
							<u-icon name="clock" size="16" color="#999"></u-icon>
						</view>
					</view>

					<view class="form-group">
						<text class="form-label">结束时间 <text class="required">*</text></text>
						<view class="time-picker-input" @click="showEndTimePicker = true">
							<text class="time-text">{{ formData.endTime || '请选择结束时间' }}</text>
							<u-icon name="clock" size="16" color="#999"></u-icon>
						</view>
					</view>



					<!-- 时间选择器 -->
					<view v-if="showStartTimePicker" class="time-picker-overlay" @click="onStartTimeCancel">
						<view class="time-picker-container" @click.stop="">
							<view class="time-picker-header">
								<text class="picker-cancel" @click="onStartTimeCancel">取消</text>
								<text class="picker-title">选择开始时间</text>
								<text class="picker-confirm" @click="confirmStartTime">确定</text>
							</view>
							<picker-view class="time-picker-view" :value="startTimePickerValue" @change="onStartTimeChange">
								<picker-view-column>
									<view v-for="hour in hourOptions" :key="hour" class="picker-item">
										{{ String(hour).padStart(2, '0') }}时
									</view>
								</picker-view-column>
								<picker-view-column>
									<view v-for="minute in minuteOptions" :key="minute" class="picker-item">
										{{ String(minute).padStart(2, '0') }}分
									</view>
								</picker-view-column>
							</picker-view>
						</view>
					</view>

					<view v-if="showEndTimePicker" class="time-picker-overlay" @click="onEndTimeCancel">
						<view class="time-picker-container" @click.stop="">
							<view class="time-picker-header">
								<text class="picker-cancel" @click="onEndTimeCancel">取消</text>
								<text class="picker-title">选择结束时间</text>
								<text class="picker-confirm" @click="confirmEndTime">确定</text>
							</view>
							<picker-view class="time-picker-view" :value="endTimePickerValue" @change="onEndTimeChange">
								<picker-view-column>
									<view v-for="hour in hourOptions" :key="hour" class="picker-item">
										{{ String(hour).padStart(2, '0') }}时
									</view>
								</picker-view-column>
								<picker-view-column>
									<view v-for="minute in minuteOptions" :key="minute" class="picker-item">
										{{ String(minute).padStart(2, '0') }}分
									</view>
								</picker-view-column>
							</picker-view>
						</view>
					</view>
				</view>

				<view class="dialog-actions">
					<button class="dialog-btn cancel" @click="closeFormDialog">取消</button>
					<button class="dialog-btn confirm" @click="handleSubmit">{{ isEdit ? '更新' : '创建' }}</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import {
	getTimeConfigList,
	getTimeConfigDetail,
	addTimeConfig,
	updateTimeConfig,
	deleteTimeConfig
} from '@/api/api.js'

export default {
	data() {
		return {
			// 列表数据
			timeConfigList: [],
			loading: false,
			hasMore: true,

			// 搜索筛选
			searchKeyword: '',
			showFilterPopup: false,
			filterForm: {
				timeType: '',
				isActive: ''
			},

			// 表单弹窗
			showFormDialog: false,
			isEdit: false,
			formData: {
				configId: null,
				configName: '',
				timeType: '',
				startTime: '',
				endTime: '',
				isActive: 1
			},

			// 时间选择器
			showStartTimePicker: false,
			showEndTimePicker: false,
			startTimePickerValue: [8, 0], // [小时索引, 分钟索引]
			endTimePickerValue: [17, 0],
			tempStartTime: [8, 0],
			tempEndTime: [17, 0],

			// 时间选项
			hourOptions: [],
			minuteOptions: [],

			// 选项数据
			timeTypeOptions: [
				{ label: '园费', value: 'tuition' },
				{ label: '托管费', value: 'course' }
			],
			statusOptions: [
				{ label: '全部', value: '' },
				{ label: '启用', value: 1 },
				{ label: '禁用', value: 0 }
			],

			// 分页
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				configName: null,
				timeType: null,
				isActive: null
			}
		}
	},

	onLoad() {
		this.loadTimeConfigList(true)
		this.initTimePicker()
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 初始化时间选择器
		initTimePicker() {
			// 生成小时选项 (0-23)
			this.hourOptions = []
			for (let i = 0; i < 24; i++) {
				this.hourOptions.push(i)
			}

			// 生成分钟选项 (0, 15, 30, 45)
			this.minuteOptions = [0, 15, 30, 45]
		},

	// 过滤参数，移除null、undefined和空字符串
	filterParams(params) {
		const filteredParams = {}
		Object.keys(params).forEach(key => {
			const value = params[key]
			if (value !== null && value !== undefined && value !== '') {
				filteredParams[key] = value
			}
		})
		return filteredParams
	},

	// 加载时间段配置列表
	async loadTimeConfigList(refresh = false) {
		if (this.loading) return

		this.loading = true

		if (refresh) {
			this.queryParams.pageNum = 1
			this.timeConfigList = []
			this.hasMore = true
		}

		try {
			// 过滤掉null值的参数，只传递有值的参数
			const params = this.filterParams(this.queryParams)

			const response = await getTimeConfigList(params)

			if (response.code === 200) {
				const newList = response.rows || []

				if (refresh) {
					this.timeConfigList = newList
				} else {
					this.timeConfigList = [...this.timeConfigList, ...newList]
				}

				// 判断是否还有更多数据
				this.hasMore = newList.length >= this.queryParams.pageSize
			} else {
				toast(response.msg || '加载失败')
			}
		} catch (error) {
			console.error('加载时间段配置列表失败:', error)
			toast('加载失败，请重试')
		} finally {
			this.loading = false
		}
	},

	// 搜索输入
	onSearchInput() {
		// 防抖处理
		clearTimeout(this.searchTimer)
		this.searchTimer = setTimeout(() => {
			this.handleSearch()
		}, 500)
	},

	// 执行搜索
	handleSearch() {
		this.queryParams.configName = this.searchKeyword.trim() || null
		this.loadTimeConfigList(true)
	},

	// 清除搜索
	clearSearch() {
		this.searchKeyword = ''
		this.queryParams.configName = null
		this.loadTimeConfigList(true)
	},

	// 加载更多
	loadMore() {
		if (!this.hasMore || this.loading) return
		this.queryParams.pageNum++
		this.loadTimeConfigList()
	},

	// 筛选相关方法
	selectTimeType(value) {
		this.filterForm.timeType = value
	},

	selectStatus(value) {
		this.filterForm.isActive = value
	},

	resetFilter() {
		this.filterForm = {
			timeType: '',
			isActive: ''
		}
	},

	applyFilter() {
		this.queryParams.timeType = this.filterForm.timeType || null
		this.queryParams.isActive = this.filterForm.isActive || null
		this.showFilterPopup = false
		this.loadTimeConfigList(true)
	},

	// 表单相关方法
	showAddDialog() {
		this.isEdit = false
		this.resetFormData()
		this.showFormDialog = true
	},

	closeFormDialog() {
		this.showFormDialog = false
		this.resetFormData()
	},

	resetFormData() {
		this.formData = {
			configId: null,
			configName: '',
			timeType: '',
			startTime: '',
			endTime: '',
			isActive: 1
		}

		// 重置时间选择器的值
		this.startTimePickerValue = [8, 0]
		this.endTimePickerValue = [17, 0]
		this.tempStartTime = [8, 0]
		this.tempEndTime = [17, 0]
		this.showStartTimePicker = false
		this.showEndTimePicker = false
	},

	// 表单中时间类型选择
	selectFormTimeType(value) {
		this.formData.timeType = value
	},

	// 时间选择器相关方法
	onStartTimeChange(e) {
		this.tempStartTime = e.detail.value
	},

	confirmStartTime() {
		const hour = this.hourOptions[this.tempStartTime[0]]
		const minute = this.minuteOptions[this.tempStartTime[1]]

		this.formData.startTime = `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`
		this.startTimePickerValue = [...this.tempStartTime]
		this.showStartTimePicker = false
	},

	onStartTimeCancel() {
		this.tempStartTime = [...this.startTimePickerValue]
		this.showStartTimePicker = false
	},

	onEndTimeChange(e) {
		this.tempEndTime = e.detail.value
	},

	confirmEndTime() {
		const hour = this.hourOptions[this.tempEndTime[0]]
		const minute = this.minuteOptions[this.tempEndTime[1]]

		this.formData.endTime = `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`
		this.endTimePickerValue = [...this.tempEndTime]
		this.showEndTimePicker = false
	},

	onEndTimeCancel() {
		this.tempEndTime = [...this.endTimePickerValue]
		this.showEndTimePicker = false
	},

	// 表单提交
	async handleSubmit() {
		// 表单验证
		if (!this.formData.configName) {
			toast('请输入配置名称')
			return
		}

		if (!this.formData.timeType) {
			toast('请选择时间类型')
			return
		}

		if (!this.formData.startTime) {
			toast('请选择开始时间')
			return
		}

		if (!this.formData.endTime) {
			toast('请选择结束时间')
			return
		}

		// 验证时间逻辑
		if (this.formData.startTime >= this.formData.endTime) {
			toast('开始时间必须小于结束时间')
			return
		}

		try {
			const submitData = { ...this.formData }

			let response
			if (this.isEdit) {
				response = await updateTimeConfig(submitData)
			} else {
				response = await addTimeConfig(submitData)
			}

			if (response.code === 200) {
				toast(this.isEdit ? '更新成功' : '创建成功')
				this.closeFormDialog()
				this.loadTimeConfigList(true)
			} else {
				toast(response.msg || '操作失败')
			}
		} catch (error) {
			console.error('提交失败:', error)
			toast('操作失败，请重试')
		}
	},

	// 列表操作方法
	handleDetail(item) {
		// 可以跳转到详情页面或显示详情弹窗
		console.log('查看详情:', item)
	},

	async handleEdit(item) {
		try {
			const response = await getTimeConfigDetail(item.configId)
			if (response.code === 200) {
				this.formData = { ...response.data }

				// 设置时间选择器的值
				if (this.formData.startTime) {
					const [hour, minute] = this.formData.startTime.split(':')
					const hourIndex = this.hourOptions.indexOf(parseInt(hour))
					const minuteIndex = this.minuteOptions.indexOf(parseInt(minute))
					this.startTimePickerValue = [hourIndex, minuteIndex]
					this.tempStartTime = [hourIndex, minuteIndex]
				}
				if (this.formData.endTime) {
					const [hour, minute] = this.formData.endTime.split(':')
					const hourIndex = this.hourOptions.indexOf(parseInt(hour))
					const minuteIndex = this.minuteOptions.indexOf(parseInt(minute))
					this.endTimePickerValue = [hourIndex, minuteIndex]
					this.tempEndTime = [hourIndex, minuteIndex]
				}

				this.isEdit = true
				this.showFormDialog = true
			} else {
				toast(response.msg || '获取详情失败')
			}
		} catch (error) {
			console.error('获取详情失败:', error)
			toast('获取详情失败，请重试')
		}
	},

	async handleStatusChange(item) {
		const action = item.isActive === 1 ? '禁用' : '启用'

		uni.showModal({
			title: '确认操作',
			content: `确定要${action}配置"${item.configName}"吗？`,
			success: async (res) => {
				if (res.confirm) {
					try {
						const newStatus = item.isActive === 1 ? 0 : 1
						const response = await updateTimeConfig({
							...item,
							isActive: newStatus
						})

						if (response.code === 200) {
							toast(`${action}成功`)
							this.loadTimeConfigList(true)
						} else {
							toast(response.msg || `${action}失败`)
						}
					} catch (error) {
						console.error(`${action}失败:`, error)
						toast(`${action}失败，请重试`)
					}
				}
			}
		})
	},

	async handleDelete(item) {
		uni.showModal({
			title: '确认删除',
			content: `确定要删除配置"${item.configName}"吗？删除后不可恢复。`,
			success: async (res) => {
				if (res.confirm) {
					try {
						const response = await deleteTimeConfig(item.configId)

						if (response.code === 200) {
							toast('删除成功')
							this.loadTimeConfigList(true)
						} else {
							toast(response.msg || '删除失败')
						}
					} catch (error) {
						console.error('删除失败:', error)
						toast('删除失败，请重试')
					}
				}
			}
		})
	},

	// 工具方法
	getConfigTypeIcon(timeType) {
		switch (timeType) {
			case 'tuition':
				return '🏫'
			case 'course':
				return '📚'
			default:
				return '⏰'
		}
	},

	getConfigTypeText(timeType) {
		switch (timeType) {
			case 'tuition':
				return '园费'
			case 'course':
				return '托管费'
			default:
				return '未知类型'
		}
	},

	getStatusClass(isActive) {
		return isActive === 1 ? 'active' : 'inactive'
	},

	getStatusText(isActive) {
		return isActive === 1 ? '启用' : '禁用'
	},

	formatDate(dateString) {
		if (!dateString) return ''
		const date = new Date(dateString)
		return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
	}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 搜索筛选栏 */
.search-filter-section {
	background: #ffffff;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.search-box {
	flex: 1;
	position: relative;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 24rpx;
	padding: 0 20rpx;
	height: 72rpx;
}

.search-icon {
	font-size: 28rpx;
	color: #999;
	margin-right: 12rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
}

.clear-icon {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #ccc;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	color: #fff;
	margin-left: 12rpx;
}

.filter-actions {
	display: flex;
	gap: 16rpx;
}

.filter-chip {
	padding: 16rpx 24rpx;
	background: #4CAF50;
	color: #ffffff;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&:active {
		background: #388E3C;
		transform: scale(0.95);
	}
}

/* 空状态 */
.empty-container {
	padding: 120rpx 60rpx;
	text-align: center;
}

.empty-illustration {
	margin-bottom: 40rpx;
}

.empty-emoji {
	font-size: 120rpx;
	display: block;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-subtitle {
	font-size: 26rpx;
	color: #666;
	display: block;
}

/* 加载状态 */
.loading-container {
	padding: 120rpx 60rpx;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.loading-text {
	font-size: 26rpx;
	color: #666;
}

/* 页面内容 */
.content {
	padding: 30rpx;
}

.list-header {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

/* 时间段配置卡片 */
.time-config-card {
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}
}

.card-header {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f0f0;
}

.left-section {
	display: flex;
	align-items: center;
	flex: 1;
}

.config-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.icon-text {
	font-size: 32rpx;
}

.info-section {
	flex: 1;
}

.primary-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.secondary-text {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 500;

	&.active {
		background: rgba(76, 175, 80, 0.1);
		color: #4CAF50;
	}

	&.inactive {
		background: rgba(158, 158, 158, 0.1);
		color: #9E9E9E;
	}
}

.badge-text {
	font-size: 22rpx;
}

.card-body {
	padding: 32rpx;
}

.config-details {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.detail-row {
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
}

.detail-label {
	font-size: 26rpx;
	color: #666;
	min-width: 120rpx;
	flex-shrink: 0;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
	line-height: 1.4;
}

.card-actions {
	padding: 24rpx 32rpx;
	display: flex;
	gap: 16rpx;
	border-top: 1rpx solid #f0f0f0;
}

.action-btn {
	flex: 1;
	height: 64rpx;
	border-radius: 12rpx;
	border: none;
	font-size: 26rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&.primary {
		background: #4CAF50;
		color: #ffffff;

		&:active {
			background: #388E3C;
		}
	}

	&.secondary {
		background: #f8f9fa;
		color: #666;
		border: 1rpx solid #e0e0e0;

		&:active {
			background: #e9ecef;
		}
	}

	&.danger {
		background: #f44336;
		color: #ffffff;

		&:active {
			background: #d32f2f;
		}
	}
}

.btn-text {
	font-size: 26rpx;
	font-weight: 500;
}

/* 加载更多 */
.load-more {
	padding: 40rpx;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
}

.load-more-text {
	font-size: 26rpx;
	color: #666;
}

.no-more {
	padding: 40rpx;
	text-align: center;
}

.no-more-text {
	font-size: 24rpx;
	color: #999;
}

/* 浮动新增按钮 */
.floating-add-btn {
	position: fixed;
	bottom: 120rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.4);
	z-index: 999;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.6);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 50%;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
		pointer-events: none;
	}
}

/* 筛选弹窗 */
.filter-popup {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	overflow: hidden;
}

.popup-header {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.popup-close {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
}

.filter-content {
	padding: 32rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.filter-group {
	margin-bottom: 40rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.filter-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.filter-options {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.filter-option {
	padding: 20rpx 24rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #ffffff;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}

	&.active {
		border-color: #4CAF50;
		background: rgba(76, 175, 80, 0.05);
	}
}

.radio-option {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.radio-icon {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	.filter-option.active & {
		border-color: #4CAF50;
	}
}

.radio-dot {
	width: 16rpx;
	height: 16rpx;
	background: #4CAF50;
	border-radius: 50%;
}

.option-text {
	font-size: 28rpx;
	color: #333;

	.filter-option.active & {
		color: #4CAF50;
		font-weight: 500;
	}
}

.filter-actions {
	padding: 32rpx;
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.filter-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 16rpx;
	border: none;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&.reset {
		background: #f8f9fa;
		color: #666;

		&:active {
			background: #e9ecef;
		}
	}

	&.confirm {
		background: #4CAF50;
		color: #ffffff;

		&:active {
			background: #388E3C;
		}
	}
}

/* 表单弹窗 */
.form-dialog {
	background: #ffffff;
	border-radius: 20rpx;
	max-height: 90vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.dialog-header {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f0f0;
	flex-shrink: 0;
}

.dialog-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.dialog-close {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
}

.dialog-content {
	padding: 32rpx;
	flex: 1;
	overflow-y: auto;
}

.form-group {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.required {
	color: #f44336;
}

/* 单选框组 */
.radio-group {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-top: 16rpx;
}

.radio-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 24rpx;
	background: #ffffff;
	transition: all 0.3s ease;
	cursor: pointer;

	&:active {
		transform: scale(0.98);
	}

	&.active {
		border-color: #4CAF50;
		background: rgba(76, 175, 80, 0.05);
	}
}

.radio-circle {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	.radio-item.active & {
		border-color: #4CAF50;
	}
}

.radio-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;

	.radio-item.active & {
		color: #4CAF50;
	}
}

/* 时间选择器输入框 */
.time-picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #ffffff;
	transition: all 0.3s ease;

	&:active {
		border-color: #4CAF50;
	}
}

.time-text {
	font-size: 28rpx;
	color: #333;

	&:empty::before {
		content: attr(placeholder);
		color: #999;
	}
}

/* 时间选择器弹窗 */
.time-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	z-index: 9999;
}

.time-picker-container {
	width: 100%;
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
}

.time-picker-header {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel, .picker-confirm {
	font-size: 28rpx;
	color: #4CAF50;
	font-weight: 500;
}

.picker-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.time-picker-view {
	height: 400rpx;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
}

.dialog-actions {
	padding: 32rpx;
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	flex-shrink: 0;
}

.dialog-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 16rpx;
	border: none;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&.cancel {
		background: #f8f9fa;
		color: #666;

		&:active {
			background: #e9ecef;
		}
	}

	&.confirm {
		background: #4CAF50;
		color: #ffffff;

		&:active {
			background: #388E3C;
		}
	}
}
</style>
