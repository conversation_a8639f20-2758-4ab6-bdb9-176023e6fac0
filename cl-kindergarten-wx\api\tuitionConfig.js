import request from '@/utils/request.js'

/**
 * 园费配置相关API接口
 */

// 查询园费配置列表
export const listTuitionConfig = (params) => {
  return request.get('/business/tuition-config/wx/list', params)
}

// 查询园费配置详情
export const getTuitionConfig = (configId) => {
  return request.get(`/business/tuition-config/wx/${configId}`)
}

// 新增园费配置
export const addTuitionConfig = (data) => {
  return request.post('/business/tuition-config/wx', data)
}

// 编辑园费配置
export const updateTuitionConfig = (data) => {
  return request.put('/business/tuition-config/wx', data)
}

// 删除园费配置
export const deleteTuitionConfig = (configId) => {
  return request.delete(`/business/tuition-config/wx/${configId}`)
}

// 批量删除园费配置
export const deleteTuitionConfigs = (configIds) => {
  return request.delete(`/business/tuition-config/wx/${configIds.join(',')}`)
}

// 启用/停用园费配置
export const changeStatus = (configId, status) => {
  return request.put('/business/tuition-config/wx/changeStatus', {
    configId,
    status
  })
}

// 导出园费配置
export const exportTuitionConfig = (params) => {
  return request.get('/business/tuition-config/export', params)
}
