<template>
  <div class="app-container">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <h2>课程排班管理</h2>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增排班</el-button>
        <el-button type="info" icon="el-icon-view" @click="viewMode = 'week'">周视图</el-button>
        <el-button type="info" icon="el-icon-menu" @click="viewMode = 'list'">列表视图</el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <el-form-item label="班级" prop="classId">
          <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable @change="getList">
            <el-option
              v-for="item in classList"
              :key="item.classId"
              :label="item.className"
              :value="item.classId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="教师" prop="teacherId">
          <el-select v-model="queryParams.teacherId" placeholder="请选择教师" clearable @change="getList">
            <el-option
              v-for="item in teacherList"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="课程" prop="courseId">
          <el-select v-model="queryParams.courseId" placeholder="请选择课程" clearable @change="getList">
            <el-option
              v-for="item in courseList"
              :key="item.courseId"
              :label="item.courseName"
              :value="item.courseId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 周视图 -->
    <el-card v-if="viewMode === 'week'" class="schedule-card">
      <div class="week-schedule">
        <table class="schedule-table">
          <thead>
            <tr>
              <th class="time-header">时间</th>
              <th v-for="day in weekDays" :key="day.value" class="day-header">
                {{ day.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="time in timeSlots" :key="time.value">
              <td class="time-cell">{{ time.label }}</td>
              <td v-for="day in weekDays" :key="day.value" class="schedule-cell">
                <div
                  v-for="schedule in getScheduleByDayTime(day.value, time.value)"
                  :key="schedule.scheduleId"
                  class="schedule-item"
                  :class="getScheduleClass(schedule)"
                  @click="handleEdit(schedule)">
                  <div class="schedule-title">{{ schedule.courseName }}</div>
                  <div class="schedule-info">{{ schedule.className }}</div>
                  <div class="schedule-teacher">{{ schedule.teacherName }}</div>
                  <div class="schedule-time">{{ schedule.timeRange }}</div>
                  <div class="schedule-actions">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      class="delete-btn"
                      @click.stop="handleDelete(schedule)"
                      v-hasPermi="['kg:schedule:remove']"
                      title="删除">
                    </el-button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </el-card>

    <!-- 列表视图 -->
    <el-card v-if="viewMode === 'list'" class="table-card">
      <el-table v-loading="loading" :data="scheduleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="班级" align="center" prop="className" width="100" />
        <el-table-column label="课程" align="center" prop="courseName" width="120" />
        <el-table-column label="教师" align="center" prop="teacherName" width="100" />
        <el-table-column label="星期" align="center" prop="dayName" width="80" />
        <el-table-column label="时间" align="center" prop="timeRange" width="120" />
        <el-table-column label="生效日期" align="center" prop="effectiveDate" width="110">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.effectiveDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isActive === 1 ? 'success' : 'danger'">
              {{ scope.row.isActive === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" prop="scheduleType" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.scheduleType === 'regular'" type="success">常规</el-tag>
            <el-tag v-else-if="scope.row.scheduleType === 'substitute'" type="warning">代课</el-tag>
            <el-tag v-else-if="scope.row.scheduleType === 'makeup'" type="info">补课</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['kg:schedule:edit']">修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['kg:schedule:remove']">删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="handleCopy(scope.row)">复制</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"/>
    </el-card>

    <!-- 添加或修改排班对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="班级" prop="classId">
              <el-select v-model="form.classId" placeholder="请选择班级">
                <el-option
                  v-for="item in classList"
                  :key="item.classId"
                  :label="item.className"
                  :value="item.classId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程" prop="courseId">
              <el-select v-model="form.courseId" placeholder="请选择课程">
                <el-option
                  v-for="item in courseList"
                  :key="item.courseId"
                  :label="item.courseName"
                  :value="item.courseId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="教师" prop="teacherId">
              <el-select v-model="form.teacherId" placeholder="请选择教师">
                <el-option
                  v-for="item in teacherList"
                  :key="item.teacherId"
                  :label="item.teacherName"
                  :value="item.teacherId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="星期" prop="dayOfWeek">
              <el-select v-model="form.dayOfWeek" placeholder="请选择星期">
                <el-option
                  v-for="item in weekDays"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="form.startTime"
                value-format="HH:mm:ss"
                placeholder="选择开始时间">
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="form.endTime"
                value-format="HH:mm:ss"
                placeholder="选择结束时间">
              </el-time-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="form.effectiveDate"
                type="date"
                placeholder="选择生效日期"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效日期" prop="expiryDate">
              <el-date-picker
                v-model="form.expiryDate"
                type="date"
                placeholder="选择失效日期"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型" prop="scheduleType">
              <el-radio-group v-model="form.scheduleType">
                <el-radio label="regular">常规</el-radio>
                <el-radio label="substitute">代课</el-radio>
                <el-radio label="makeup">补课</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.isActive" size="small">
                <el-radio-button :label="1">启用</el-radio-button>
                <el-radio-button :label="0">禁用</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSchedule, listScheduleView, getSchedule, delSchedule, addSchedule, updateSchedule } from "@/api/kg/schedule";
import { listAllClass } from "@/api/kg/class/manage";
import { listAllTeacher } from "@/api/kg/teacher/info";
import { listAllCourse } from "@/api/kg/course/manage";

export default {
  name: "Schedule",
  dicts: ['kg_schedule_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 排班表格数据
      scheduleList: [],
      // 班级列表
      classList: [],
      // 教师列表
      teacherList: [],
      // 课程列表
      courseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        classId: null,
        courseId: null,
        teacherId: null,
        dayOfWeek: null,
        isActive: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        classId: [
          { required: true, message: "班级不能为空", trigger: "change" }
        ],
        courseId: [
          { required: true, message: "课程不能为空", trigger: "change" }
        ],
        teacherId: [
          { required: true, message: "教师不能为空", trigger: "change" }
        ],
        dayOfWeek: [
          { required: true, message: "星期不能为空", trigger: "change" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" }
        ],
        effectiveDate: [
          { required: true, message: "生效日期不能为空", trigger: "blur" }
        ]
      },
      // 视图模式：week 周视图，list 列表视图
      viewMode: 'week',
      // 星期选项
      weekDays: [
        { value: 1, label: '周一' },
        { value: 2, label: '周二' },
        { value: 3, label: '周三' },
        { value: 4, label: '周四' },
        { value: 5, label: '周五' },
        { value: 6, label: '周六' },
        { value: 7, label: '周日' }
      ],
      // 时间段
      timeSlots: [
        { value: '08:00', label: '08:00' },
        { value: '09:00', label: '09:00' },
        { value: '10:00', label: '10:00' },
        { value: '11:00', label: '11:00' },
        { value: '14:00', label: '14:00' },
        { value: '15:00', label: '15:00' },
        { value: '16:00', label: '16:00' },
        { value: '17:00', label: '17:00' }
      ]
    };
  },
  created() {
    this.getList();
    this.getBasicData();
  },
  methods: {
    /** 查询排班列表 */
    getList() {
      this.loading = true;
      
      // 根据视图模式使用不同的查询策略
      if (this.viewMode === 'week') {
        // 周视图：需要查询所有数据（不分页）
        const weekQueryParams = { ...this.queryParams };
        delete weekQueryParams.pageNum;  // 移除分页参数
        delete weekQueryParams.pageSize;
        weekQueryParams.noPaging = true;  // 加上不分页参数
        
        listScheduleView(weekQueryParams).then(response => {
          console.log('周视图查询返回数据:', response);
          console.log('第一条数据结构:', response.rows && response.rows[0]);
          this.scheduleList = response.rows || response.data || []; // 兼容不同返回结构
          
          // 详细调试信息
          console.log('周视图数据总数:', this.scheduleList.length);
          this.scheduleList.forEach((item, index) => {
            console.log(`排班${index + 1}:`, {
              scheduleId: item.scheduleId,
              className: item.className,
              courseName: item.courseName, 
              teacherName: item.teacherName,
              dayOfWeek: item.dayOfWeek,
              startTime: item.startTime,
              endTime: item.endTime,
              dayName: item.dayName,
              timeRange: item.timeRange
            });
          });
          
          this.total = this.scheduleList.length;
          this.loading = false;
          
          // 调用数据验证
          this.$nextTick(() => {
            this.validateScheduleData();
          });
        }).catch(error => {
          console.error('周视图查询失败:', error);
          this.loading = false;
        });
      } else {
        // 列表视图：使用分页查询
        listScheduleView(this.queryParams).then(response => {
          console.log('列表视图查询返回数据:', response);
          console.log('第一条数据结构:', response.rows && response.rows[0]);
          this.scheduleList = response.rows;
          this.total = response.total;
          this.loading = false;
        }).catch(error => {
          console.error('列表视图查询失败:', error);
          // 如果视图查询失败，则使用原来的查询
          console.log('尝试使用原始查询接口...');
          listSchedule(this.queryParams).then(response => {
            console.log('原始查询返回数据:', response);
            this.scheduleList = response.rows;
            this.total = response.total;
            this.loading = false;
          });
        });
      }
    },
    /** 获取基础数据 */
    getBasicData() {
      // 获取班级列表
      listAllClass().then(response => {
        this.classList = response.data;
      });
      // 获取教师列表
      listAllTeacher().then(response => {
        this.teacherList = response.data;
      });
      // 获取课程列表
      listAllCourse().then(response => {
        this.courseList = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        scheduleId: null,
        classId: null,
        courseId: null,
        teacherId: null,
        dayOfWeek: null,
        startTime: null,
        endTime: null,
        effectiveDate: null,
        expiryDate: null,
        isActive: 1,
        scheduleType: 'regular',
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.scheduleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加课程排班";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const scheduleId = row.scheduleId || this.ids;
      getSchedule(scheduleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改课程排班";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.scheduleId != null) {
            updateSchedule(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSchedule(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const scheduleIds = row.scheduleId || this.ids;
      this.$confirm('是否确认删除课程排班编号为"' + scheduleIds + '"的数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return delSchedule(scheduleIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 复制排班 */
    handleCopy(row) {
      this.form = { ...row };
      this.form.scheduleId = null;
      this.open = true;
      this.title = "复制课程排班";
    },
    /** 周视图中根据星期和时间获取排班 */
    getScheduleByDayTime(dayOfWeek, timeSlot) {
      console.log(`查找排班: 星期${dayOfWeek}, 时间${timeSlot}`);
      console.log('当前所有排班数据:', this.scheduleList.length, '条');
      
      const matchedSchedules = this.scheduleList.filter(schedule => {
        // 检查星期是否匹配
        const dayMatch = schedule.dayOfWeek === dayOfWeek;
        
        // 灵活的时间匹配逻辑
        let timeMatch = false;
        if (typeof schedule.startTime === 'string') {
          const startHour = schedule.startTime.substring(0, 5); // 取前5位 "HH:mm"
          const scheduleHour = parseInt(schedule.startTime.substring(0, 2)); // 取小时数
          const timeSlotHour = parseInt(timeSlot.substring(0, 2)); // 时间槽小时数
          
          // 精确匹配
          if (startHour === timeSlot) {
            timeMatch = true;
          }
          // 灵活匹配：如果课程开始时间在该时间槽范围内
          else if (scheduleHour === timeSlotHour) {
            timeMatch = true;
          }
          // 跨时间槽匹配：如果课程跨越了该时间槽
          else if (schedule.endTime) {
            const endHour = parseInt(schedule.endTime.substring(0, 2));
            if (scheduleHour <= timeSlotHour && timeSlotHour < endHour) {
              timeMatch = true;
            }
          }
        } else if (schedule.startTime instanceof Date) {
          const hours = schedule.startTime.getHours();
          const minutes = schedule.startTime.getMinutes();
          const startHour = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          timeMatch = startHour === timeSlot || hours === parseInt(timeSlot.substring(0, 2));
        }
        
        console.log(`  比较排班[${schedule.scheduleId}]: 星期${schedule.dayOfWeek}(${dayMatch}) 时间${schedule.startTime}(${timeMatch}) - ${schedule.courseName}`);
        
        return dayMatch && timeMatch;
      });
      
      console.log(`  找到${matchedSchedules.length}个匹配的排班`);
      return matchedSchedules;
    },
    /** 获取排班项的样式类 */
    getScheduleClass(schedule) {
      const classes = ['schedule-item'];
      if (schedule.scheduleType === 'substitute') {
        classes.push('substitute-schedule');
      } else if (schedule.scheduleType === 'makeup') {
        classes.push('makeup-schedule');
      }
      return classes.join(' ');
    },
    /** 编辑排班项 */
    handleEdit(schedule) {
      this.handleUpdate(schedule);
    },
    /** 验证数据结构 */
    validateScheduleData() {
      console.log('=== 数据验证分析 ===');
      console.log('视图模式:', this.viewMode);
      console.log('排班数据总数:', this.scheduleList.length);
      
      if (this.scheduleList.length > 0) {
        const sample = this.scheduleList[0];
        console.log('示例数据结构:', {
          scheduleId: sample.scheduleId,
          dayOfWeek: sample.dayOfWeek + ' (类型: ' + typeof sample.dayOfWeek + ')',
          startTime: sample.startTime + ' (类型: ' + typeof sample.startTime + ')',
          endTime: sample.endTime,
          className: sample.className,
          courseName: sample.courseName,
          teacherName: sample.teacherName,
          dayName: sample.dayName,
          timeRange: sample.timeRange
        });
      }
      
      // 验证时间槽匹配
      console.log('定义的时间槽:', this.timeSlots.map(t => t.value));
      
      // 统计各时间段的课程数量
      const timeStats = {};
      this.timeSlots.forEach(timeSlot => {
        this.weekDays.forEach(day => {
          const count = this.getScheduleByDayTime(day.value, timeSlot.value).length;
          if (count > 0) {
            timeStats[`${day.label}-${timeSlot.value}`] = count;
          }
        });
      });
      console.log('各时间段课程统计:', timeStats);
    }
  }
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.schedule-card {
  margin-bottom: 20px;
}

.week-schedule {
  overflow-x: auto;
}

.schedule-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  border: 1px solid #ddd;
}

.schedule-table th,
.schedule-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
  vertical-align: top;
}

.time-header,
.day-header {
  background-color: #f5f7fa;
  font-weight: bold;
  min-width: 100px;
}

.time-cell {
  background-color: #fafafa;
  font-weight: bold;
  width: 80px;
}

.schedule-cell {
  height: 80px;
  width: 120px;
  position: relative;
  background-color: #fff;
}

.schedule-item {
  margin: 2px;
  padding: 4px;
  background-color: #e7f3ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  line-height: 1.2;
  position: relative;
}

.schedule-item:hover {
  background-color: #d1ecff;
}

.substitute-schedule {
  background-color: #fff7e6;
  border-color: #ffd591;
}

.makeup-schedule {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.schedule-title {
  font-weight: bold;
  color: #1890ff;
}

.schedule-info {
  color: #666;
  margin-top: 2px;
}

.schedule-teacher {
  color: #999;
  font-size: 11px;
}

.schedule-time {
  color: #999;
  font-size: 11px;
}

.schedule-actions {
  position: absolute;
  top: 2px;
  right: 2px;
  opacity: 0;
  transition: opacity 0.2s;
}

.schedule-item:hover .schedule-actions {
  opacity: 1;
}

.delete-btn {
  padding: 2px 4px !important;
  min-height: auto !important;
  font-size: 12px !important;
  color: #f56c6c !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 2px !important;
}

.delete-btn:hover {
  background-color: #f56c6c !important;
  color: white !important;
}

.table-card {
  margin-bottom: 20px;
}
</style>
