-- 数据库迁移脚本：为kg_course表添加default_teacher_id字段
-- 执行时间：2025-08-07
-- 目的：修复课程管理中授课教师不显示的问题

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'kg_course' 
         AND COLUMN_NAME = 'default_teacher_id') = 0,
        'ALTER TABLE kg_course ADD COLUMN default_teacher_id bigint DEFAULT NULL COMMENT ''默认授课教师ID，关联kg_teacher.teacher_id'' AFTER max_students',
        'SELECT ''字段default_teacher_id已存在，跳过添加'' as message'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果字段存在且索引不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'kg_course' 
         AND INDEX_NAME = 'idx_default_teacher_id') = 0
        AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE TABLE_SCHEMA = DATABASE() 
             AND TABLE_NAME = 'kg_course' 
             AND COLUMN_NAME = 'default_teacher_id') = 1,
        'ALTER TABLE kg_course ADD INDEX idx_default_teacher_id (default_teacher_id)',
        'SELECT ''索引idx_default_teacher_id已存在或字段不存在，跳过添加'' as message'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'kg_course' 
AND COLUMN_NAME = 'default_teacher_id';

-- 显示完成信息
SELECT '数据库迁移完成：kg_course表已添加default_teacher_id字段' as result;
