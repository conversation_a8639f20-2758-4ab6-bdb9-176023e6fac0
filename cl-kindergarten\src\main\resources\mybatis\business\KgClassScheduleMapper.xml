<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgClassScheduleMapper">
    
    <resultMap type="KgClassSchedule" id="KgClassScheduleResult">
        <result property="scheduleId"    column="schedule_id"    />
        <result property="classId"    column="class_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="dayOfWeek"    column="day_of_week"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="expiryDate"    column="expiry_date"    />
        <result property="isActive"    column="is_active"    />
        <result property="scheduleType"    column="schedule_type"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgClassScheduleVo">
        select schedule_id, class_id, course_id, teacher_id, day_of_week, start_time, end_time, effective_date, expiry_date, is_active, schedule_type, com_id, create_by, create_time, update_by, update_time, remark from kg_class_schedule
    </sql>

    <select id="selectKgClassScheduleList" parameterType="KgClassSchedule" resultMap="KgClassScheduleResult">
        <include refid="selectKgClassScheduleVo"/>
        <where>  
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="dayOfWeek != null "> and day_of_week = #{dayOfWeek}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="expiryDate != null "> and expiry_date = #{expiryDate}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="scheduleType != null  and scheduleType != ''"> and schedule_type = #{scheduleType}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgClassScheduleById" parameterType="Long" resultMap="KgClassScheduleResult">
        <include refid="selectKgClassScheduleVo"/>
        where schedule_id = #{scheduleId}
    </select>
        
    <insert id="insertKgClassSchedule" parameterType="KgClassSchedule" useGeneratedKeys="true" keyProperty="scheduleId">
        insert into kg_class_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classId != null">class_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="dayOfWeek != null">day_of_week,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="isActive != null">is_active,</if>
            <if test="scheduleType != null">schedule_type,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classId != null">#{classId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="dayOfWeek != null">#{dayOfWeek},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="scheduleType != null">#{scheduleType},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgClassSchedule" parameterType="KgClassSchedule">
        update kg_class_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="classId != null">class_id = #{classId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="dayOfWeek != null">day_of_week = #{dayOfWeek},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="scheduleType != null">schedule_type = #{scheduleType},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where schedule_id = #{scheduleId}
    </update>

    <delete id="deleteKgClassScheduleById" parameterType="Long">
        delete from kg_class_schedule where schedule_id = #{scheduleId}
    </delete>

    <delete id="deleteKgClassScheduleByIds" parameterType="String">
        delete from kg_class_schedule where schedule_id in 
        <foreach item="scheduleId" collection="array" open="(" separator="," close=")">
            #{scheduleId}
        </foreach>
    </delete>
    
    <!-- ==================== 视图查询 ==================== -->
    
    <!-- 从视图查询排班信息列表 -->
    <select id="selectScheduleViewList" parameterType="KgClassSchedule" resultType="java.util.Map">
        SELECT * FROM v_class_schedule_info
        <where>
            <if test="classId != null">AND classId = #{classId}</if>
            <if test="courseId != null">AND courseId = #{courseId}</if>
            <if test="teacherId != null">AND teacherId = #{teacherId}</if>
            <if test="dayOfWeek != null">AND dayOfWeek = #{dayOfWeek}</if>
            <if test="scheduleType != null and scheduleType != ''">AND scheduleType = #{scheduleType}</if>
        </where>
        ORDER BY dayOfWeek, startTime
    </select>
    
    <!-- 从视图查询单个排班信息 -->
    <select id="selectScheduleViewById" parameterType="Long" resultType="java.util.Map">
        SELECT * FROM v_class_schedule_info
        WHERE scheduleId = #{scheduleId}
    </select>
    
    <!-- 根据教师ID从视图查询排班 -->
    <select id="selectScheduleViewByTeacherId" parameterType="Long" resultType="java.util.Map">
        SELECT * FROM v_class_schedule_info
        WHERE teacherId = #{teacherId}
        ORDER BY dayOfWeek, startTime
    </select>
    
    <!-- 根据班级ID从视图查询课表 -->
    <select id="selectScheduleViewByClassId" parameterType="Long" resultType="java.util.Map">
        SELECT * FROM v_class_schedule_info
        WHERE classId = #{classId}
        ORDER BY dayOfWeek, startTime
    </select>
    
    <!-- 根据排班信息查询对应的考勤记录 -->
    <select id="getAttendanceBySchedule" resultType="java.util.Map">
        SELECT 
            a.attendance_id as attendanceId,
            a.enrollment_id as enrollmentId,
            a.student_id as studentId,
            a.course_id as courseId,
            a.teacher_id as teacherId,
            a.attendance_date as attendanceDate,
            a.start_time as startTime,
            a.end_time as endTime,
            a.attendance_status as attendanceStatus,
            a.check_in_method as checkInMethod,
            a.is_confirmed as isConfirmed,
            a.confirmed_by as confirmedBy,
            a.confirmed_time as confirmedTime,
            s.student_name as studentName,
            s.phone as studentPhone,
            c.course_name as courseName,
            cl.class_name as className,
            t.teacher_name as teacherName,
            CASE 
                WHEN a.attendance_status = 'present' AND a.is_confirmed = 1 THEN '已签到'
                WHEN a.attendance_status = 'present' AND a.is_confirmed = 0 THEN '待确认'  
                WHEN a.attendance_status = 'absent' THEN '缺勤'
                WHEN a.attendance_status = 'late' THEN '迟到'
                WHEN a.attendance_status = 'early' THEN '早退'
                ELSE '未知'
            END as statusDisplay
        FROM kg_course_attendance a
        JOIN kg_course_enrollment e ON a.enrollment_id = e.enrollment_id
        JOIN kg_student s ON e.student_id = s.student_id
        JOIN kg_course c ON a.course_id = c.course_id
        JOIN kg_class cl ON e.class_id = cl.class_id  
        JOIN kg_teacher t ON a.teacher_id = t.teacher_id
        WHERE EXISTS (
            SELECT 1 FROM kg_class_schedule sch 
            WHERE sch.schedule_id = #{scheduleId}
              AND e.class_id = sch.class_id
              AND a.course_id = sch.course_id
              AND a.teacher_id = sch.teacher_id
        )
        <if test="attendanceDate != null and attendanceDate != ''">
            AND DATE(a.attendance_date) = #{attendanceDate}
        </if>
        <if test="attendanceDate == null or attendanceDate == ''">
            AND a.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        ORDER BY a.attendance_date DESC, s.student_name
    </select>
    
    <!-- 根据排班信息查询对应的学生名单 -->
    <select id="getStudentsBySchedule" parameterType="Long" resultType="java.util.Map">
        SELECT DISTINCT
            s.student_id as studentId,
            s.student_name as studentName,
            s.phone as phone,
            s.parent_name as parentName,
            s.parent_phone as parentPhone,
            s.gender as gender,
            e.enrollment_id as enrollmentId,
            e.total_sessions as totalSessions,
            e.used_sessions as usedSessions,
            e.remaining_sessions as remainingSessions,
            e.enrollment_date as enrollmentDate,
            e.status as enrollmentStatus,
            c.course_name as courseName,
            cl.class_name as className,
            t.teacher_name as teacherName,
            sch.day_of_week as dayOfWeek,
            sch.start_time as startTime,
            sch.end_time as endTime
        FROM kg_class_schedule sch
        JOIN kg_course_enrollment e ON sch.class_id = e.class_id 
                                    AND sch.course_id = e.course_id
        JOIN kg_student s ON e.student_id = s.student_id
        JOIN kg_course c ON sch.course_id = c.course_id
        JOIN kg_class cl ON sch.class_id = cl.class_id  
        JOIN kg_teacher t ON sch.teacher_id = t.teacher_id
        WHERE sch.schedule_id = #{scheduleId}
          AND sch.is_active = 1
          AND e.status = 'active'
        ORDER BY s.student_name
    </select>
    
    <!-- 根据排班生成考勤记录模板 -->
    <insert id="generateAttendanceTemplate">
        INSERT INTO kg_course_attendance (
            enrollment_id,
            student_id,
            course_id,
            teacher_id,
            attendance_date,
            start_time,
            end_time,
            attendance_status,
            check_in_method,
            is_confirmed,
            com_id,
            create_by,
            create_time
        )
        SELECT DISTINCT
            e.enrollment_id,
            e.student_id,
            sch.course_id,
            sch.teacher_id,
            #{attendanceDate} as attendance_date,
            CONCAT(#{attendanceDate}, ' ', sch.start_time) as start_time,
            CONCAT(#{attendanceDate}, ' ', sch.end_time) as end_time,
            'present' as attendance_status,
            'manual' as check_in_method,
            0 as is_confirmed,
            sch.com_id,
            'system' as create_by,
            NOW() as create_time
        FROM kg_class_schedule sch
        JOIN kg_course_enrollment e ON sch.class_id = e.class_id 
                                    AND sch.course_id = e.course_id
        WHERE sch.schedule_id = #{scheduleId}
          AND sch.is_active = 1
          AND e.status = 'active'
          AND DAYOFWEEK(#{attendanceDate}) = sch.day_of_week + 1
          AND NOT EXISTS (
              SELECT 1 FROM kg_course_attendance a 
              WHERE a.enrollment_id = e.enrollment_id 
                AND a.attendance_date = #{attendanceDate}
                AND a.course_id = sch.course_id
                AND a.teacher_id = sch.teacher_id
          )
    </insert>
    
    <!-- 批量生成考勤记录模板 -->
    <insert id="batchGenerateAttendanceTemplate">
        INSERT INTO kg_course_attendance (
            enrollment_id,
            student_id,
            course_id,
            teacher_id,
            attendance_date,
            start_time,
            end_time,
            attendance_status,
            check_in_method,
            is_confirmed,
            com_id,
            create_by,
            create_time
        )
        SELECT DISTINCT
            e.enrollment_id,
            e.student_id,
            sch.course_id,
            sch.teacher_id,
            date_table.date_value as attendance_date,
            CONCAT(date_table.date_value, ' ', sch.start_time) as start_time,
            CONCAT(date_table.date_value, ' ', sch.end_time) as end_time,
            'present' as attendance_status,
            'manual' as check_in_method,
            0 as is_confirmed,
            sch.com_id,
            'system' as create_by,
            NOW() as create_time
        FROM kg_class_schedule sch
        JOIN kg_course_enrollment e ON sch.class_id = e.class_id 
                                    AND sch.course_id = e.course_id
        CROSS JOIN (
            SELECT DATE_ADD(#{startDate}, INTERVAL seq.seq DAY) as date_value
            FROM (
                SELECT 0 as seq UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 
                UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13
                UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
                UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23 UNION ALL SELECT 24 UNION ALL SELECT 25 UNION ALL SELECT 26 UNION ALL SELECT 27
                UNION ALL SELECT 28 UNION ALL SELECT 29 UNION ALL SELECT 30 UNION ALL SELECT 31 UNION ALL SELECT 32 UNION ALL SELECT 33 UNION ALL SELECT 34
                UNION ALL SELECT 35 UNION ALL SELECT 36 UNION ALL SELECT 37 UNION ALL SELECT 38 UNION ALL SELECT 39 UNION ALL SELECT 40 UNION ALL SELECT 41
                UNION ALL SELECT 42 UNION ALL SELECT 43 UNION ALL SELECT 44 UNION ALL SELECT 45 UNION ALL SELECT 46 UNION ALL SELECT 47 UNION ALL SELECT 48
                UNION ALL SELECT 49 UNION ALL SELECT 50 UNION ALL SELECT 51 UNION ALL SELECT 52 UNION ALL SELECT 53 UNION ALL SELECT 54 UNION ALL SELECT 55
                UNION ALL SELECT 56 UNION ALL SELECT 57 UNION ALL SELECT 58 UNION ALL SELECT 59 UNION ALL SELECT 60 UNION ALL SELECT 61 UNION ALL SELECT 62
                UNION ALL SELECT 63 UNION ALL SELECT 64 UNION ALL SELECT 65 UNION ALL SELECT 66 UNION ALL SELECT 67 UNION ALL SELECT 68 UNION ALL SELECT 69
            ) seq
            WHERE DATE_ADD(#{startDate}, INTERVAL seq.seq DAY) &lt;= #{endDate}
        ) date_table
        WHERE sch.schedule_id = #{scheduleId}
          AND sch.is_active = 1
          AND e.status = 'active'
          AND DAYOFWEEK(date_table.date_value) = sch.day_of_week + 1
          AND NOT EXISTS (
              SELECT 1 FROM kg_course_attendance a 
              WHERE a.enrollment_id = e.enrollment_id 
                AND a.attendance_date = date_table.date_value
                AND a.course_id = sch.course_id
                AND a.teacher_id = sch.teacher_id
          )
    </insert>
    
    <!-- 学生签到 -->
    <update id="studentCheckIn">
        UPDATE kg_course_attendance 
        SET 
            attendance_status = #{attendanceStatus},
            check_in_method = #{checkInMethod},
            update_time = NOW(),
            update_by = 'student'
        WHERE attendance_id = #{attendanceId}
          AND is_confirmed = 0
    </update>
    
    <!-- 管理员确认考勤 -->
    <update id="confirmAttendance">
        UPDATE kg_course_attendance 
        SET 
            attendance_status = #{finalStatus},
            is_confirmed = 1,
            confirmed_by = #{confirmedBy},
            confirmed_time = NOW(),
            update_time = NOW(),
            update_by = 'admin'
        WHERE attendance_id = #{attendanceId}
    </update>
    
    <!-- 批量确认考勤 -->
    <update id="batchConfirmAttendance">
        UPDATE kg_course_attendance 
        SET 
            is_confirmed = 1,
            confirmed_by = #{confirmedBy},
            confirmed_time = NOW(),
            update_time = NOW(),
            update_by = 'admin'
        WHERE attendance_id IN
        <foreach collection="attendanceIds" item="attendanceId" open="(" separator="," close=")">
            #{attendanceId}
        </foreach>
          AND is_confirmed = 0
    </update>
    
</mapper>