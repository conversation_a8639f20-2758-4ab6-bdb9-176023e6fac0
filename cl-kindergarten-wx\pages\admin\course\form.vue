<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">{{ isEdit ? '编辑' : '新增' }}课程</text>
				</view>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<view class="form-section">
				<view class="section-title">基本信息</view>
				
				<view class="form-item">
					<text class="label required">课程名称</text>
					<input 
						class="input" 
						v-model="form.courseName" 
						placeholder="请输入课程名称"
						@blur="validateField('courseName')"
					/>
				</view>
				
				<view class="form-item">
					<text class="label required">课程类型</text>
					<picker @change="onCourseTypeChange" :value="courseTypeIndex" :range="courseTypeOptions">
						<view class="picker-value">
							{{ courseTypeIndex >= 0 ? courseTypeOptions[courseTypeIndex] : '请选择课程类型' }}
							<u-icon name="arrow-down" size="14" color="#999"></u-icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="label required">单节课价格</text>
					<view class="input-with-unit">
						<input 
							class="input" 
							type="digit" 
							v-model="form.pricePerSession" 
							placeholder="请输入单节课价格"
							@blur="validateField('pricePerSession')"
						/>
						<text class="unit">元</text>
					</view>
				</view>
				
				<view class="form-item">
					<text class="label required">课程时长</text>
					<view class="input-with-unit">
						<input 
							class="input" 
							type="number" 
							v-model="form.duration" 
							placeholder="请输入课程时长"
							@blur="validateField('duration')"
						/>
						<text class="unit">分钟</text>
					</view>
				</view>
				
				<view class="form-item">
					<text class="label required">最少开班人数</text>
					<view class="input-with-unit">
						<input 
							class="input" 
							type="number" 
							v-model="form.minStudents" 
							placeholder="请输入最少开班人数"
							@blur="validateField('minStudents')"
						/>
						<text class="unit">人</text>
					</view>
				</view>
				
				<view class="form-item">
					<text class="label required">最大班级人数</text>
					<view class="input-with-unit">
						<input 
							class="input" 
							type="number" 
							v-model="form.maxStudents" 
							placeholder="请输入最大班级人数"
							@blur="validateField('maxStudents')"
						/>
						<text class="unit">人</text>
					</view>
				</view>
				
				<view class="form-item">
					<text class="label">授课教师</text>
					<picker @change="onTeacherChange" :value="teacherIndex" :range="teacherOptions" range-key="teacherName">
						<view class="picker-value">
							{{ teacherIndex >= 0 ? teacherOptions[teacherIndex].teacherName : '请选择授课教师' }}
							<u-icon name="arrow-down" size="14" color="#999"></u-icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="label">状态</text>
					<view class="radio-group">
						<label class="radio-item" @click="form.status = '0'">
							<radio :checked="form.status === '0'" color="#667eea" />
							<text>正常</text>
						</label>
						<label class="radio-item" @click="form.status = '1'">
							<radio :checked="form.status === '1'" color="#667eea" />
							<text>停用</text>
						</label>
					</view>
				</view>
				
				<view class="form-item">
					<text class="label">备注</text>
					<textarea 
						class="textarea" 
						v-model="form.remark" 
						placeholder="请输入备注信息"
						maxlength="200"
					></textarea>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<button class="btn-cancel" @click="goBack">取消</button>
			<button class="btn-submit" @click="handleSubmit" :disabled="submitting">
				{{ submitting ? '提交中...' : '保存' }}
			</button>
		</view>
	</view>
</template>

<script>
import {toast} from '@/utils/utils.js'
import {
	getCourse,
	addCourse,
	updateCourse
} from '@/api/course.js'
import {
	listAllTeacher
} from '@/api/teacher.js'

export default {
	data() {
		return {
			// 页面类型：add新增，edit编辑
			type: 'add',
			// 课程ID（编辑时使用）
			courseId: null,
			// 是否编辑模式
			isEdit: false,
			// 表单数据
			form: {
				courseName: '',
				courseType: '',
				pricePerSession: '',
				duration: '',
				minStudents: '',
				maxStudents: '',
				defaultTeacherId: '',
				status: '0',
				remark: ''
			},
			// 课程类型选项
			courseTypeOptions: ['托管课程', '兴趣班', '特色课程'],
			courseTypeIndex: -1,
			// 教师选项
			teacherOptions: [],
			teacherIndex: -1,
			// 提交状态
			submitting: false,
			// 表单验证错误
			errors: {}
		}
	},
	
	onLoad(options) {
		this.type = options.type || 'add'
		this.isEdit = this.type === 'edit'
		this.courseId = options.courseId
		
		this.getTeacherList()
		
		if (this.isEdit && this.courseId) {
			this.getCourseDetail()
		}
	},
	
	methods: {
		// 返回上一页
		goBack() {
			const pages = getCurrentPages()
			if (pages.length > 1) {
				uni.navigateBack()
			} else {
				uni.redirectTo({
					url: '/pages/admin/course/index'
				})
			}
		},
		
		// 获取教师列表
		async getTeacherList() {
			try {
				const res = await listAllTeacher()
				if (res && res.code === 200) {
					this.teacherOptions = res.data || []
				}
			} catch (error) {
				console.error('获取教师列表失败:', error)
			}
		},
		
		// 获取课程详情（编辑时）
		async getCourseDetail() {
			try {
				const res = await getCourse(this.courseId)
				if (res.code === 200) {
					this.form = { ...res.data }
					// 设置课程类型选择器
					const typeMap = { '1': 0, '2': 1, '3': 2 }
					this.courseTypeIndex = typeMap[this.form.courseType] ?? -1
					// 设置教师选择器
					const teacherIndex = this.teacherOptions.findIndex(t => t.teacherId === this.form.defaultTeacherId)
					this.teacherIndex = teacherIndex >= 0 ? teacherIndex : -1
				} else {
					toast(res.msg || '获取课程详情失败')
					this.goBack()
				}
			} catch (error) {
				console.error('获取课程详情失败:', error)
				toast('获取课程详情失败')
				this.goBack()
			}
		},
		
		// 课程类型选择
		onCourseTypeChange(e) {
			this.courseTypeIndex = e.detail.value
			// 映射课程类型值
			const typeMap = { 0: '1', 1: '2', 2: '3' }
			this.form.courseType = typeMap[this.courseTypeIndex]
			this.validateField('courseType')
		},
		
		// 教师选择
		onTeacherChange(e) {
			this.teacherIndex = e.detail.value
			this.form.defaultTeacherId = this.teacherOptions[this.teacherIndex]?.teacherId || ''
		},
		
		// 字段验证
		validateField(field) {
			const errors = { ...this.errors }
			
			switch (field) {
				case 'courseName':
					if (!this.form.courseName.trim()) {
						errors.courseName = '请输入课程名称'
					} else {
						delete errors.courseName
					}
					break
				case 'courseType':
					if (!this.form.courseType) {
						errors.courseType = '请选择课程类型'
					} else {
						delete errors.courseType
					}
					break
				case 'pricePerSession':
					if (!this.form.pricePerSession) {
						errors.pricePerSession = '请输入单节课价格'
					} else if (isNaN(this.form.pricePerSession) || Number(this.form.pricePerSession) < 0) {
						errors.pricePerSession = '请输入有效的价格'
					} else {
						delete errors.pricePerSession
					}
					break
				case 'duration':
					if (!this.form.duration) {
						errors.duration = '请输入课程时长'
					} else if (isNaN(this.form.duration) || Number(this.form.duration) <= 0) {
						errors.duration = '请输入有效的时长'
					} else {
						delete errors.duration
					}
					break
				case 'minStudents':
					if (!this.form.minStudents) {
						errors.minStudents = '请输入最少开班人数'
					} else if (isNaN(this.form.minStudents) || Number(this.form.minStudents) <= 0) {
						errors.minStudents = '请输入有效的人数'
					} else {
						delete errors.minStudents
					}
					break
				case 'maxStudents':
					if (!this.form.maxStudents) {
						errors.maxStudents = '请输入最大班级人数'
					} else if (isNaN(this.form.maxStudents) || Number(this.form.maxStudents) <= 0) {
						errors.maxStudents = '请输入有效的人数'
					} else if (this.form.minStudents && Number(this.form.maxStudents) < Number(this.form.minStudents)) {
						errors.maxStudents = '最大人数不能小于最少人数'
					} else {
						delete errors.maxStudents
					}
					break
			}
			
			this.errors = errors
		},
		
		// 表单验证
		validateForm() {
			// 验证所有必填字段
			this.validateField('courseName')
			this.validateField('courseType')
			this.validateField('pricePerSession')
			this.validateField('duration')
			this.validateField('minStudents')
			this.validateField('maxStudents')
			
			// 检查是否有错误
			const errorKeys = Object.keys(this.errors)
			if (errorKeys.length > 0) {
				toast(this.errors[errorKeys[0]])
				return false
			}
			
			return true
		},
		
		// 提交表单
		async handleSubmit() {
			if (!this.validateForm()) {
				return
			}
			
			try {
				this.submitting = true
				
				const api = this.isEdit ? updateCourse : addCourse
				const res = await api(this.form)
				
				if (res.code === 200) {
					toast(this.isEdit ? '编辑成功' : '新增成功')
					// 延迟返回，让用户看到成功提示
					setTimeout(() => {
						this.goBack()
					}, 1500)
				} else {
					toast(res.msg || (this.isEdit ? '编辑失败' : '新增失败'))
				}
			} catch (error) {
				console.error('提交失败:', error)
				toast(this.isEdit ? '编辑失败' : '新增失败')
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 表单容器 */
.form-container {
	padding: 20rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.form-item {
	margin-bottom: 30rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.label {
	display: block;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;

	&.required::after {
		content: '*';
		color: #ff4d4f;
		margin-left: 4rpx;
	}
}

.input-with-unit {
	position: relative;
	display: flex;
	align-items: center;
}

.input {
	flex: 1;
	height: 80rpx;
	padding: 0 60rpx 0 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	box-sizing: border-box;

	&:focus {
		border-color: #667eea;
		background: #ffffff;
	}
}

.textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	box-sizing: border-box;

	&:focus {
		border-color: #667eea;
		background: #ffffff;
	}
}

.picker-value {
	height: 80rpx;
	padding: 0 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.unit {
	position: absolute;
	right: 20rpx;
	font-size: 26rpx;
	color: #999999;
	pointer-events: none;
}

.radio-group {
	display: flex;
	gap: 40rpx;
}

.radio-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	font-size: 28rpx;
	color: #333333;
}

/* 底部按钮 */
.footer-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	display: flex;
	gap: 20rpx;
}

.btn-cancel, .btn-submit {
	flex: 1;
	height: 80rpx;
	border-radius: 8rpx;
	font-size: 32rpx;
	border: none;
}

.btn-cancel {
	background: #f8f9fa;
	color: #666666;
}

.btn-submit {
	background: #667eea;
	color: #ffffff;

	&:disabled {
		background: #cccccc;
		color: #999999;
	}
}
</style>
