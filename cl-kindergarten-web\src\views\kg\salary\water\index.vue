<template>
  <div class="app-container">
    <!-- 页面说明 -->
    <el-alert
      title="工资流水记录查看"
      description="查看已生成的工资记录及发放状态，工资计算和确认发放请前往“工资计算”页面操作"
      type="info"
      :closable="false"
      class="mb20">
    </el-alert>
    
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="工资年月" prop="salaryMonth">
        <el-date-picker
          v-model="queryDate"
          type="month"
          placeholder="选择年月"
          format="YYYY-MM"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="教师" prop="teacherId">
        <el-select v-model="queryParams.teacherId" placeholder="请选择教师" clearable filterable>
          <el-option
            v-for="teacher in teacherOptions"
            :key="teacher.teacherId"
            :label="teacher.teacherName"
            :value="teacher.teacherId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="工资状态" prop="salaryStatus">
        <el-select v-model="queryParams.salaryStatus" placeholder="请选择状态" clearable>
          <el-option label="已计算" value="calculated" />
          <el-option label="已确认" value="confirmed" />
          <el-option label="已发放" value="paid" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Download"
          @click="handleExport"
        >导出记录</el-button>
      </el-col>
      <RightToolbar :showSearch.sync="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <el-table v-loading="loading" :data="salaryList">
      <el-table-column label="工资编号" align="center" prop="salaryId" width="100" />
      <el-table-column label="教师姓名" align="center" prop="teacherName" width="120" />
      <el-table-column label="教师工号" align="center" prop="teacherCode" width="200">
        <template #default="scope">
          <span style="color: #67c23a; font-weight: 600;">{{ scope.row.teacherCode || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工资年月" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.salaryYear }}-{{ String(scope.row.salaryMonth).padStart(2, '0') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="基本工资" align="center" prop="baseSalary" width="100">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.baseSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时费" align="center" prop="courseBonus" width="100">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.courseBonus }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应发工资" align="center" prop="grossSalary" width="120">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.grossSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实发工资" align="center" prop="netSalary" width="120">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.netSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工资状态" align="center" prop="salaryStatus" width="100">
        <template #default="scope">
          <el-tag :type="getSalaryStatusType(scope.row.salaryStatus)">
            {{ getSalaryStatusText(scope.row.salaryStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="确认时间" align="center" prop="confirmedTime" />
      <el-table-column label="发放时间" align="center" prop="paidTime" />
      <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewDetail(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 工资详情对话框 -->
    <el-dialog 
      title="工资详情查看" 
      :visible.sync="detailDialogVisible" 
      width="900px"
      :before-close="() => detailDialogVisible = false"
    >
      <div v-if="salaryDetail" class="salary-detail-container">
        <!-- 基本信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-user"></i>
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">教师姓名</div>
                <div class="info-value teacher-name">{{ salaryDetail.teacherName || '未知' }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">教师工号</div>
                <div class="info-value teacher-code">{{ salaryDetail.teacherCode || '无' }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">工资年月</div>
                <div class="info-value">{{ salaryDetail.salaryYear }}年{{ String(salaryDetail.salaryMonth).padStart(2, '0') }}月</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">工资状态</div>
                <div class="info-value">
                  <el-tag :type="getSalaryStatusType(salaryDetail.salaryStatus)" size="medium">
                    {{ getSalaryStatusText(salaryDetail.salaryStatus) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 工资汇总卡片 -->
        <el-card class="detail-card summary-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-s-finance"></i>
            <span>工资汇总</span>
          </div>
          <el-row :gutter="20" class="summary-row">
            <el-col :span="8">
              <div class="summary-item gross-salary">
                <div class="summary-icon">
                  <i class="el-icon-wallet"></i>
                </div>
                <div class="summary-content">
                  <div class="summary-label">应发工资</div>
                  <div class="summary-value">￥{{ (salaryDetail.grossSalary || 0).toLocaleString() }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item net-salary">
                <div class="summary-icon">
                  <i class="el-icon-money"></i>
                </div>
                <div class="summary-content">
                  <div class="summary-label">实发工资</div>
                  <div class="summary-value main-amount">￥{{ (salaryDetail.netSalary || 0).toLocaleString() }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item attendance">
                <div class="summary-icon">
                  <i class="el-icon-date"></i>
                </div>
                <div class="summary-content">
                  <div class="summary-label">出勤天数</div>
                  <div class="summary-value">{{ salaryDetail.attendanceDays || 0 }}天</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <el-row :gutter="20">
          <!-- 工资构成卡片 -->
          <el-col :span="12">
            <el-card class="detail-card income-card" shadow="never">
              <div slot="header" class="card-header">
                <i class="el-icon-circle-plus"></i>
                <span>工资构成</span>
              </div>
              <div class="salary-items">
                <div class="salary-item" v-if="salaryDetail.baseSalary">
                  <div class="item-label">
                    <i class="el-icon-coin"></i>
                    基本工资
                  </div>
                  <div class="item-value income">￥{{ (salaryDetail.baseSalary || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.attendanceBonus">
                  <div class="item-label">
                    <i class="el-icon-medal-1"></i>
                    满勤奖
                  </div>
                  <div class="item-value income">￥{{ (salaryDetail.attendanceBonus || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.courseBonus">
                  <div class="item-label">
                    <i class="el-icon-reading"></i>
                    课时费
                  </div>
                  <div class="item-value income">￥{{ (salaryDetail.courseBonus || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.enrollmentBonus">
                  <div class="item-label">
                    <i class="el-icon-user-solid"></i>
                    报名奖励
                  </div>
                  <div class="item-value income">￥{{ (salaryDetail.enrollmentBonus || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.attendanceRateBonus">
                  <div class="item-label">
                    <i class="el-icon-trophy"></i>
                    出勤率奖励
                  </div>
                  <div class="item-value income">￥{{ (salaryDetail.attendanceRateBonus || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.newStudentBonus">
                  <div class="item-label">
                    <i class="el-icon-star-on"></i>
                    新生奖励
                  </div>
                  <div class="item-value income">￥{{ (salaryDetail.newStudentBonus || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.otherBonus">
                  <div class="item-label">
                    <i class="el-icon-plus"></i>
                    其他奖励
                  </div>
                  <div class="item-value income">￥{{ (salaryDetail.otherBonus || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.performanceScore">
                  <div class="item-label">
                    <i class="el-icon-data-line"></i>
                    绩效积分
                  </div>
                  <div class="item-value score">{{ salaryDetail.performanceScore || 0 }}分</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 扣除项目卡片 -->
          <el-col :span="12">
            <el-card class="detail-card deduction-card" shadow="never">
              <div slot="header" class="card-header">
                <i class="el-icon-remove"></i>
                <span>扣除项目</span>
              </div>
              <div class="salary-items">
                <div class="salary-item" v-if="salaryDetail.withdrawalPenalty">
                  <div class="item-label">
                    <i class="el-icon-warning"></i>
                    退园扣款
                  </div>
                  <div class="item-value deduction">-￥{{ (salaryDetail.withdrawalPenalty || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.socialInsurance">
                  <div class="item-label">
                    <i class="el-icon-lock"></i>
                    社保代扣
                  </div>
                  <div class="item-value deduction">-￥{{ (salaryDetail.socialInsurance || 0).toLocaleString() }}</div>
                </div>
                <div class="salary-item" v-if="salaryDetail.otherDeduction">
                  <div class="item-label">
                    <i class="el-icon-minus"></i>
                    其他扣款
                  </div>
                  <div class="item-value deduction">-￥{{ (salaryDetail.otherDeduction || 0).toLocaleString() }}</div>
                </div>
                <div class="empty-state" v-if="!salaryDetail.withdrawalPenalty && !salaryDetail.socialInsurance && !salaryDetail.otherDeduction">
                  <i class="el-icon-check"></i>
                  <div>本月无扣除项目</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 记录信息卡片 -->
        <el-card class="detail-card record-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-document"></i>
            <span>记录信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="record-item">
                <div class="record-label">确认人</div>
                <div class="record-value">
                  <i class="el-icon-user"></i>
                  {{ salaryDetail.confirmedByName || '未确认' }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="record-item">
                <div class="record-label">确认时间</div>
                <div class="record-value">
                  <i class="el-icon-time"></i>
                  {{ salaryDetail.confirmedTime || '未确认' }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="record-item">
                <div class="record-label">发放时间</div>
                <div class="record-value">
                  <i class="el-icon-money"></i>
                  {{ salaryDetail.paidTime || '未发放' }}
                </div>
              </div>
            </el-col>
          </el-row>
          <div class="remark-section" v-if="salaryDetail.remark">
            <div class="remark-label">备注</div>
            <div class="remark-value">{{ salaryDetail.remark }}</div>
          </div>
        </el-card>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button size="medium" @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="SalaryRecords">
import { 
  listTeacherSalary, 
  getTeacherSalary
} from "@/api/kg/salary/manage";
import { listAllTeacher } from "@/api/kg/teacher/info";
import Pagination from "@/components/Pagination";

export default {
  name: "SalaryRecords",
  components: {
    Pagination
  },
  data() {
    return {
      // 遮罩层
      loading: true,

      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工资表格数据
      salaryList: [],
      // 教师选项
      teacherOptions: [],

      // 详情对话框
      detailDialogVisible: false,
      // 工资详情
      salaryDetail: {},
      // 查询日期 - 默认设置为当前年月（Date对象）
      queryDate: new Date(),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        teacherId: null,
        salaryYear: new Date().getFullYear(),
        salaryMonth: new Date().getMonth() + 1,
        salaryStatus: null
      }
    };
  },
  created() {
    this.getList();
    this.loadTeacherOptions();
  },
  methods: {
    /** 查询工资列表 */
    getList() {
      this.loading = true;
      console.log('查询参数:', this.queryParams);
      
      listTeacherSalary(this.queryParams).then(response => {
        this.salaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 加载教师选项 */
    loadTeacherOptions() {
      listAllTeacher().then(response => {
        this.teacherOptions = response.data || [];
      });
    },
    /** 日期变化处理（参考工资计算页面的逻辑） */
    handleDateChange(date) {
      console.log('日期变化:', date);
      if (date) {
        this.queryParams.salaryYear = date.getFullYear();
        this.queryParams.salaryMonth = date.getMonth() + 1;
        console.log('解析后年月:', {
          年份: this.queryParams.salaryYear,
          月份: this.queryParams.salaryMonth
        });
      } else {
        this.queryParams.salaryYear = null;
        this.queryParams.salaryMonth = null;
        console.log('清空日期参数');
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置为当前年月（Date对象）
      this.queryDate = new Date();
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        teacherId: null,
        salaryYear: new Date().getFullYear(),
        salaryMonth: new Date().getMonth() + 1,
        salaryStatus: null
      };
      this.handleQuery();
    },

    /** 查看详情 */
    handleViewDetail(row) {
      getTeacherSalary(row.salaryId).then(response => {
        this.salaryDetail = response.data;
        this.detailDialogVisible = true;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('kg/salary/export', {
        ...this.queryParams
      }, `teacher_salary_${new Date().getTime()}.xlsx`);
    },
    /** 获取工资状态类型 */
    getSalaryStatusType(status) {
      const statusMap = {
        'calculated': 'info',
        'confirmed': 'warning',
        'paid': 'success'
      };
      return statusMap[status] || 'info';
    },
    /** 获取工资状态文本 */
    getSalaryStatusText(status) {
      const statusMap = {
        'calculated': '已计算',
        'confirmed': '已确认',
        'paid': '已发放'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.text-price {
  color: #E6A23C;
  font-weight: bold;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}

/* 工资详情对话框样式 */
.salary-detail-container {
  max-height: 80vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
  font-size: 15px;
}

.card-header i {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

/* 基本信息样式 */
.info-item {
  text-align: center;
  padding: 8px;
}

.info-label {
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
}

.info-value {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.teacher-name {
  color: #409eff;
  font-size: 16px;
}

.teacher-code {
  color: #67c23a;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

.salary-id {
  color: #909399;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 工资汇总样式 */
.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.summary-card .card-header {
  color: white;
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

.summary-card .card-header i {
  color: #ffd700;
}

.summary-row {
  padding: 10px 0;
}

.summary-item {
  display: flex;
  align-items: center;
  padding: 20px 16px;
  border-radius: 8px;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.summary-item:hover {
  background: rgba(255,255,255,0.2);
  transform: translateY(-2px);
}

.summary-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255,255,255,0.2);
  margin-right: 16px;
}

.summary-icon i {
  font-size: 24px;
  color: #ffd700;
}

.summary-content {
  flex: 1;
}

.summary-label {
  font-size: 13px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.main-amount {
  font-size: 22px;
  color: #ffd700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 工资项目样式 */
.income-card {
  border-left: 4px solid #67c23a;
}

.deduction-card {
  border-left: 4px solid #f56c6c;
}

.salary-items {
  min-height: 200px;
}

.salary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 6px;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.salary-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.salary-item:last-child {
  margin-bottom: 0;
}

.item-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.item-label i {
  margin-right: 8px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.item-value {
  font-weight: 600;
  font-size: 15px;
}

.income {
  color: #67c23a;
}

.deduction {
  color: #f56c6c;
}

.score {
  color: #e6a23c;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 12px;
  color: #67c23a;
  display: block;
}

/* 记录信息样式 */
.record-card {
  border-left: 4px solid #909399;
}

.record-item {
  text-align: center;
  padding: 16px 8px;
}

.record-label {
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
}

.record-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-value i {
  margin-right: 6px;
  color: #409eff;
}

.remark-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.remark-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 600;
}

.remark-value {
  background: #f5f7fa;
  padding: 12px 16px;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
  border-left: 4px solid #409eff;
}

/* 对话框底部 */
.dialog-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .summary-item {
    flex-direction: column;
    text-align: center;
  }
  
  .summary-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .salary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .item-value {
    align-self: flex-end;
  }
}
</style>
