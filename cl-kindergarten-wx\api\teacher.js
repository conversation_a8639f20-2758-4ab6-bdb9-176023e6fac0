import request from '@/utils/request.js'

/**
 * 教师管理相关API接口
 */

// 查询教师列表
export const listTeacher = (params) => {
  return request.get('/business/teacher/list', params)
}

// 查询教师详情
export const getTeacher = (teacherId) => {
  return request.get(`/business/teacher/${teacherId}`)
}

// 新增教师
export const addTeacher = (data) => {
  return request.post('/business/teacher', data)
}

// 编辑教师
export const updateTeacher = (data) => {
  return request.put('/business/teacher', data)
}

// 删除教师
export const deleteTeacher = (teacherId) => {
  return request.delete(`/business/teacher/${teacherId}`)
}

// 获取所有教师列表（用于选择）
export const listAllTeacher = () => {
  return request.get('/business/teacher/listAll')
}
