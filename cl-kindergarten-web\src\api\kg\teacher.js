import request from '@/utils/request'

// 查询教师列表
export function listTeacher(query) {
  return request({
    url: '/kg/teacher/list',
    method: 'get',
    params: query
  })
}

// 查询教师详细
export function getTeacher(teacherId) {
  return request({
    url: '/kg/teacher/' + teacherId,
    method: 'get'
  })
}

// 新增教师
export function addTeacher(data) {
  return request({
    url: '/kg/teacher',
    method: 'post',
    data: data
  })
}

// 修改教师
export function updateTeacher(data) {
  return request({
    url: '/kg/teacher',
    method: 'put',
    data: data
  })
}

// 删除教师
export function delTeacher(teacherId) {
  return request({
    url: '/kg/teacher/' + teacherId,
    method: 'delete'
  })
}

// 批量删除教师
export function delTeachers(teacherIds) {
  return request({
    url: '/kg/teacher/batch/' + teacherIds,
    method: 'delete'
  })
}

// 获取教师统计信息
export function getTeacherStats(teacherId) {
  return request({
    url: '/kg/teacher/' + teacherId + '/stats',
    method: 'get'
  })
}

// 获取教师任教班级
export function getTeacherClasses(teacherId) {
  return request({
    url: '/kg/teacher/' + teacherId + '/classes',
    method: 'get'
  })
}

// 获取教师任教课程
export function getTeacherCourses(teacherId) {
  return request({
    url: '/kg/teacher/' + teacherId + '/courses',
    method: 'get'
  })
}

// 教师排班信息
export function getTeacherSchedule(query) {
  return request({
    url: '/kg/teacher/schedule',
    method: 'get',
    params: query
  })
}

// 教师考勤统计
export function getTeacherAttendance(query) {
  return request({
    url: '/kg/teacher/attendance',
    method: 'get',
    params: query
  })
}

// 教师工资计算
export function calculateTeacherSalary(data) {
  return request({
    url: '/kg/teacher/salary/calculate',
    method: 'post',
    data: data
  })
}

// 教师请假申请
export function applyTeacherLeave(data) {
  return request({
    url: '/kg/teacher/leave/apply',
    method: 'post',
    data: data
  })
}

// 教师调课申请
export function applyTeacherTransfer(data) {
  return request({
    url: '/kg/teacher/transfer/apply',
    method: 'post',
    data: data
  })
}

// 获取可用教师列表（某个时间段）
export function getAvailableTeachers(query) {
  return request({
    url: '/kg/teacher/available',
    method: 'get',
    params: query
  })
}

// 教师评价管理
export function getTeacherEvaluations(teacherId) {
  return request({
    url: '/kg/teacher/' + teacherId + '/evaluations',
    method: 'get'
  })
}

// 添加教师评价
export function addTeacherEvaluation(data) {
  return request({
    url: '/kg/teacher/evaluation',
    method: 'post',
    data: data
  })
}

// 教师培训记录
export function getTeacherTrainings(teacherId) {
  return request({
    url: '/kg/teacher/' + teacherId + '/trainings',
    method: 'get'
  })
}

// 教师报表
export function getTeacherReport(query) {
  return request({
    url: '/kg/teacher/report',
    method: 'get',
    params: query
  })
}

// 导出教师数据
export function exportTeacher(query) {
  return request({
    url: '/kg/teacher/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
