package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 托管考勤记录对象 kg_course_attendance
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgCourseAttendance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 考勤ID */
    private Long attendanceId;

    /** 报名ID，关联kg_course_enrollment.enrollment_id */
    @Excel(name = "报名ID，关联kg_course_enrollment.enrollment_id")
    private Long enrollmentId;

    /** 幼儿ID，关联kg_student.student_id */
    @Excel(name = "幼儿ID，关联kg_student.student_id")
    private Long studentId;

    /** 课程ID，关联kg_course.course_id */
    @Excel(name = "课程ID，关联kg_course.course_id")
    private Long courseId;

    /** 授课教师ID，关联kg_teacher.teacher_id */
    @Excel(name = "授课教师ID，关联kg_teacher.teacher_id")
    private Long teacherId;

    /** 上课日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上课日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date attendanceDate;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 考勤状态（present出勤、absent缺勤、late迟到、early早退） */
    @Excel(name = "考勤状态", readConverterExp = "p=resent出勤、absent缺勤、late迟到、early早退")
    private String attendanceStatus;

    /** 实际签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际签到时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkInTime;

    /** 签到方式（face人脸、manual手动） */
    @Excel(name = "签到方式", readConverterExp = "f=ace人脸、manual手动")
    private String checkInMethod;

    /** 是否确认（0未确认 1已确认） */
    @Excel(name = "是否确认", readConverterExp = "0=未确认,1=已确认")
    private Long isConfirmed;

    /** 确认人ID，关联kg_teacher.teacher_id */
    @Excel(name = "确认人ID，关联kg_teacher.teacher_id")
    private Long confirmedBy;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmedTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;
    /** 班级ID */
    private Long classId;

    // ========== 显示字段（不对应数据库字段） ==========
    
    /** 学生姓名 */
    private String studentName;
    
    /** 课程名称 */
    private String courseName;
    
    /** 课程类型 */
    private String courseType;
    
    /** 教师姓名 */
    private String teacherName;
    /** 教师编号 */
    private String teacherCode;
    
    /** 班级名称 */
    private String className;
    
    /** 报名状态 */
    private String enrollmentStatus;
    
    /** 总课时数 */
    private Integer totalSessions;
    
    /** 已用课时数 */
    private Integer usedSessions;
    
    /** 剩余课时数 */
    private Integer remainingSessions;

    public void setClassId(Long classId) {
        this.classId = classId;
    }
    public Long getClassId() {
        return classId;
    }
    public void setTeacherCode(String teacherCode) {
        this.teacherCode = teacherCode;
    }
    public String getTeacherCode() {
        return teacherCode;
    }
    public void setAttendanceId(Long attendanceId) 
    {
        this.attendanceId = attendanceId;
    }

    public Long getAttendanceId() 
    {
        return attendanceId;
    }
    public void setEnrollmentId(Long enrollmentId) 
    {
        this.enrollmentId = enrollmentId;
    }

    public Long getEnrollmentId() 
    {
        return enrollmentId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setAttendanceDate(Date attendanceDate) 
    {
        this.attendanceDate = attendanceDate;
    }

    public Date getAttendanceDate() 
    {
        return attendanceDate;
    }

    public void setAttendanceStatus(String attendanceStatus) 
    {
        this.attendanceStatus = attendanceStatus;
    }

    public String getAttendanceStatus() 
    {
        return attendanceStatus;
    }
    
    public void setCheckInTime(Date checkInTime) 
    {
        this.checkInTime = checkInTime;
    }

    public Date getCheckInTime() 
    {
        return checkInTime;
    }
    
    public void setCheckInMethod(String checkInMethod) 
    {
        this.checkInMethod = checkInMethod;
    }

    public String getCheckInMethod() 
    {
        return checkInMethod;
    }
    public void setIsConfirmed(Long isConfirmed) 
    {
        this.isConfirmed = isConfirmed;
    }

    public Long getIsConfirmed() 
    {
        return isConfirmed;
    }
    public void setConfirmedBy(Long confirmedBy) 
    {
        this.confirmedBy = confirmedBy;
    }

    public Long getConfirmedBy() 
    {
        return confirmedBy;
    }
    public void setConfirmedTime(Date confirmedTime) 
    {
        this.confirmedTime = confirmedTime;
    }

    public Date getConfirmedTime() 
    {
        return confirmedTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    // ========== 显示字段的 getter/setter 方法 ==========
    
    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getCourseType() {
        return courseType;
    }

    public void setCourseType(String courseType) {
        this.courseType = courseType;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getEnrollmentStatus() {
        return enrollmentStatus;
    }

    public void setEnrollmentStatus(String enrollmentStatus) {
        this.enrollmentStatus = enrollmentStatus;
    }

    public Integer getTotalSessions() {
        return totalSessions;
    }

    public void setTotalSessions(Integer totalSessions) {
        this.totalSessions = totalSessions;
    }

    public Integer getUsedSessions() {
        return usedSessions;
    }

    public void setUsedSessions(Integer usedSessions) {
        this.usedSessions = usedSessions;
    }

    public Integer getRemainingSessions() {
        return remainingSessions;
    }

    public void setRemainingSessions(Integer remainingSessions) {
        this.remainingSessions = remainingSessions;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("attendanceId", getAttendanceId())
            .append("enrollmentId", getEnrollmentId())
            .append("studentId", getStudentId())
            .append("courseId", getCourseId())
            .append("teacherId", getTeacherId())
            .append("attendanceDate", getAttendanceDate())
            .append("attendanceStatus", getAttendanceStatus())
            .append("checkInMethod", getCheckInMethod())
            .append("isConfirmed", getIsConfirmed())
            .append("confirmedBy", getConfirmedBy())
            .append("confirmedTime", getConfirmedTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
