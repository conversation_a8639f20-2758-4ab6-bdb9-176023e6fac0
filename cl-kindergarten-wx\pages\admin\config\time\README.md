# 时间段配置页面

## 功能概述

时间段配置页面用于管理幼儿园的各种时间段设置，包括园费时间段和托管费时间段的配置。

## 主要功能

### 1. 时间段配置列表
- 显示所有时间段配置
- 支持分页加载
- 显示配置名称、时间类型、时间段、状态等信息
- 支持下拉刷新和上拉加载更多

### 2. 搜索和筛选
- **搜索功能**: 支持按配置名称搜索
- **筛选功能**: 
  - 按时间类型筛选（园费、托管费）
  - 按状态筛选（启用、禁用）

### 3. 新增时间段配置
- 浮动+号按钮：右下角固定位置的新增按钮
- 表单字段：
  - 配置名称（必填）
  - 时间类型（必填）：园费、托管费
  - 开始时间（必填）
  - 结束时间（必填）
- 表单验证确保数据完整性

### 4. 编辑时间段配置
- 点击配置卡片进入编辑模式
- 支持修改所有配置信息
- 自动填充现有数据

### 5. 状态管理
- 支持启用/禁用配置
- 确认操作防止误操作
- 实时更新状态显示

### 6. 删除配置
- 支持删除不需要的配置
- 确认操作防止误删除

## 技术实现

### API接口
- `getTimeConfigList`: 查询时间段配置列表
- `getTimeConfigDetail`: 查询时间段配置详细
- `addTimeConfig`: 新增时间段配置
- `updateTimeConfig`: 修改时间段配置
- `deleteTimeConfig`: 删除时间段配置

### 数据结构
```javascript
{
  configId: Number,        // 配置ID
  configName: String,      // 配置名称
  timeType: String,        // 时间类型（tuition园费、course托管费）
  startTime: String,       // 开始时间（HH:mm格式）
  endTime: String,         // 结束时间（HH:mm格式）
  isActive: Number,        // 是否启用（0否 1是）
  createTime: String,      // 创建时间
  remark: String          // 备注
}
```

### 时间选择器
- 自定义时间选择器组件
- 支持小时和分钟选择
- 分钟选项：0, 15, 30, 45
- 小时选项：0-23

### 状态管理
- 响应式数据绑定
- 实时状态更新
- 错误处理和用户提示

## 界面设计

### 卡片式布局
- 每个配置以卡片形式展示
- 显示配置图标、名称、类型、状态
- 操作按钮：编辑、启用/禁用、删除

### 浮动操作按钮
- 右下角固定位置
- 渐变背景和阴影效果
- 点击动画反馈

### 筛选弹窗
- 底部弹出式设计
- 单选框形式的筛选选项
- 重置和确定操作

### 表单弹窗
- 居中弹出式设计
- 响应式表单布局
- 时间选择器集成

## 用户体验

### 操作便捷性
1. **一键操作**: 状态切换、删除等操作一键完成
2. **搜索过滤**: 快速找到目标配置
3. **确认机制**: 重要操作有确认提示
4. **即时反馈**: 操作结果立即显示

### 视觉体验
1. **状态清晰**: 配置状态一目了然
2. **类型区分**: 不同类型有不同图标
3. **信息完整**: 显示配置的详细信息
4. **响应流畅**: 交互响应迅速

### 错误处理
1. **网络异常**: 处理网络请求失败的情况
2. **数据验证**: 确保输入数据的有效性
3. **状态同步**: 操作后及时更新界面状态
4. **用户提示**: 提供清晰的操作反馈

## 注意事项

1. **时间验证**: 确保开始时间小于结束时间
2. **数据同步**: 操作后及时刷新列表
3. **权限控制**: 根据用户权限显示操作按钮
4. **性能优化**: 分页加载避免一次性加载大量数据

## 扩展建议

1. **批量操作**: 支持批量启用/禁用配置
2. **模板功能**: 提供常用时间段模板
3. **导入导出**: 支持配置的导入导出
4. **历史记录**: 记录配置的修改历史
5. **冲突检测**: 检测时间段是否有冲突
6. **快捷设置**: 提供快捷的时间段设置选项
