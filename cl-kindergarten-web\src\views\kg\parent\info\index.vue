<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="家长姓名" prop="parentName">
        <el-input
          v-model="queryParams.parentName"
          placeholder="请输入家长姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="parentPhone">
        <el-input
          v-model="queryParams.parentPhone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="微信绑定" prop="bindStatus">
        <el-select v-model="queryParams.bindStatus" placeholder="请选择绑定状态" clearable>
          <el-option label="已绑定" value="1" />
          <el-option label="未绑定" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['kg:parent:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['kg:parent:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['kg:parent:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="parentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="家长姓名" align="center" prop="parentName" width="120" />
      <el-table-column label="联系电话" align="center" prop="parentPhone" width="120" />
      <el-table-column label="关系" align="center" prop="relationshipType" width="80">
        <template slot-scope="scope">
          {{ getRelationshipText(scope.row.relationshipType) }}
        </template>
      </el-table-column>
      <el-table-column label="学生信息" align="center" prop="studentList" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.studentList && scope.row.studentList.length > 0">
            <div v-for="student in scope.row.studentList" :key="student.studentId" style="margin-bottom: 2px;">
              <el-tag size="mini" type="info">{{ student.studentName }}</el-tag>
            </div>
          </div>
          <span v-else>{{ scope.row.studentCount || 0 }}个学生</span>
        </template>
      </el-table-column>
      <el-table-column label="微信绑定" align="center" prop="bindStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.bindStatus === '1' ? 'success' : 'info'" size="mini">
            {{ scope.row.bindStatus === '1' ? '已绑定' : '未绑定' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="绑定时间" align="center" prop="wechatBindTime" width="150" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['kg:parent:info:detail']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['kg:parent:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['kg:parent:info:remove']"
          >删除</el-button>
          <el-button
            v-if="scope.row.bindStatus === '0'"
            size="mini"
            type="text"
            icon="el-icon-connection"
            @click="handleWechatBind(scope.row)"
            v-hasPermi="['kg:parent:info:wechat']"
          >微信绑定</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改家长信息对话框 -->
    <el-dialog 
      :title="title" 
      :visible.sync="open" 
      width="600px" 
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :modal-append-to-body="true"
      :show-close="true"
      @close="cancel"
      custom-class="dialog-custom"
      append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="家长姓名" prop="parentName">
              <el-input v-model="form.parentName" placeholder="请输入家长姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="parentPhone">
              <el-input v-model="form.parentPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="与学生关系" prop="relationshipType">
              <el-select v-model="form.relationshipType" placeholder="请选择关系" style="width: 100%">
                <el-option label="父亲" value="father" />
                <el-option label="母亲" value="mother" />
                <el-option label="爷爷" value="grandfather_paternal" />
                <el-option label="奶奶" value="grandmother_paternal" />
                <el-option label="外公" value="grandfather_maternal" />
                <el-option label="外婆" value="grandmother_maternal" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="紧急联系人" prop="emergencyContact">
              <el-input v-model="form.emergencyContact" placeholder="请输入紧急联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急联系电话" prop="emergencyPhone">
              <el-input v-model="form.emergencyPhone" placeholder="请输入紧急联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作单位" prop="workUnit">
              <el-input v-model="form.workUnit" placeholder="请输入工作单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="职业" prop="occupation">
              <el-input v-model="form.occupation" placeholder="请输入职业" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="注册日期" prop="registerDate">
              <el-date-picker
                v-model="form.registerDate"
                type="date"
                placeholder="选择注册日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家庭住址" prop="address">
              <el-input v-model="form.address" placeholder="请输入家庭住址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 微信绑定对话框 -->
    <el-dialog 
      title="微信绑定" 
      :visible.sync="wechatBindDialog" 
      width="400px" 
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :show-close="true"
      @close="wechatBindDialog = false"
      custom-class="dialog-custom"
      append-to-body>
      <div style="text-align: center;">
        <p>请使用微信扫描下方二维码进行绑定</p>
        <div id="qrcode" style="margin: 20px auto; width: 200px; height: 200px; border: 1px solid #ddd;"></div>
        <p style="color: #999; font-size: 12px;">绑定成功后可接收学生在园信息推送</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="wechatBindDialog = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 家长详情对话框 -->
    <el-dialog 
      title="家长详细信息" 
      :visible.sync="detailDialogVisible" 
      width="80%" 
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :show-close="true"
      @close="closeDetailDialog"
      custom-class="detail-dialog"
      append-to-body>
      
      <div class="parent-detail-container">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-user"></i>
            <span>基本信息</span>
          </div>
          <div class="detail-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-group">
                  <div class="detail-item">
                    <label><i class="el-icon-user-solid"></i>家长姓名：</label>
                    <span class="value">{{ parentDetail.parentName || '-' }}</span>
                  </div>
                  <div class="detail-item">
                    <label><i class="el-icon-phone"></i>联系电话：</label>
                    <span class="value phone">{{ parentDetail.parentPhone || '-' }}</span>
                  </div>
                  <div class="detail-item">
                    <label><i class="el-icon-postcard"></i>身份证号：</label>
                    <span class="value">{{ parentDetail.idCard || '未填写' }}</span>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-group">
                  <div class="detail-item">
                    <label><i class="el-icon-connection"></i>与学生关系：</label>
                    <span class="value">
                      <el-tag size="mini" type="primary">{{ getRelationshipText(parentDetail.relationshipType) }}</el-tag>
                    </span>
                  </div>
                  <div class="detail-item">
                    <label><i class="el-icon-mobile-phone"></i>微信绑定：</label>
                    <span class="value">
                      <el-tag size="mini" :type="parentDetail.bindStatus === '1' ? 'success' : 'warning'">
                        {{ parentDetail.bindStatus === '1' ? '已绑定' : '未绑定' }}
                      </el-tag>
                    </span>
                  </div>
                  <div class="detail-item" v-if="parentDetail.workUnit">
                    <label><i class="el-icon-office-building"></i>工作单位：</label>
                    <span class="value">{{ parentDetail.workUnit }}</span>
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <el-row :gutter="24" v-if="parentDetail.address">
              <el-col :span="24">
                <div class="detail-item address-item">
                  <label><i class="el-icon-location"></i>家庭地址：</label>
                  <span class="value">{{ parentDetail.address }}</span>
                </div>
              </el-col>
            </el-row>
            
            <!-- 紧急联系人信息 -->
            <el-row :gutter="24" v-if="parentDetail.emergencyContact">
              <el-col :span="12">
                <div class="detail-item">
                  <label><i class="el-icon-warning"></i>紧急联系人：</label>
                  <span class="value">{{ parentDetail.emergencyContact }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <label><i class="el-icon-phone-outline"></i>紧急联系电话：</label>
                  <span class="value phone">{{ parentDetail.emergencyPhone || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 子女信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <i class="el-icon-school"></i>
            <span>子女信息</span>
            <el-badge :value="childrenInfo.length" class="children-count" v-if="childrenInfo.length > 0" />
          </div>
          <div v-if="childrenInfo.length === 0" class="empty-data">
            <i class="el-icon-user"></i>
            <p>暂无子女信息</p>
          </div>
          <el-table v-else :data="childrenInfo" border stripe class="children-table">
            <el-table-column prop="studentName" label="学生姓名" min-width="100" align="center">
              <template slot-scope="scope">
                <span class="student-name">{{ scope.row.studentName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="studentCode" label="学生编号" width="120" align="center" />
            <el-table-column prop="className" label="班级" min-width="120" align="center">
              <template slot-scope="scope">
                <el-tag size="small" type="info">{{ scope.row.className || '未分班' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="学生状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag size="small" :type="getStudentStatusType(scope.row.status)">
                  {{ getStudentStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" plain @click="viewStudent(scope.row)">
                  <i class="el-icon-view"></i> 查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 费用统计 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <i class="el-icon-money"></i>
            <span>费用统计</span>
          </div>
          <div class="fee-stats-container">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-card total">
                  <div class="stat-icon">
                    <i class="el-icon-wallet"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">￥{{ (feeStats.totalAmount || 0).toLocaleString() }}</div>
                    <div class="stat-label">总费用</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card paid">
                  <div class="stat-icon">
                    <i class="el-icon-success"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">￥{{ (feeStats.paidAmount || 0).toLocaleString() }}</div>
                    <div class="stat-label">已交费用</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card unpaid">
                  <div class="stat-icon">
                    <i class="el-icon-warning"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">￥{{ (feeStats.unpaidAmount || 0).toLocaleString() }}</div>
                    <div class="stat-label">未交费用</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 最近消息记录 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <i class="el-icon-message"></i>
            <span>最近消息记录</span>
          </div>
          <div v-if="recentMessages.length === 0" class="empty-data">
            <i class="el-icon-chat-line-square"></i>
            <p>暂无消息记录</p>
          </div>
          <el-table v-else :data="recentMessages" border stripe class="message-table">
            <el-table-column prop="title" label="消息标题" min-width="150" align="center">
              <template slot-scope="scope">
                <span class="message-title">{{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="消息内容" show-overflow-tooltip min-width="200" />
            <el-table-column prop="sendTime" label="发送时间" width="160" align="center">
              <template slot-scope="scope">
                <span class="send-time">{{ scope.row.sendTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="消息类型" width="100" align="center">
              <template slot-scope="scope">
                <el-tag size="small" :type="scope.row.type === 1 ? 'primary' : 'success'">
                  <i :class="scope.row.type === 1 ? 'el-icon-document' : 'el-icon-bell'"></i>
                  {{ scope.row.type === 1 ? '账单' : '通知' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDetailDialog">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listParent, getParent, delParent, addParent, updateParent, getParentChildren, getParentFeeStats, getParentMessages } from "@/api/kg/parent/info";
import { Message, MessageBox } from 'element-ui';

export default {
  name: "ParentInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 家长信息表格数据
      parentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 微信绑定对话框
      wechatBindDialog: false,
      // 详情对话框显示
      detailDialogVisible: false,
      // 当前查看的家长ID
      currentParentId: null,
      // 家长详情信息
      parentDetail: {},
      // 子女信息
      childrenInfo: [],
      // 费用统计
      feeStats: {},
      // 最近消息记录
      recentMessages: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentName: null,
        parentPhone: null,
        studentName: null,
        bindStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: "家长编号不能为空", trigger: "blur" }
        ],
        parentName: [
          { required: true, message: "家长姓名不能为空", trigger: "blur" }
        ],
        parentPhone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        relationshipType: [
          { required: true, message: "请选择与学生关系", trigger: "change" }
        ],
        idCard: [
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号", trigger: "blur" }
        ],
        emergencyPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        status: [
          { required: true, message: "请选择状态", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询家长信息列表 */
    getList() {
      this.loading = true;
      listParent(this.queryParams).then(response => {
        this.parentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      this.$refs.form && this.$refs.form.clearValidate();
    },
    // 表单重置
    reset() {
      this.form = {
        parentId: null,
        parentName: null,
        parentPhone: null,
        wechatOpenid: null,
        idCard: null,
        relationshipType: null,
        emergencyContact: null,
        emergencyPhone: null,
        address: null,
        workUnit: null,
        occupation: null,
        status: "0",
        bindStatus: "0",
        registerDate: null,
        comId: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.parentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.generateParentCode();
      this.open = true;
      this.title = "添加家长信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const parentId = row.parentId || this.ids
      getParent(parentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改家长信息";
      });
    },
    /** 查看详情 */
    handleDetail(row) {
      this.currentParentId = row.parentId;
      this.detailDialogVisible = true;
      this.getParentDetail(row.parentId);
    },
    /** 获取家长详情 */
    getParentDetail(parentId) {
      getParent(parentId).then(response => {
        this.parentDetail = response.data;
        this.loadChildrenInfo(parentId);
        this.loadFeeStats(parentId);
        this.loadRecentRecords(parentId);
      }).catch(() => {
        this.$modal.msgError('获取家长详情失败');
      });
    },
    /** 加载子女信息 */
    loadChildrenInfo(parentId) {
      if (!parentId) {
        this.childrenInfo = [];
        return;
      }
      
      getParentChildren(parentId).then(response => {
        this.childrenInfo = response.data || [];
      }).catch(error => {
        console.error('获取子女信息失败:', error);
        this.childrenInfo = [];
        // 不显示错误提示，只记录日志，避免影响用户体验
      });
    },
    /** 加载费用统计 */
    loadFeeStats(parentId) {
      if (!parentId) {
        this.feeStats = { totalAmount: 0, paidAmount: 0, unpaidAmount: 0 };
        return;
      }
      
      getParentFeeStats(parentId).then(response => {
        this.feeStats = response.data || { totalAmount: 0, paidAmount: 0, unpaidAmount: 0 };
      }).catch(error => {
        console.error('获取费用统计失败:', error);
        this.feeStats = { totalAmount: 0, paidAmount: 0, unpaidAmount: 0 };
      });
    },
    /** 加载最近记录 */
    loadRecentRecords(parentId) {
      if (!parentId) {
        this.recentMessages = [];
        return;
      }
      
      // 获取最近消息记录，限制数量为10条
      const query = { pageNum: 1, pageSize: 10 };
      getParentMessages(parentId, query).then(response => {
        this.recentMessages = response.rows || [];
      }).catch(error => {
        console.error('获取消息记录失败:', error);
        this.recentMessages = [];
      });
    },
    /** 获取关系文本 */
    getRelationshipText(relationship) {
      const relationshipMap = {
        '0': '父母',
        '1': '祖父母',
        '2': '外祖父母',
        '3': '其他'
      };
      return relationshipMap[relationship] || '未知';
    },
    /** 获取学生状态文本 */
    getStudentStatusText(status) {
      const statusMap = {
        0: '在读',
        1: '休学',
        2: '退学'
      };
      return statusMap[status] || '未知';
    },
    /** 获取学生状态类型 */
    getStudentStatusType(status) {
      const typeMap = {
        0: 'success',
        1: 'warning',
        2: 'danger'
      };
      return typeMap[status] || 'info';
    },
    /** 查看学生 */
    viewStudent(row) {
      this.$router.push({
        path: '/kg/student/info',
        query: { studentId: row.studentId }
      });
    },
    /** 关闭详情对话框 */
    closeDetailDialog() {
      this.detailDialogVisible = false;
      this.currentParentId = null;
      this.parentDetail = {};
      this.childrenInfo = [];
      this.feeStats = {};
      this.recentMessages = [];
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '提交中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          const promise = this.form.parentId 
            ? updateParent(this.form)
            : addParent(this.form);
            
          promise.then(response => {
            Message.success(this.form.parentId ? "修改成功" : "新增成功");
            this.getList();     // 刷新列表
            return new Promise(resolve => {
              // 使用$nextTick确保DOM更新
              this.$nextTick(() => {
                this.reset();
                this.open = false;
                resolve();
              });
            });
          }).catch(error => {
            console.error('操作失败:', error);
            Message.error(error.msg || (this.form.parentId ? '修改失败' : '新增失败'));
            // 不清空表单，保留用户输入以便修改
            return Promise.reject(error);
          }).finally(() => {
            loading.close();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const parentIds = row.parentId || this.ids;
      MessageBox.confirm('是否确认删除家长编号为"' + parentIds + '"的数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return delParent(parentIds);
      }).then(() => {
        this.getList();
        Message.success("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('kg/parent/export', {
        ...this.queryParams
      }, `parent_${new Date().getTime()}.xlsx`)
    },
    /** 微信绑定 */
    handleWechatBind(row) {
      this.wechatBindDialog = true;
      // 这里应该生成微信绑定二维码
      this.$nextTick(() => {
        // 模拟二维码展示
        document.getElementById('qrcode').innerHTML = '<div style="line-height: 200px; text-align: center; color: #999;">二维码占位</div>';
      });
    },
    // 生成家长编号
    generateParentCode() {
      const now = new Date();
      const year = now.getFullYear().toString().substr(-2);
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      this.form.parentId = `PAR${year}${month}${random}`;
    },
    // 获取关系文本
    getRelationshipText(relationship) {
      // 处理英文对应的中文显示
      const relationshipMap = {
        'father': '父亲',
        'mother': '母亲',
        'grandfather_paternal': '爷爷',
        'grandmother_paternal': '奶奶',
        'grandfather_maternal': '外公',
        'grandmother_maternal': '外婆',
        'other': '其他'
      };
      // 如果已经是中文，直接返回
      if (relationship && typeof relationship === 'string' && !/^[a-zA-Z_]+$/.test(relationship)) {
        return relationship;
      }
      // 否则通过映射表转换
      return relationshipMap[relationship] || '未知';
    }
  }
};
</script>

<style lang="scss" scoped>
.parent-detail-container {
  .detail-card {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    
    .card-header {
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 16px;
      }
      
      .children-count {
        margin-left: auto;
      }
    }
  }
  
  .detail-content {
    padding: 10px 0;
  }
  
  .info-group {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    height: 100%;
  }
  
  .detail-item {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.address-item {
      background: #f0f9ff;
      padding: 12px;
      border-radius: 6px;
      border-left: 4px solid #409EFF;
    }
    
    label {
      font-weight: 500;
      color: #606266;
      margin-right: 12px;
      min-width: 100px;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 4px;
        color: #909399;
      }
    }
    
    .value {
      color: #303133;
      flex: 1;
      
      &.phone {
        font-family: monospace;
        font-weight: 500;
        color: #409EFF;
      }
    }
  }
  
  // 空数据样式
  .empty-data {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
    
    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
  
  // 表格样式
  .children-table, .message-table {
    .student-name {
      font-weight: 500;
      color: #303133;
    }
    
    .message-title {
      font-weight: 500;
      color: #303133;
    }
    
    .send-time {
      color: #909399;
      font-size: 12px;
    }
  }
  
  // 费用统计样式
  .fee-stats-container {
    padding: 10px 0;
  }
  
  .stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      i {
        font-size: 24px;
      }
    }
    
    .stat-content {
      flex: 1;
      
      .stat-value {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;
        line-height: 1;
      }
      
      .stat-label {
        font-size: 13px;
        opacity: 0.9;
      }
    }
    
    &.total {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      
      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
      }
    }
    
    &.paid {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      
      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
      }
    }
    
    &.unpaid {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      color: white;
      
      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

// 对话框样式调整
::v-deep .detail-dialog {
  .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }
  
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    
    .el-dialog__title {
      color: white;
      font-weight: 600;
    }
    
    .el-dialog__close {
      color: white;
      
      &:hover {
        color: #f0f0f0;
      }
    }
  }
  
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
    background: #fafafa;
  }
  
  .el-dialog__footer {
    padding: 16px 20px;
    background: white;
    border-top: 1px solid #ebeef5;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .parent-detail-container {
    .info-group {
      margin-bottom: 16px;
    }
    
    .detail-item {
      flex-direction: column;
      align-items: flex-start;
      
      label {
        margin-bottom: 4px;
        min-width: auto;
      }
    }
    
    .stat-card {
      flex-direction: column;
      text-align: center;
      
      .stat-icon {
        margin-right: 0;
        margin-bottom: 12px;
      }
    }
  }
}
</style>
