<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="计算类型" prop="calculationType">
        <el-select v-model="queryParams.calculationType" placeholder="请选择计算类型" clearable size="small">
          <el-option label="单个学生" value="single" />
          <el-option label="班级批量" value="class" />
          <el-option label="全园批量" value="all" />
        </el-select>
      </el-form-item>
      <el-form-item label="班级" prop="classId" v-if="queryParams.calculationType !== 'all'">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable size="small" @change="handleClassChange">
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学生" prop="studentId" v-if="queryParams.calculationType === 'single'">
        <el-select v-model="queryParams.studentId" placeholder="请选择学生" clearable size="small" filterable>
          <el-option
            v-for="item in studentList"
            :key="item.studentId"
            :label="item.studentName"
            :value="item.studentId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计算月份">
        <el-date-picker
          v-model="calculationMonth"
          type="month"
          placeholder="选择月份"
          size="small"
          @change="handleMonthChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleCalculate">开始计算</el-button>
        <el-button type="success" icon="el-icon-view" size="mini" @click="handlePreview">预览</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 计算规则展示 -->
    <el-card class="mb20 calculation-rules-card" v-if="calculationRules">
      <div slot="header" class="clearfix">
        <span class="rule-title">
          <i class="el-icon-s-order"></i>
          费用计算规则
        </span>
        <el-tag size="mini" type="info" style="float: right; margin-top: 3px;">
          {{ calculationRules.config ? calculationRules.config.classType : '' }}类别
        </el-tag>
      </div>
      <div class="calculation-descriptions">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="rule-item">
              <label class="rule-label">餐费计算：</label>
              <span class="rule-text">
                <i class="el-icon-food rule-icon meal-icon"></i>
                {{ calculationRules.calculationRules.mealFeeRule }}
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="rule-item">
              <label class="rule-label">保教费计算：</label>
              <span class="rule-text">
                <i class="el-icon-reading rule-icon education-icon"></i>
                {{ calculationRules.calculationRules.educationFeeRule }}
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="12">
            <div class="rule-item">
              <label class="rule-label">出勤率计算：</label>
              <span class="rule-text">
                <i class="el-icon-pie-chart rule-icon rate-icon"></i>
                {{ calculationRules.calculationRules.attendanceRateRule }}
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="rule-item">
              <label class="rule-label">园费总额：</label>
              <span class="rule-text">
                <i class="el-icon-money rule-icon total-icon"></i>
                {{ calculationRules.calculationRules.totalFeeRule }}
              </span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" v-if="calculationResults.length > 0">
      <!-- 状态信息提示 -->
      <el-col :span="24" v-if="hasRecalledBills" style="margin-bottom: 10px;">
        <el-alert
          :title="`已回显 ${recalledBillsCount} 条历史账单数据`"
          type="success"
          :closable="false"
          show-icon>
          <template slot="description">
            该月份已生成账单，以下是历史账单数据。如需重新计算，请先删除对应账单。
          </template>
        </el-alert>
      </el-col>
      <el-col :span="24" v-else-if="generatedBillsCount > 0" style="margin-bottom: 10px;">
        <el-alert
          :title="`已生成 ${generatedBillsCount} 条账单，剩余 ${unGeneratedBillsCount} 条可生成`"
          type="info"
          :closable="false"
          show-icon>
        </el-alert>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          :disabled="!hasUnGeneratedBills"
          @click="handleGenerateBills"
          v-hasPermi="['kg:finance:tuition:send']"
        >生成账单 ({{ unGeneratedBillsCount }})</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出结果</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-refresh"
          size="mini"
          @click="handleRecalculateAll"
          v-hasPermi="['kg:finance:tuition:calculate']"
        >重新计算</el-button>
      </el-col>
    </el-row>

    <!-- 计算结果表格 -->
    <el-table 
      v-loading="loading" 
      :data="paginatedResults" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生姓名" align="center" prop="studentInfo.studentName" width="100" />
      <el-table-column label="班级" align="center" prop="studentInfo.className" width="80" />
      <el-table-column label="出勤天数" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceDays : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="出勤率" align="center" width="100">
        <template slot-scope="scope">
          <span :class="getAttendanceRateClass(scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0)">
            {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0 }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column label="餐费" align="center" width="150">
        <template slot-scope="scope">
          <span class="text-primary">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.mealFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保教费" align="center" width="150">
        <template slot-scope="scope">
          <span class="text-success">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.educationFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="费用小计" align="center" width="200">
        <template slot-scope="scope">
          <span class="text-warning">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.totalFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="余额结转" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-success">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.balanceCarryover : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际应缴" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-danger font-weight-bold">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.actualPayable : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下月预交" align="center" width="200">
        <template slot-scope="scope">
          <span class="text-info">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.advancePayment : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.error" type="danger">计算失败</el-tag>
          <el-tag v-else-if="scope.row.originalBill && scope.row.originalBill.billStatus === 'paid'" type="success">已支付</el-tag>
          <el-tag v-else-if="scope.row.originalBill && scope.row.originalBill.billStatus === 'sent'" type="warning">已发送</el-tag>
          <el-tag v-else-if="scope.row.billGenerated" type="info">已生成账单</el-tag>
          <el-tag v-else type="success">计算成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
          >详情</el-button>
          <el-button
            v-if="scope.row.billGenerated || scope.row.originalBill"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAdjust(scope.row)"
            v-hasPermi="['kg:finance:tuition:calculate']"
          >调整</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handlePagination"
    />

    <!-- 费用详情对话框 -->
    <el-dialog title="费用计算详情" :visible.sync="detailDialogVisible" width="800px" append-to-body>
      <div v-if="selectedCalculation">
        <!-- 学生信息 -->
        <el-card class="mb20">
          <div slot="header" class="clearfix">
            <span>学生信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>学生姓名：</label>
                <span>{{ selectedCalculation.studentInfo.studentName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>班级：</label>
                <span>{{ selectedCalculation.studentInfo.className }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>学生状态：</label>
                <span>{{ getStudentStatusText(selectedCalculation.studentInfo.status) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
        
        <!-- 考勤统计 -->
        <el-card class="mb20">
          <div slot="header" class="clearfix">
            <span>考勤统计</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="detail-item">
                <label>总天数：</label>
                <span>{{ selectedCalculation.attendanceStats.totalDays }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>出勤天数：</label>
                <span>{{ selectedCalculation.attendanceStats.attendanceDays }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>缺勤天数：</label>
                <span>{{ selectedCalculation.attendanceStats.absentDays }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>出勤率：</label>
                <span>{{ selectedCalculation.attendanceStats.attendanceRate }}%</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
        
        <!-- 费用明细 -->
        <el-card>
          <div slot="header" class="clearfix">
            <span>费用明细</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>餐费：</label>
                <span class="fee-amount">￥{{ selectedCalculation.feeBreakdown.mealFee }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>保教费：</label>
                <span class="fee-amount">￥{{ selectedCalculation.feeBreakdown.educationFee }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px;">
            <el-col :span="12">
              <div class="detail-item">
                <label>管理费：</label>
                <span class="fee-amount">￥{{ selectedCalculation.feeBreakdown.managementFee }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>费用小计：</label>
                <span class="fee-amount total-fee">￥{{ selectedCalculation.feeBreakdown.totalFee }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px;">
            <el-col :span="12">
              <div class="detail-item">
                <label>余额结转：</label>
                <span class="fee-amount">￥{{ selectedCalculation.feeBreakdown.balanceCarryover }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>实际应缴：</label>
                <span class="fee-amount payable-amount">￥{{ selectedCalculation.feeBreakdown.actualPayable }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-dialog>

    <!-- 费用调整对话框 -->
    <el-dialog title="费用调整" :visible.sync="adjustDialogVisible" width="500px" append-to-body>
      <el-form ref="adjustForm" :model="adjustForm" :rules="adjustRules" label-width="100px">
        <el-form-item label="费用类型" prop="feeType">
          <el-select v-model="adjustForm.feeType" placeholder="请选择费用类型">
            <el-option label="餐费" value="meal" />
            <el-option label="保教费" value="education" />
          </el-select>
        </el-form-item>
        <el-form-item label="调整金额" prop="adjustAmount">
          <el-input-number 
            v-model="adjustForm.adjustAmount" 
            :precision="2" 
            :step="1" 
            placeholder="正数为增加，负数为减少"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input v-model="adjustForm.reason" type="textarea" placeholder="请输入调整原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdjust">确 定</el-button>
        <el-button @click="cancelAdjust">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  calculateMonthlyTuition,
  calculateClassTuitionBatch,
  calculateAllTuitionBatch,
  previewTuitionCalculation,
  getTuitionCalculationRules,
  generateTuitionBills,
  adjustFee,
  getTuitionStatistics,
  recallTuitionCalculationResults
} from "@/api/kg/finance/tuition/calculation";
// import { listTuitionBill } from "@/api/kg/finance/tuition/bill"; // 不再直接使用账单API
import { listClass } from "@/api/kg/student/class";
import { listStudent } from "@/api/kg/student/info";

export default {
  name: "TuitionCalculation",
  data() {
    return {
      // 查询参数
      queryParams: {
        calculationType: 'single',
        classId: null,
        studentId: null,
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        pageNum: 1,
        pageSize: 10
      },
      // 计算月份
      calculationMonth: new Date(),
      // 加载状态
      loading: false,
      // 计算结果
      calculationResults: [],
      // 计算规则
      calculationRules: null,
      // 总记录数
      total: 0,
      // 选中的记录
      ids: [],
      multiple: true,
      // 基础数据
      classList: [],
      studentList: [],
      // 详情对话框
      detailDialogVisible: false,
      selectedCalculation: null,
      // 调整对话框
      adjustDialogVisible: false,
      adjustForm: {
        billId: null,
        feeType: '',
        adjustAmount: 0,
        reason: ''
      },
      adjustRules: {
        feeType: [
          { required: true, message: "费用类型不能为空", trigger: "change" }
        ],
        adjustAmount: [
          { required: true, message: "调整金额不能为空", trigger: "blur" }
        ],
        reason: [
          { required: true, message: "调整原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    // 分页显示的数据
    paginatedResults() {
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
      const end = start + this.queryParams.pageSize;
      return this.calculationResults.slice(start, end);
    },
    // 计算是否还有未生成账单的记录
    hasUnGeneratedBills() {
      return this.calculationResults.some(result => !result.error && !result.billGenerated);
    },
    // 计算未生成账单的记录数量
    unGeneratedBillsCount() {
      return this.calculationResults.filter(result => !result.error && !result.billGenerated).length;
    },
    // 计算已生成账单的记录数量
    generatedBillsCount() {
      return this.calculationResults.filter(result => result.billGenerated).length;
    },
    // 判断是否有回显的账单数据
    hasRecalledBills() {
      return this.calculationResults.some(result => result.originalBill);
    },
    // 计算回显的账单数量
    recalledBillsCount() {
      return this.calculationResults.filter(result => result.originalBill).length;
    }
  },
  created() {
    this.getClassList();
    this.getStudentList();
  },
  methods: {
    /** 获取班级列表 */
    getClassList() {
      listClass().then(response => {
        this.classList = response.rows;
      });
    },
    
    /** 获取学生列表 */
    getStudentList() {
      listStudent().then(response => {
        this.studentList = response.rows;
      });
    },
    
    /** 班级改变时更新学生列表 */
    handleClassChange(classId) {
      if (classId) {
        this.studentList = this.studentList.filter(student => student.classId === classId);
        this.queryParams.studentId = null;
        
        // 获取计算规则
        getTuitionCalculationRules(classId).then(response => {
          this.calculationRules = response.data;
        });
      } else {
        this.getStudentList();
        this.calculationRules = null;
      }
    },
    
    /** 月份改变 */
    handleMonthChange(date) {
      if (date) {
        this.queryParams.year = date.getFullYear();
        this.queryParams.month = date.getMonth() + 1;
      }
    },
    
    /** 开始计算 */
    async handleCalculate() {
      if (!this.validateParams()) {
        return;
      }
      
      this.loading = true;
      
      try {
        // 第一步：查询是否已存在账单数据
        const existingBills = await this.queryExistingBills();
        
        if (existingBills.length > 0) {
          // 新的API已经返回了与计算结果相同的数据结构，直接使用
          this.calculationResults = existingBills.map(result => ({
            ...result,
            billGenerated: true, // 标记为已生成账单
            billGeneratedTime: result.calculationDate || new Date().toLocaleString(),
            calculationStatus: 'bills_generated', // 更新状态
            originalBill: result.originalBill || result // 保留原始账单数据
          }));
          this.total = this.calculationResults.length;
          this.loading = false;
          
          const billCount = existingBills.length;
          this.$message.info(`已回显 ${billCount} 条账单记录`);
          return;
        }
        
        // 第二步：如果不存在账单数据，进行计算
        await this.performCalculation();
        
      } catch (error) {
        console.error('计算过程出错:', error);
        this.$message.error('计算失败: ' + (error.message || '未知错误'));
        this.loading = false;
      }
    },
    
    /** 预览计算 */
    handlePreview() {
      if (this.queryParams.calculationType !== 'single' || !this.queryParams.studentId) {
        this.$message.warning('预览功能仅支持单个学生计算');
        return;
      }
      
      previewTuitionCalculation(
        this.queryParams.studentId,
        this.queryParams.year,
        this.queryParams.month
      ).then(response => {
        this.selectedCalculation = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 参数验证 */
    validateParams() {
      if (this.queryParams.calculationType === 'single' && !this.queryParams.studentId) {
        this.$message.warning('请选择学生');
        return false;
      }
      
      if (this.queryParams.calculationType !== 'all' && !this.queryParams.classId) {
        this.$message.warning('请选择班级');
        return false;
      }
      
      if (!this.queryParams.year || !this.queryParams.month) {
        this.$message.warning('请选择计算月份');
        return false;
      }
      
      return true;
    },
    
    /** 重置查询 */
    resetQuery() {
      this.resetForm("queryForm");
      this.calculationResults = [];
      this.calculationRules = null;
      this.total = 0;
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.studentInfo.studentId);
      this.multiple = !selection.length;
    },
    
    /** 生成账单 */
    handleGenerateBills() {
      if (this.calculationResults.length === 0) {
        this.$message.warning('没有可生成的账单');
        return;
      }
      
      // 过滤掉计算失败和已生成账单的记录
      const validResults = this.calculationResults.filter(result => !result.error && !result.billGenerated);
      
      if (validResults.length === 0) {
        this.$message.warning('没有可生成的账单记录');
        return;
      }
      
      this.$confirm(`确认生成 ${validResults.length} 条账单?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 添加账单月份信息
        const billData = validResults.map(result => ({
          ...result,
          billMonth: `${this.queryParams.year}-${String(this.queryParams.month).padStart(2, '0')}`
        }));
        
        generateTuitionBills(billData).then(response => {
          this.$message.success(response.msg);
          
          // 更新计算结果状态为已生成账单，而不是清空
          this.calculationResults = this.calculationResults.map(result => {
            if (!result.error) {
              return {
                ...result,
                billGenerated: true, // 标记为已生成账单
                billGeneratedTime: new Date().toLocaleString(),
                calculationStatus: 'bills_generated' // 更新状态
              };
            }
            return result;
          });
          
          // 刷新计算规则显示
          this.loadCalculationRules();
        }).catch(error => {
          console.error('生成账单失败:', error);
          this.$message.error('生成账单失败: ' + (error.response?.data?.msg || error.message));
        });
      });
    },
    
    /** 导出结果 */
    handleExport() {
      // TODO: 实现导出功能
      this.$message.info("导出功能开发中...");
    },
    

    
    /** 查看详情 */
    handleViewDetail(row) {
      this.selectedCalculation = row;
      this.detailDialogVisible = true;
    },
    
    /** 费用调整 */
    handleAdjust(row) {
      // 优先使用回显字段 originalBill 中的 billId
      if (row.originalBill && row.originalBill.billId) {
        this.adjustForm.billId = row.originalBill.billId;
      } else if (row.billGenerated) {
        // 如果是当前新生成的账单，提示用户先刷新页面
        this.$message.warning('请先刷新页面后再进行调整操作');
        return;
      } else {
        this.$message.error('只能对已生成的账单进行调整');
        return;
      }
      
      this.adjustForm.feeType = '';
      this.adjustForm.adjustAmount = 0;
      this.adjustForm.reason = '';
      this.adjustDialogVisible = true;
    },
    
    /** 提交调整 */
    submitAdjust() {
      this.$refs["adjustForm"].validate(valid => {
        if (valid) {
          adjustFee(
            this.adjustForm.billId,
            this.adjustForm.feeType,
            this.adjustForm.adjustAmount,
            this.adjustForm.reason
          ).then(response => {
            this.$message.success("调整成功");
            this.adjustDialogVisible = false;
            this.handleCalculate();
          });
        }
      });
    },
    
    /** 取消调整 */
    cancelAdjust() {
      this.adjustDialogVisible = false;
    },
    
    /** 获取出勤率样式类 */
    getAttendanceRateClass(rate) {
      if (rate >= 90) return 'text-success';
      if (rate >= 70) return 'text-warning';
      return 'text-danger';
    },
    
    /** 获取学生状态文本 */
    getStudentStatusText(status) {
      const statusMap = {
        '0': '在读',
        '1': '休学',
        '2': '退学'
      };
      return statusMap[status] || '未知';
    },
    
    /** 表格合计行 */
    getSummaries(param) {
      const { columns } = param;
      const sums = [];
      // 使用所有数据计算合计，而不是只计算当前页
      const allData = this.calculationResults;
      
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        
        const values = allData.map(item => {
          if (!item.feeBreakdown) return 0;
          
          switch (column.property) {
            case 'feeBreakdown.mealFee':
              return Number(item.feeBreakdown.mealFee);
            case 'feeBreakdown.educationFee':
              return Number(item.feeBreakdown.educationFee);
            case 'feeBreakdown.managementFee':
              return Number(item.feeBreakdown.managementFee);
            case 'feeBreakdown.totalFee':
              return Number(item.feeBreakdown.totalFee);
            case 'feeBreakdown.actualPayable':
              return Number(item.feeBreakdown.actualPayable);
            default:
              return 0;
          }
        });
        
        if (values.every(value => !isNaN(value))) {
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = '￥' + sum.toFixed(2);
        } else {
          sums[index] = '';
        }
      });
      
      return sums;
    },
    
    /** 加载计算规则 */
    loadCalculationRules() {
      // 如果有班级选择，加载对应班级的计算规则
      if (this.queryParams.classId) {
        getTuitionCalculationRules(this.queryParams.classId).then(response => {
          this.calculationRules = response.data;
        }).catch(error => {
          console.error('加载计算规则失败:', error);
        });
      } else {
        // 如果没有指定班级，加载默认规则
        getTuitionCalculationRules().then(response => {
          this.calculationRules = response.data;
        }).catch(error => {
          console.error('加载计算规则失败:', error);
        });
      }
    },
    
    /** 查询已存在的账单数据 */
    async queryExistingBills() {
      const queryParams = {
        year: this.queryParams.year,
        month: this.queryParams.month,
        calculationType: this.queryParams.calculationType
      };
      
      // 根据计算类型添加筛选条件
      if (this.queryParams.calculationType === 'single' && this.queryParams.studentId) {
        queryParams.studentId = this.queryParams.studentId;
      } else if (this.queryParams.calculationType === 'class' && this.queryParams.classId) {
        queryParams.classId = this.queryParams.classId;
      }
      // 全园计算不添加额外筛选条件
      
      const response = await recallTuitionCalculationResults(queryParams);
      return response.data || [];
    },
    
    /** 账单数据转换已经移至后端处理，保持数据结构一致性 */
    
    /** 执行计算逻辑 */
    async performCalculation() {
      let apiCall;
      
      switch (this.queryParams.calculationType) {
        case 'single':
          apiCall = calculateMonthlyTuition(
            this.queryParams.studentId,
            this.queryParams.year,
            this.queryParams.month
          );
          break;
        case 'class':
          apiCall = calculateClassTuitionBatch(
            this.queryParams.classId,
            this.queryParams.year,
            this.queryParams.month
          );
          break;
        case 'all':
          apiCall = calculateAllTuitionBatch(
            this.queryParams.year,
            this.queryParams.month
          );
          break;
      }
      
      const response = await apiCall;
      
      if (this.queryParams.calculationType === 'single') {
        this.calculationResults = [response.data];
      } else if (this.queryParams.calculationType === 'class') {
        this.calculationResults = response.data;
      } else {
        // 全园批量计算，需要展开所有班级的结果
        this.calculationResults = [];
        response.data.classResults.forEach(classResult => {
          this.calculationResults.push(...classResult.studentResults);
        });
      }
      
      this.total = this.calculationResults.length;
      this.loading = false;
      
      this.$message.success('计算完成');
    },
    
    /** 重新计算（忽略已有账单数据） */
    async handleRecalculateAll() {
      if (!this.validateParams()) {
        return;
      }
      
      this.$confirm('确认要重新计算吗？这将忽略已有的账单数据。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true;
        
        try {
          // 直接进行计算，不查询已有账单
          this.$message.info('强制重新计算模式，忽略已有账单数据');
          await this.performCalculation();
          
        } catch (error) {
          console.error('重新计算过程出错:', error);
          this.$message.error('重新计算失败: ' + (error.message || '未知错误'));
          this.loading = false;
        }
      }).catch(() => {
        // 用户取消
      });
    },
    
    /** 分页处理 */
    handlePagination(pagination) {
      this.queryParams.pageNum = pagination.page;
      this.queryParams.pageSize = pagination.limit;
      // 园费计算页面不需要重新查询，分页只是为了显示
      // 这里只是更新分页参数，不需要调用API
    }
  }
};
</script>

<style scoped>
/* 基础样式 */
.text-primary {
  color: #409eff !important;
}

.text-success {
  color: #67c23a !important;
}

.text-info {
  color: #909399 !important;
}

.text-warning {
  color: #e6a23c !important;
}

.text-danger {
  color: #f56c6c !important;
}

.font-weight-bold {
  font-weight: bold;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}

/* 费用计算规则样式 */
.calculation-rules-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.calculation-rules-card .el-card__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 6px 6px 0 0;
}

.rule-title {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rule-title i {
  font-size: 18px;
  margin-right: 8px;
}

.calculation-descriptions {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.rule-item {
  margin-bottom: 10px;
}

.rule-label {
  font-weight: 600;
  color: #333;
  display: inline-block;
  min-width: 100px;
}

.detail-item {
  margin-bottom: 10px;
  line-height: 1.8;
}

.detail-item label {
  font-weight: 600;
  color: #666;
  display: inline-block;
  min-width: 80px;
}

.fee-amount {
  color: #409eff;
  font-weight: 600;
}

.total-fee {
  color: #e6a23c;
  font-weight: bold;
  font-size: 16px;
}

.payable-amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}

.rule-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
}

.rule-icon {
  margin-right: 8px;
  font-size: 16px;
  min-width: 20px;
}

.meal-icon {
  color: #ff9800;
}

.education-icon {
  color: #2196f3;
}

.rate-icon {
  color: #4caf50;
}

.total-icon {
  color: #e91e63;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calculation-descriptions {
    --el-descriptions-item-trailing-colon: '';
  }
  
  .rule-text {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .rule-icon {
    margin-right: 4px;
  }
}
</style>
