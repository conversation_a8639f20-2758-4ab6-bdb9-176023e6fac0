import request from '@/utils/request'

// 查询课程排班列表
export function listSchedule(query) {
  return request({
    url: '/business/schedule/list',
    method: 'get',
    params: query
  })
}

// 查询课程排班列表（含关联信息）
export function listScheduleView(query) {
  return request({
    url: '/business/schedule/view/list',
    method: 'get',
    params: query
  })
}

// 根据排班ID获取详细信息（含关联信息）
export function getScheduleView(scheduleId) {
  return request({
    url: '/business/schedule/view/' + scheduleId,
    method: 'get'
  })
}

// 根据教师ID查询排班（含关联信息）
export function getScheduleByTeacher(teacherId) {
  return request({
    url: '/business/schedule/view/teacher/' + teacherId,
    method: 'get'
  })
}

// 根据班级ID查询课表（含关联信息）
export function getScheduleByClass(classId) {
  return request({
    url: '/business/schedule/view/class/' + classId,
    method: 'get'
  })
}

// 查询课程排班详细
export function getSchedule(scheduleId) {
  return request({
    url: '/business/schedule/' + scheduleId,
    method: 'get'
  })
}

// 新增课程排班
export function addSchedule(data) {
  return request({
    url: '/business/schedule',
    method: 'post',
    data: data
  })
}

// 修改课程排班
export function updateSchedule(data) {
  return request({
    url: '/business/schedule',
    method: 'put',
    data: data
  })
}

// 删除课程排班
export function delSchedule(scheduleId) {
  return request({
    url: '/business/schedule/' + scheduleId,
    method: 'delete'
  })
}

// 批量删除课程排班
export function delSchedules(scheduleIds) {
  return request({
    url: '/business/schedule/batch/' + scheduleIds,
    method: 'delete'
  })
}

// 复制课程排班
export function copySchedule(scheduleId, data) {
  return request({
    url: '/business/schedule/copy/' + scheduleId,
    method: 'post',
    data: data
  })
}

// 获取教师排班信息
export function getTeacherSchedule(query) {
  return request({
    url: '/business/schedule/teacher',
    method: 'get',
    params: query
  })
}

// 获取班级课表信息
export function getClassSchedule(query) {
  return request({
    url: '/business/schedule/class',
    method: 'get',
    params: query
  })
}

// 获取班级学生列表
export function getClassStudents(classId) {
  return request({
    url: '/business/schedule/class/' + classId + '/students',
    method: 'get'
  })
}

// 获取课程排班的学生列表
export function getScheduleStudents(scheduleId) {
  return request({
    url: '/business/schedule/' + scheduleId + '/students',
    method: 'get'
  })
}

// 检查排班时间冲突
export function checkScheduleConflict(data) {
  return request({
    url: '/business/schedule/conflict-check',
    method: 'post',
    data: data
  })
}

// 批量导入排班
export function importSchedule(data) {
  return request({
    url: '/business/schedule/import',
    method: 'post',
    data: data
  })
}

// 导出排班数据
export function exportSchedule(query) {
  return request({
    url: '/business/schedule/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}


// 获取排班统计信息
export function getScheduleStats(query) {
  return request({
    url: '/business/schedule/stats',
    method: 'get',
    params: query
  })
}

// 获取教师工作量统计
export function getTeacherWorkload(query) {
  return request({
    url: '/business/schedule/teacher-workload',
    method: 'get',
    params: query
  })
}

// 获取班级课程安排统计
export function getClassCourseStats(query) {
  return request({
    url: '/business/schedule/class-course-stats',
    method: 'get',
    params: query
  })
}

// 调课申请
export function applyScheduleChange(data) {
  return request({
    url: '/business/schedule/change-apply',
    method: 'post',
    data: data
  })
}

// 代课申请
export function applySubstitute(data) {
  return request({
    url: '/business/schedule/substitute-apply',
    method: 'post',
    data: data
  })
}

// 根据排班查询对应的考勤记录
export function getAttendanceBySchedule(scheduleId, attendanceDate) {
  return request({
    url: `/business/schedule/attendance/${scheduleId}`,
    method: 'get',
    params: {
      attendanceDate: attendanceDate
    }
  })
}

// 根据排班查询对应的学生名单
export function getStudentsBySchedule(scheduleId) {
  return request({
    url: '/business/schedule/students/' + scheduleId,
    method: 'get'
  })
}

// 生成考勤记录模板
export function generateAttendanceTemplate(scheduleId, attendanceDate) {
  return request({
    url: '/business/schedule/generateAttendance/' + scheduleId,
    method: 'post',
    params: {
      attendanceDate: attendanceDate
    }
  })
}

// 批量生成考勤记录模板
export function batchGenerateAttendanceTemplate(scheduleId, startDate, endDate) {
  return request({
    url: '/business/schedule/batchGenerateAttendance/' + scheduleId,
    method: 'post',
    params: {
      startDate: startDate,
      endDate: endDate
    }
  })
}

// 补课安排
export function arrangeMakeupClass(data) {
  return request({
    url: '/business/schedule/makeup-arrange',
    method: 'post',
    data: data
  })
}

// 获取可用教师列表（某个时间段）
export function getAvailableTeachers(query) {
  return request({
    url: '/business/schedule/available-teachers',
    method: 'get',
    params: query
  })
}

// 获取可用教室列表（某个时间段）
export function getAvailableClassrooms(query) {
  return request({
    url: '/business/schedule/available-classrooms',
    method: 'get',
    params: query
  })
}
