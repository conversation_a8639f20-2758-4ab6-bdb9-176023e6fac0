<template>
	<u-popup 
		v-model="visible" 
		mode="center" 
		border-radius="24"
		:mask-close-able="true"
		@close="handleCancel"
	>
		<view class="popup-content">
			<view class="popup-body">
				<view class="function-grid">
					<view class="function-card course-card" @click="handleCourseManagement">
						<view class="card-icon">📚</view>
						<text class="card-title">课程管理</text>
					</view>

					<view class="function-card enrollment-card" @click="handleStudentEnrollment">
						<view class="card-icon">📝</view>
						<text class="card-title">学生报名</text>
					</view>

					<view class="function-card attendance-card" @click="handleCustodyAttendance">
						<view class="card-icon">✅</view>
						<text class="card-title">托管考勤</text>
					</view>

					<view class="function-card statistics-card" @click="handleTeacherStatistics">
						<view class="card-icon">📊</view>
						<text class="card-title">课时统计</text>
					</view>

					<view class="function-card gift-record-card" @click="handleGiftRecord">
						<view class="card-icon">🎁</view>
						<text class="card-title">赠送记录</text>
					</view>

					<view class="function-card gift-rule-card" @click="handleGiftRule">
						<view class="card-icon">⚙️</view>
						<text class="card-title">课时赠送规则</text>
					</view>
				</view>
			</view>
			
			<view class="popup-footer">
				<view class="cancel-btn" @click="handleCancel">
					<text class="cancel-text">取消</text>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
export default {
	name: 'CustodyManagePopup',
	data() {
		return {
			visible: false
		}
	},
	methods: {
		show() {
			this.visible = true;
		},
		hide() {
			this.visible = false;
		},
		handleCourseManagement() {
			this.$emit('select', 'course');
			this.hide();
		},
		handleStudentEnrollment() {
			this.$emit('select', 'enrollment');
			this.hide();
		},
		handleCustodyAttendance() {
			this.$emit('select', 'attendance');
			this.hide();
		},
		handleTeacherStatistics() {
			this.$emit('select', 'statistics');
			this.hide();
		},
		handleGiftRecord() {
			this.$emit('select', 'gift-record');
			this.hide();
		},
		handleGiftRule() {
			this.$emit('select', 'gift-rule');
			this.hide();
		},
		handleCancel() {
			this.$emit('cancel');
			this.hide();
		}
	}
}
</script>

<style lang="scss" scoped>
.popup-content {
	width: 580rpx;
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
}



.popup-body {
	padding: 40rpx 30rpx 20rpx;
}

.function-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.function-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 32rpx 16rpx;
	background: #ffffff;
	border-radius: 16rpx;
	border: 2rpx solid #f0f2f7;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	min-height: 120rpx;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	&:active {
		transform: scale(0.95);

		&::before {
			opacity: 1;
		}
	}
}

.course-card {
	background: linear-gradient(135deg, #e8eaf6 0%, #f3f4f9 100%);
	border-color: #3F51B5;
	box-shadow: 0 8rpx 24rpx rgba(63, 81, 181, 0.15);

	&:active {
		background: linear-gradient(135deg, #c5cae9 0%, #e8eaf6 100%);
		box-shadow: 0 12rpx 32rpx rgba(63, 81, 181, 0.25);
	}
}

.enrollment-card {
	background: linear-gradient(135deg, #e3f2fd 0%, #f8fbff 100%);
	border-color: #2196F3;
	box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.15);

	&:active {
		background: linear-gradient(135deg, #bbdefb 0%, #e3f2fd 100%);
		box-shadow: 0 12rpx 32rpx rgba(33, 150, 243, 0.25);
	}
}

.attendance-card {
	background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
	border-color: #4CAF50;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.15);

	&:active {
		background: linear-gradient(135deg, #c8e6c9 0%, #e8f5e8 100%);
		box-shadow: 0 12rpx 32rpx rgba(76, 175, 80, 0.25);
	}
}

.statistics-card {
	background: linear-gradient(135deg, #fff3e0 0%, #fffbf0 100%);
	border-color: #FF9800;
	box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, 0.15);

	&:active {
		background: linear-gradient(135deg, #ffe0b2 0%, #fff3e0 100%);
		box-shadow: 0 12rpx 32rpx rgba(255, 152, 0, 0.25);
	}
}

.gift-record-card {
	background: linear-gradient(135deg, #fce4ec 0%, #fef7f0 100%);
	border-color: #E91E63;
	box-shadow: 0 8rpx 24rpx rgba(233, 30, 99, 0.15);

	&:active {
		background: linear-gradient(135deg, #f8bbd9 0%, #fce4ec 100%);
		box-shadow: 0 12rpx 32rpx rgba(233, 30, 99, 0.25);
	}
}

.gift-rule-card {
	background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
	border-color: #9C27B0;
	box-shadow: 0 8rpx 24rpx rgba(156, 39, 176, 0.15);

	&:active {
		background: linear-gradient(135deg, #e1bee7 0%, #f3e5f5 100%);
		box-shadow: 0 12rpx 32rpx rgba(156, 39, 176, 0.25);
	}
}

.card-icon {
	font-size: 48rpx;
	margin-bottom: 12rpx;
	display: block;
	filter: brightness(1.1);
}

.card-title {
	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
	text-align: center;
	line-height: 1.2;
}

.popup-footer {
	padding: 0 30rpx 30rpx;
	text-align: center;
}

.cancel-btn {
	padding: 20rpx 50rpx;
	background: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
	display: inline-block;

	&:active {
		background: #e9ecef;
		transform: scale(0.96);
	}
}

.cancel-text {
	font-size: 26rpx;
	color: #6c757d;
	font-weight: 500;
}

/* 动画效果 */
@keyframes slideIn {
	from {
		transform: translateY(20rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.popup-content {
	animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 卡片悬浮效果 */
.function-card {
	&:active {
		.card-icon {
			transform: scale(1.1);
		}

		.card-title {
			color: #333333;
		}
	}
}

.enrollment-card:active .card-title {
	color: #1976D2;
}

.attendance-card:active .card-title {
	color: #388E3C;
}

.statistics-card:active .card-title {
	color: #F57C00;
}

.gift-record-card:active .card-title {
	color: #C2185B;
}

.gift-rule-card:active .card-title {
	color: #7B1FA2;
}
</style>
