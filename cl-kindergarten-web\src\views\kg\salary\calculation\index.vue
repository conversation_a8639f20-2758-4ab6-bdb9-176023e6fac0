<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="计算类型" prop="calculationType">
        <el-select v-model="queryParams.calculationType" placeholder="请选择计算类型" clearable size="small">
          <el-option label="单个教师" value="single" />
          <el-option label="全体教师" value="all" />
        </el-select>
      </el-form-item>
      <el-form-item label="教师" prop="teacherId" v-if="queryParams.calculationType === 'single'">
        <el-select v-model="queryParams.teacherId" placeholder="请选择教师" clearable size="small" filterable>
          <el-option
            v-for="item in teacherList"
            :key="item.teacherId"
            :label="item.teacherName"
            :value="item.teacherId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计算月份">
        <el-date-picker
          v-model="calculationMonth"
          type="month"
          placeholder="选择月份"
          size="small"
          @change="handleMonthChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleCalculate">开始计算</el-button>
        <el-button type="success" icon="el-icon-view" size="mini" @click="handlePreview">预览</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工资统计卡片 -->
    <el-row :gutter="20" class="mb20" v-if="salaryStatistics">
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">教师总数</div>
          <div class="card-value">{{ salaryStatistics.totalTeachers || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">工资总额</div>
          <div class="card-value text-success">¥{{ salaryStatistics.totalSalary || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">平均工资</div>
          <div class="card-value text-primary">¥{{ salaryStatistics.averageSalary || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">已发放数</div>
          <div class="card-value text-warning">{{ salaryStatistics.paidCount || 0 }}</div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" v-if="calculationResults.length > 0">
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleGeneratePayslips"
          v-hasPermi="['kg:salary:generate']"
        >生成工资单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-money"
          size="mini"
          :disabled="!hasValidSalaryRecords"
          @click="handleConfirmPayment"
          v-hasPermi="['kg:salary:confirm']"
        >确认发放</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExportReport"
          v-hasPermi="['kg:salary:report']"
        >导出报表</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-refresh"
          size="mini"
          @click="handleRecalculateAll"
          v-hasPermi="['kg:salary:calculate']"
        >重新计算</el-button>
      </el-col>
    </el-row>

    <!-- 工资计算结果表格 -->
    <el-table 
      v-loading="loading" 
      :data="calculationResults" 
      @selection-change="handleSelectionChange"
      :summary-method="getSummaries"
      show-summary
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="教师姓名" align="center" prop="teacherInfo.teacherName" width="100" />
      <el-table-column label="职位" align="center" prop="teacherInfo.position" width="80" />
      <el-table-column label="出勤天数" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceDays : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="出勤率" align="center" width="80">
        <template slot-scope="scope">
          <span :class="getAttendanceRateClass(scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0)">
            {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0 }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column label="基本工资" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-primary">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.baseSalary : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="满勤奖" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-success">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.attendanceBonus : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时费" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-info">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.courseFee : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报名奖励" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-warning">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.enrollmentBonus : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="新生奖励" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-success">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.newStudentBonus : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出勤率奖励" align="center" width="90">
        <template slot-scope="scope">
          <span :class="getAttendanceRateBonusClass(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.attendanceRateBonus : 0)">
            ¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.attendanceRateBonus : 0) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="退园扣款" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-danger">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.withdrawalDeduction : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应发工资" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-success font-weight-bold">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.grossSalary : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="社保代扣" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-danger">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.socialInsurance : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实发工资" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-primary font-weight-bold">¥{{ formatMoney(scope.row.salaryBreakdown ? scope.row.salaryBreakdown.netSalary : 0) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发放状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.error" type="danger">计算失败</el-tag>
          <el-tag v-else-if="scope.row.salaryStatus === 'paid'" type="success">已发放</el-tag>
          <el-tag v-else-if="scope.row.salaryStatus === 'confirmed'" type="warning">已确认</el-tag>
          <el-tag v-else-if="scope.row.salaryStatus === 'calculated'" type="primary">已生成</el-tag>
          <el-tag v-else type="info">预览</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
          >详情</el-button>
          <el-button v-if="scope.row.salaryStatus === 'calculated'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAdjust(scope.row)"
            v-hasPermi="['kg:salary:adjust']"
            :disabled="!hasSalaryRecord(scope.row)"
            :title="hasSalaryRecord(scope.row) ? '调整工资' : '请先生成工资单后再进行调整'"
          >调整</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-pie-chart"
            @click="handleViewCourseStats(scope.row)"
          >课时</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 工资详情对话框 -->
    <el-dialog title="工资计算详情" :visible.sync="detailDialogVisible" width="900px" append-to-body>
      <div v-if="selectedCalculation">
        <el-descriptions title="教师信息" :column="3" border>
          <el-descriptions-item label="教师姓名">{{ selectedCalculation.teacherInfo.teacherName }}</el-descriptions-item>
          <el-descriptions-item label="职位">{{ selectedCalculation.teacherInfo.position }}</el-descriptions-item>
          <el-descriptions-item label="入职日期">{{ selectedCalculation.teacherInfo.hireDate }}</el-descriptions-item>
        </el-descriptions>
        
        <el-descriptions title="考勤统计" :column="4" border class="mt20">
          <el-descriptions-item label="应出勤天数">{{ selectedCalculation.attendanceStats.workDays }}</el-descriptions-item>
          <el-descriptions-item label="实际出勤">{{ selectedCalculation.attendanceStats.attendanceDays }}</el-descriptions-item>
          <el-descriptions-item label="缺勤天数">{{ selectedCalculation.attendanceStats.absentDays }}</el-descriptions-item>
          <el-descriptions-item label="出勤率">{{ selectedCalculation.attendanceStats.attendanceRate }}%</el-descriptions-item>
        </el-descriptions>
        
        <el-descriptions title="工资明细" :column="2" border class="mt20">
          <el-descriptions-item label="基本工资">¥{{ formatMoney(selectedCalculation.salaryBreakdown.baseSalary) }}</el-descriptions-item>
          <el-descriptions-item label="满勤奖">¥{{ formatMoney(selectedCalculation.salaryBreakdown.attendanceBonus) }}</el-descriptions-item>
          <el-descriptions-item label="课时费">¥{{ formatMoney(selectedCalculation.salaryBreakdown.courseFee) }}</el-descriptions-item>
          <el-descriptions-item label="报名奖励">¥{{ formatMoney(selectedCalculation.salaryBreakdown.enrollmentBonus) }}</el-descriptions-item>
          <el-descriptions-item label="出勤率奖励">¥{{ formatMoney(selectedCalculation.salaryBreakdown.attendanceRateBonus) }}</el-descriptions-item>
          <el-descriptions-item label="新生奖励">¥{{ formatMoney(selectedCalculation.salaryBreakdown.newStudentBonus) }}</el-descriptions-item>
          <el-descriptions-item label="退园扣款">¥{{ formatMoney(selectedCalculation.salaryBreakdown.withdrawalDeduction) }}</el-descriptions-item>
          <el-descriptions-item label="社保代扣">¥{{ formatMoney(selectedCalculation.salaryBreakdown.socialInsurance) }}</el-descriptions-item>
          <el-descriptions-item label="应发工资">¥{{ formatMoney(selectedCalculation.salaryBreakdown.grossSalary) }}</el-descriptions-item>
          <el-descriptions-item label="实发工资">¥{{ formatMoney(selectedCalculation.salaryBreakdown.netSalary) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 工资调整对话框 -->
    <el-dialog title="工资调整" :visible.sync="adjustDialogVisible" width="500px" append-to-body>
      <el-form ref="adjustForm" :model="adjustForm" :rules="adjustRules" label-width="100px">
        <el-form-item label="调整类型" prop="adjustType">
          <el-select v-model="adjustForm.adjustType" placeholder="请选择调整类型">
            <el-option label="基本工资" value="base_salary" />
            <el-option label="满勤奖" value="attendance_bonus" />
            <el-option label="课时费" value="course_fee" />
            <el-option label="报名奖励" value="enrollment_bonus" />
    
          </el-select>
        </el-form-item>
        <el-form-item label="调整金额" prop="adjustAmount">
          <el-input-number 
            v-model="adjustForm.adjustAmount" 
            :precision="2" 
            :step="1" 
            placeholder="正数为增加，负数为减少"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input v-model="adjustForm.reason" type="textarea" placeholder="请输入调整原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdjust">确 定</el-button>
        <el-button @click="cancelAdjust">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 课时费统计对话框 -->
    <el-dialog title="课时费统计" :visible.sync="courseStatsDialogVisible" width="700px" append-to-body>
      <div v-if="courseStatistics">
        <el-table :data="courseStatistics.courseDetails" border>
          <el-table-column label="课程名称" align="center" prop="courseName" />
          <el-table-column label="授课次数" align="center" prop="teachingCount" />
          <el-table-column label="学生人数" align="center" prop="studentCount" />
          <el-table-column label="课时单价" align="center" prop="hourlyRate">
            <template slot-scope="scope">
              ¥{{ scope.row.hourlyRate }}
            </template>
          </el-table-column>
          <el-table-column label="课时费小计" align="center" prop="subtotal">
            <template slot-scope="scope">
              ¥{{ scope.row.subtotal }}
            </template>
          </el-table-column>
        </el-table>
        <div class="mt20 text-right">
          <span class="font-weight-bold">课时费总计：¥{{ courseStatistics.totalCourseFee }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  calculateMonthlySalary,
  calculateAllSalaryBatch,
  previewSalaryCalculation,
  getSalaryCalculationRules,
  generatePayslips,
  adjustSalary,
  getSalaryStatistics,
  confirmSalaryPayment,
  generateSalaryReport,
  getCourseFeeStatistics
} from "@/api/kg/salary/calculation";
import { listAllTeacher } from "@/api/kg/teacher/info";

export default {
  name: "SalaryCalculation",
  data() {
    return {
      // 查询参数
      queryParams: {
        calculationType: 'single',
        teacherId: null,
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1
      },
      // 计算月份
      calculationMonth: new Date(),
      // 加载状态
      loading: false,
      // 计算结果
      calculationResults: [],
      // 工资统计
      salaryStatistics: null,
      // 选中的记录
      ids: [],
      multiple: true,
      // 基础数据
      teacherList: [],
      // 详情对话框
      detailDialogVisible: false,
      selectedCalculation: null,
      // 调整对话框
      adjustDialogVisible: false,
      adjustForm: {
        salaryId: null,
        teacherId: null, // 添加teacherId字段
        adjustType: '',
        adjustAmount: 0,
        reason: ''
      },
      adjustRules: {
        adjustType: [
          { required: true, message: "调整类型不能为空", trigger: "change" }
        ],
        adjustAmount: [
          { required: true, message: "调整金额不能为空", trigger: "blur" }
        ],
        reason: [
          { required: true, message: "调整原因不能为空", trigger: "blur" }
        ]
      },
      // 课时统计对话框
      courseStatsDialogVisible: false,
      courseStatistics: null
    };
  },
  computed: {
    /** 是否有有效的工资记录（已生成工资单） */
    hasValidSalaryRecords() {
      return this.calculationResults.some(item => this.hasSalaryRecord(item));
    }
  },
  created() {
    this.getTeacherList();
    this.getSalaryStats();
  },
  methods: {
    /** 获取教师列表 */
    getTeacherList() {
      listAllTeacher().then(response => {
        this.teacherList = response.data;
      });
    },
    
    /** 获取工资统计 */
    getSalaryStats() {
      getSalaryStatistics(this.queryParams.year, this.queryParams.month).then(response => {
        const data = response.data;
        // 映射后端返回的数据结构到前端期望的字段名
        this.salaryStatistics = {
          totalTeachers: data.totalRecords || 0,              // 教师总数
          totalSalary: data.salaryBreakdown?.totalNetSalary || 0,  // 工资总额（使用实发工资总额）
          averageSalary: data.salaryBreakdown?.averageNetSalary || 0, // 平均工资（使用平均实发工资）
          paidCount: data.statusStatistics?.paid || 0          // 已发放数（如果后端有此字段）
        };
      });
    },
    
    /** 月份改变 */
    handleMonthChange(date) {
      if (date) {
        this.queryParams.year = date.getFullYear();
        this.queryParams.month = date.getMonth() + 1;
        this.getSalaryStats();
      }
    },
    
    /** 开始计算 */
    handleCalculate() {
      if (!this.validateParams()) {
        return;
      }
      
      this.loading = true;
      
      let apiCall;
      if (this.queryParams.calculationType === 'single') {
        apiCall = calculateMonthlySalary(
          this.queryParams.teacherId,
          this.queryParams.year,
          this.queryParams.month
        );
      } else {
        apiCall = calculateAllSalaryBatch(
          this.queryParams.year,
          this.queryParams.month
        );
      }
      
      apiCall.then(response => {
        if (this.queryParams.calculationType === 'single') {
          // 单个教师计算结果，直接使用后端返回的真实数据
          this.calculationResults = [response.data];
        } else {
          // 批量计算结果，直接使用后端返回的真实数据
          this.calculationResults = response.data.teacherResults || [];
        }
        
        this.loading = false;
        this.$message.success('计算完成');
        this.getSalaryStats();
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 预览计算 */
    handlePreview() {
      if (this.queryParams.calculationType !== 'single' || !this.queryParams.teacherId) {
        this.$message.warning('预览功能仅支持单个教师计算');
        return;
      }
      
      previewSalaryCalculation(
        this.queryParams.teacherId,
        this.queryParams.year,
        this.queryParams.month
      ).then(response => {
        this.selectedCalculation = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 参数验证 */
    validateParams() {
      if (this.queryParams.calculationType === 'single' && !this.queryParams.teacherId) {
        this.$message.warning('请选择教师');
        return false;
      }
      
      if (!this.queryParams.year || !this.queryParams.month) {
        this.$message.warning('请选择计算月份');
        return false;
      }
      
      return true;
    },
    
    /** 重置查询 */
    resetQuery() {
      this.resetForm("queryForm");
      this.calculationResults = [];
      this.salaryStatistics = null;
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      // 确认发放时需要使用salaryId，但只有已生成工资单的记录才有有效的salaryId
      this.ids = selection
        .filter(item => this.hasSalaryRecord(item)) // 只选择已生成工资单的记录
        .map(item => item.teacherInfo.salaryId); // 使用salaryId而不是teacherId
      this.multiple = !selection.length;
    },
    
    /** 生成工资单 */
    handleGeneratePayslips() {
      if (this.calculationResults.length === 0) {
        this.$message.warning('没有可生成的工资单');
        return;
      }
      
      const validResults = this.calculationResults.filter(result => !result.error);
      
      if (validResults.length === 0) {
        this.$message.warning('没有计算成功的记录');
        return;
      }
      
      this.$confirm(`确认生成 ${validResults.length} 条工资单?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        generatePayslips(validResults).then(response => {
          this.$message.success(response.msg);
          this.calculationResults = [];
          this.getSalaryStats();
        });
      });
    },
    
    /** 确认发放 */
    handleConfirmPayment() {
      // 检查是否有已生成工资单的记录
      const validRecords = this.calculationResults.filter(item => this.hasSalaryRecord(item));
      
      if (validRecords.length === 0) {
        this.$message.warning('没有可确认发放的工资记录，请先生成工资单');
        return;
      }
      
      // 获取所有已生成工资单的salaryId
      const salaryIds = validRecords.map(item => item.teacherInfo.salaryId);
      
      if (salaryIds.length === 0) {
        this.$message.warning('请选择要确认发放的工资记录');
        return;
      }
      
      this.$confirm(`确认发放 ${salaryIds.length} 条工资记录?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        confirmSalaryPayment(salaryIds).then(response => {
          this.$message.success(response.msg || "确认成功");
          this.handleCalculate();
        });
      });
    },
    
    /** 导出报表 */
    handleExportReport() {
      generateSalaryReport(this.queryParams.year, this.queryParams.month).then(response => {
        this.$message.success("报表生成成功");
        // TODO: 下载报表文件
      });
    },
    
    /** 重新计算全部 */
    handleRecalculateAll() {
      this.handleCalculate();
    },
    
    /** 查看详情 */
    handleViewDetail(row) {
      this.selectedCalculation = row;
      this.detailDialogVisible = true;
    },
    
    /** 工资调整 */
    handleAdjust(row) {
      // 检查是否有真实的工资记录
      if (!this.hasSalaryRecord(row)) {
        this.$message.warning('请先点击“生成工资单”按钮保存工资计算结果，然后再进行调整操作');
        return;
      }
      
      // 使用后端返回的真实 salaryId
      this.adjustForm.salaryId = row.teacherInfo?.salaryId;
      this.adjustForm.teacherId = row.teacherInfo?.teacherId;
      this.adjustForm.adjustType = '';
      this.adjustForm.adjustAmount = 0;
      this.adjustForm.reason = '';
      
      if (!this.adjustForm.salaryId) {
        this.$message.warning('当前记录缺少工资ID，无法进行调整操作');
        return;
      }
      
      if (!this.adjustForm.teacherId) {
        this.$message.warning('当前记录缺少教师ID，无法进行调整操作');
        return;
      }
      
      this.adjustDialogVisible = true;
    },
    
    /** 查看课时统计 */
    handleViewCourseStats(row) {
      getCourseFeeStatistics(
        row.teacherInfo.teacherId,
        this.queryParams.year,
        this.queryParams.month
      ).then(response => {
        this.courseStatistics = response.data;
        this.courseStatsDialogVisible = true;
      });
    },
    
    /** 提交调整 */
    submitAdjust() {
      this.$refs["adjustForm"].validate(valid => {
        if (valid) {
          adjustSalary(
            this.adjustForm.salaryId,
            this.adjustForm.adjustType,
            this.adjustForm.adjustAmount,
            this.adjustForm.reason
          ).then(response => {
            this.$message.success("调整成功");
            this.adjustDialogVisible = false;
            this.handleCalculate();
          });
        }
      });
    },
    
    /** 取消调整 */
    cancelAdjust() {
      this.adjustDialogVisible = false;
    },
    
    /** 获取出勤率样式类 */
    getAttendanceRateClass(rate) {
      if (rate >= 95) return 'text-success';
      if (rate >= 85) return 'text-warning';
      return 'text-danger';
    },
    
    /** 获取出勤率奖励样式类 */
    getAttendanceRateBonusClass(bonus) {
      if (bonus > 0) return 'text-success font-weight-bold';
      if (bonus < 0) return 'text-danger font-weight-bold';
      return 'text-muted';
    },
    
    /** 格式化金额显示 */
    formatMoney(value) {
      if (value === null || value === undefined || isNaN(value)) {
        return '0.00';
      }
      return Number(value).toFixed(2);
    },
    
    /** 检查是否有真实的工资记录 */
    hasSalaryRecord(row) {
      // 检查是否有真实的salaryId（不等于teacherId的临时ID）
      const salaryId = row.teacherInfo?.salaryId;
      const teacherId = row.teacherInfo?.teacherId;
      
      // 如果salaryId存在且不等于teacherId，说明已经生成了真实的工资记录
      return salaryId && salaryId !== teacherId;
    },
    
    /** 表格合计行 */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        
        const values = data.map(item => {
          if (!item.salaryBreakdown) return 0;
          
          switch (column.property) {
            case 'salaryBreakdown.baseSalary':
              return Number(item.salaryBreakdown.baseSalary);
            case 'salaryBreakdown.attendanceBonus':
              return Number(item.salaryBreakdown.attendanceBonus);
            case 'salaryBreakdown.courseFee':
              return Number(item.salaryBreakdown.courseFee);
            case 'salaryBreakdown.grossSalary':
              return Number(item.salaryBreakdown.grossSalary);
            case 'salaryBreakdown.netSalary':
              return Number(item.salaryBreakdown.netSalary);
            default:
              return 0;
          }
        });
        
        if (values.every(value => !isNaN(value))) {
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = '¥' + sum.toFixed(2);
        } else {
          sums[index] = '';
        }
      });
      
      return sums;
    }
  }
};
</script>

<style scoped>
.statistics-card {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.text-primary {
  color: #409eff !important;
}

.text-success {
  color: #67c23a !important;
}

.text-info {
  color: #909399 !important;
}

.text-warning {
  color: #e6a23c !important;
}

.text-danger {
  color: #f56c6c !important;
}

.font-weight-bold {
  font-weight: bold;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}

.text-right {
  text-align: right;
}
</style>
