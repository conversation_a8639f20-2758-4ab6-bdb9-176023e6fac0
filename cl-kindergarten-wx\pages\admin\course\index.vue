<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">课程管理</text>
				</view>
			</view>
		</view>

		<!-- 查询筛选 -->
		<view class="search-section">
			<view class="search-form">
				<view class="form-row">
					<view class="form-item">
						<text class="label">课程名称：</text>
						<input
							class="input"
							v-model="queryParams.courseName"
							placeholder="请输入课程名称"
							@confirm="handleSearch"
						/>
					</view>
				</view>
				<view class="form-row">
					<view class="form-item">
						<text class="label">课程类型：</text>
						<picker @change="onCourseTypeChange" :value="courseTypeIndex" :range="courseTypeOptions">
							<view class="picker-value">
								{{ courseTypeIndex >= 0 ? courseTypeOptions[courseTypeIndex] : '请选择课程类型' }}
								<u-icon name="arrow-down" size="14" color="#999"></u-icon>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="label">授课教师：</text>
						<picker @change="onTeacherChange" :value="teacherIndex" :range="teacherOptions" range-key="teacherName">
							<view class="picker-value">
								{{ teacherIndex >= 0 ? teacherOptions[teacherIndex].teacherName : '请选择授课教师' }}
								<u-icon name="arrow-down" size="14" color="#999"></u-icon>
							</view>
						</picker>
					</view>
				</view>
				<view class="search-buttons">
					<button class="btn-search" @click="handleSearch">查询</button>
					<button class="btn-reset" @click="handleReset">重置</button>
				</view>
			</view>
		</view>

		<!-- 课程列表 -->
		<view class="course-list">
			<view v-if="courseList.length === 0" class="empty-state">
				<view class="empty-icon">📚</view>
				<text class="empty-title">暂无课程</text>
				<text class="empty-desc">点击右下角+号添加课程</text>
			</view>

			<view v-else>
				<view v-for="(course, index) in courseList" :key="course.courseId" class="course-item">
					<view class="course-header">
						<view class="course-name">{{ course.courseName }}</view>
						<view class="status-badge" :class="course.status === '0' ? 'status-active' : 'status-inactive'">
							{{ course.status === '0' ? '正常' : '停用' }}
						</view>
					</view>

					<view class="course-content">
						<view class="course-row">
							<text class="course-label">课程类型：</text>
							<text class="course-value">{{ getCourseTypeText(course.courseType) }}</text>
						</view>
						<view class="course-row">
							<text class="course-label">单节课价格：</text>
							<text class="course-value">¥{{ course.pricePerSession }}</text>
						</view>
						<view class="course-row">
							<text class="course-label">课程时长：</text>
							<text class="course-value">{{ course.duration }}分钟</text>
						</view>
						<view class="course-row">
							<text class="course-label">班级人数：</text>
							<text class="course-value">{{ course.minStudents }}-{{ course.maxStudents }}人</text>
						</view>
						<view class="course-row">
							<text class="course-label">授课教师：</text>
							<text class="course-value">{{ course.defaultTeacherName || '未设置' }}</text>
						</view>
						<view v-if="course.remark" class="course-row">
							<text class="course-label">备注：</text>
							<text class="course-value remark">{{ course.remark }}</text>
						</view>
					</view>

					<view class="course-actions">
						<button class="btn-edit" @click="handleEdit(course)">编辑</button>
						<button class="btn-toggle" @click="handleToggleStatus(course)">
							{{ course.status === '0' ? '停用' : '启用' }}
						</button>
						<button class="btn-delete" @click="handleDelete(course)">删除</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动新增按钮 -->
		<view class="floating-add-btn" @click="handleAdd">
			<u-icon name="plus" color="#ffffff" size="24"></u-icon>
		</view>
	</view>
</template>

<script>
import {toast} from '@/utils/utils.js'
import {
	listCourse,
	deleteCourse,
	changeStatus
} from '@/api/course.js'
import {
	listAllTeacher
} from '@/api/teacher.js'

export default {
	data() {
		return {
			// 查询参数
			queryParams: {
				courseName: '',
				courseType: '',
				defaultTeacherId: ''
			},
			// 课程列表
			courseList: [],
			// 教师列表
			teacherList: [],
			// 课程类型选项
			courseTypeOptions: ['全部', '托管课程', '兴趣班', '特色课程'],
			courseTypeIndex: 0,
			// 教师选项
			teacherOptions: [],
			teacherIndex: -1,
			// 加载状态
			loading: false
		}
	},

	onLoad() {
		this.getTeacherList()
		this.getList()
	},

	onShow() {
		// 页面显示时刷新列表（从表单页面返回时）
		this.getList()
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 获取课程列表
		async getList() {
			try {
				this.loading = true
				const params = {
					...this.queryParams
				}
				// 如果选择了"全部"，则不传courseType参数
				if (this.courseTypeIndex === 0) {
					delete params.courseType
				}
				// 如果没有选择教师，则不传defaultTeacherId参数
				if (this.teacherIndex < 0) {
					delete params.defaultTeacherId
				}

				const res = await listCourse(params)
				if (res && res.code === 200) {
					this.courseList = res.rows || []
					// 检查第一个课程的教师信息
					if (this.courseList.length > 0) {
						console.log('第一个课程的教师信息:', {
							defaultTeacherId: this.courseList[0].defaultTeacherId,
							defaultTeacherName: this.courseList[0].defaultTeacherName
						})
					}
				} else {
					toast(res?.msg)
				}
			} catch (error) {
				toast('网络连接失败，请检查网络设置')
			} finally {
				this.loading = false
			}
		},

		// 获取教师列表
		async getTeacherList() {
			try {
				console.log('开始获取教师列表...')
				const res = await listAllTeacher()
				console.log('教师列表API响应:', res)
				if (res && res.code === 200) {
					this.teacherList = res.data || []
					this.teacherOptions = [{ teacherName: '全部', teacherId: '' }, ...this.teacherList]
					console.log('教师列表数据:', this.teacherList)
					console.log('教师选项:', this.teacherOptions)
				} else {
					console.error('获取教师列表失败，响应:', res)
				}
			} catch (error) {
				console.error('获取教师列表失败:', error)
			}
		},

		// 课程类型选择
		onCourseTypeChange(e) {
			this.courseTypeIndex = e.detail.value
			if (this.courseTypeIndex > 0) {
				// 映射课程类型值
				const typeMap = { 1: '1', 2: '2', 3: '3' } // 托管课程=1, 兴趣班=2, 特色课程=3
				this.queryParams.courseType = typeMap[this.courseTypeIndex]
			} else {
				this.queryParams.courseType = ''
			}
		},

		// 教师选择
		onTeacherChange(e) {
			this.teacherIndex = e.detail.value
			if (this.teacherIndex > 0) {
				this.queryParams.defaultTeacherId = this.teacherOptions[this.teacherIndex].teacherId
			} else {
				this.queryParams.defaultTeacherId = ''
			}
		},

		// 查询
		handleSearch() {
			this.getList()
		},

		// 重置
		handleReset() {
			this.courseTypeIndex = 0
			this.teacherIndex = 0
			this.queryParams = {
				courseName: '',
				courseType: '',
				defaultTeacherId: ''
			}
			this.getList()
		},

		// 新增课程
		handleAdd() {
			uni.navigateTo({
				url: '/pages/admin/course/form?type=add'
			})
		},

		// 编辑课程
		handleEdit(course) {
			uni.navigateTo({
				url: `/pages/admin/course/form?type=edit&courseId=${course.courseId}`
			})
		},

		// 切换状态
		handleToggleStatus(course) {
			const newStatus = course.status === '0' ? '1' : '0'
			const statusText = newStatus === '0' ? '启用' : '停用'

			uni.showModal({
				title: '确认操作',
				content: `确定要${statusText}课程"${course.courseName}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await changeStatus(course.courseId, newStatus)
							if (result.code === 200) {
								toast(`${statusText}成功`)
								this.getList()
							} else {
								toast(result.msg || `${statusText}失败`)
							}
						} catch (error) {
							console.error(`${statusText}失败:`, error)
							toast(`${statusText}失败`)
						}
					}
				}
			})
		},

		// 删除课程
		handleDelete(course) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除课程"${course.courseName}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await deleteCourse(course.courseId)
							if (result.code === 200) {
								toast('删除成功')
								this.getList()
							} else {
								toast(result.msg || '删除失败')
							}
						} catch (error) {
							console.error('删除失败:', error)
							toast('删除失败')
						}
					}
				}
			})
		},

		// 获取课程类型文本
		getCourseTypeText(courseType) {
			const typeMap = {
				'1': '托管课程',
				'2': '兴趣班',
				'3': '特色课程'
			}
			return typeMap[courseType] || '未知类型'
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 查询筛选 */
.search-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.search-form {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.form-row {
	display: flex;
	gap: 20rpx;
}

.form-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.label {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.input {
	height: 70rpx;
	padding: 0 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	box-sizing: border-box;

	&:focus {
		border-color: #667eea;
		background: #ffffff;
	}
}

.picker-value {
	height: 70rpx;
	padding: 0 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.search-buttons {
	display: flex;
	gap: 20rpx;
	margin-top: 10rpx;
}

.btn-search, .btn-reset {
	flex: 1;
	height: 70rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
}

.btn-search {
	background: #667eea;
	color: #ffffff;
}

.btn-reset {
	background: #f8f9fa;
	color: #666666;
}



/* 课程列表 */
.course-list {
	padding: 0 20rpx 40rpx;
}

.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	margin: 20rpx 0;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	display: block;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-desc {
	font-size: 26rpx;
	color: #666666;
	display: block;
	line-height: 1.5;
}

.course-item {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.course-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.course-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
}

.status-active {
	background: #e8f5e8;
	color: #52c41a;
}

.status-inactive {
	background: #f5f5f5;
	color: #999999;
}

.course-content {
	margin-bottom: 30rpx;
}

.course-row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.course-label {
	font-size: 26rpx;
	color: #666666;
	min-width: 160rpx;
}

.course-value {
	font-size: 26rpx;
	color: #333333;
	flex: 1;

	&.remark {
		color: #999999;
		font-style: italic;
	}
}

.course-actions {
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
}

.btn-edit, .btn-toggle, .btn-delete {
	flex: 1;
	height: 60rpx;
	border-radius: 8rpx;
	font-size: 26rpx;
	border: none;
}

.btn-edit {
	background: #1890ff;
	color: #ffffff;
}

.btn-toggle {
	background: #52c41a;
	color: #ffffff;
}

.btn-delete {
	background: #ff4d4f;
	color: #ffffff;
}

/* 浮动新增按钮 */
.floating-add-btn {
	position: fixed;
	bottom: 120rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
	z-index: 999;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.6);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 50%;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
		pointer-events: none;
	}
}

/* 浮动按钮动画效果 */
@keyframes float {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-8rpx);
	}
}

.floating-add-btn {
	animation: float 3s ease-in-out infinite;
}
</style>
