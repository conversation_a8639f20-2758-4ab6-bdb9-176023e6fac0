package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgClassSchedule;

/**
 * 班级课程时间Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface IKgClassScheduleService 
{
    /**
     * 查询班级课程时间
     * 
     * @param scheduleId 班级课程时间ID
     * @return 班级课程时间
     */
    public KgClassSchedule selectKgClassScheduleById(Long scheduleId);

    /**
     * 查询班级课程时间列表
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 班级课程时间集合
     */
    public List<KgClassSchedule> selectKgClassScheduleList(KgClassSchedule kgClassSchedule);

    /**
     * 新增班级课程时间
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 结果
     */
    public int insertKgClassSchedule(KgClassSchedule kgClassSchedule);

    /**
     * 修改班级课程时间
     * 
     * @param kgClassSchedule 班级课程时间
     * @return 结果
     */
    public int updateKgClassSchedule(KgClassSchedule kgClassSchedule);

    /**
     * 批量删除班级课程时间
     * 
     * @param scheduleIds 需要删除的班级课程时间ID
     * @return 结果
     */
    public int deleteKgClassScheduleByIds(Long[] scheduleIds);

    /**
     * 删除班级课程时间信息
     * 
     * @param scheduleId 班级课程时间ID
     * @return 结果
     */
    public int deleteKgClassScheduleById(Long scheduleId);

    // ==================== 扩展方法 ====================

    /**
     * 根据教师ID查询排班信息
     * 
     * @param teacherId 教师ID
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 排班信息列表
     */
    public List<KgClassSchedule> selectScheduleByTeacherId(Long teacherId, String startDate, String endDate);

    /**
     * 根据班级ID查询课表信息
     * 
     * @param classId 班级ID
     * @param viewType 查看类型（current/history）
     * @return 课表信息列表
     */
    public List<KgClassSchedule> selectScheduleByClassId(Long classId, String viewType);

    /**
     * 检查排班时间冲突
     * 
     * @param kgClassSchedule 排班信息
     * @return 冲突的排班列表
     */
    public List<KgClassSchedule> checkScheduleConflict(KgClassSchedule kgClassSchedule);

    /**
     * 获取排班统计信息
     * 
     * @param scheduleId 排班ID（可选）
     * @param teacherId 教师ID（可选）
     * @param classId 班级ID（可选）
     * @return 统计信息Map
     */
    public java.util.Map<String, Object> getScheduleStats(Long scheduleId, Long teacherId, Long classId);

    /**
     * 获取教师工作量统计
     * 
     * @param teacherId 教师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工作量统计
     */
    public java.util.Map<String, Object> getTeacherWorkload(Long teacherId, String startDate, String endDate);

    /**
     * 获取班级课程安排统计
     * 
     * @param classId 班级ID
     * @return 课程安排统计
     */
    public java.util.Map<String, Object> getClassCourseStats(Long classId);

    /**
     * 获取某个时间段可用的教师列表
     * 
     * @param dayOfWeek 星期几
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeScheduleId 排除的排班ID（修改时用）
     * @return 可用教师ID列表
     */
    public List<Long> getAvailableTeachers(Long dayOfWeek, String startTime, String endTime, Long excludeScheduleId);

    /**
     * 复制排班到其他时间
     * 
     * @param sourceScheduleId 源排班ID
     * @param targetDates 目标日期列表
     * @return 复制结果
     */
    public int copySchedule(Long sourceScheduleId, List<String> targetDates);

    /**
     * 批量导入排班
     * 
     * @param scheduleList 排班列表
     * @param isUpdateSupport 是否支持更新
     * @param operName 操作人
     * @return 导入结果信息
     */
    public String importSchedule(List<KgClassSchedule> scheduleList, Boolean isUpdateSupport, String operName);

    /**
     * 生成考勤模板（基于排班）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param classId 班级ID
     * @param teacherId 教师ID
     * @return 结果
     */
    public int generateAttendanceTemplate(String startDate, String endDate, Long classId, Long teacherId);

    // ==================== 视图查询方法 ====================

    /**
     * 从视图查询排班信息列表（包含关联数据）
     * 
     * @param kgClassSchedule 排班查询条件
     * @return 排班信息列表
     */
    public List<java.util.Map<String, Object>> selectScheduleViewList(KgClassSchedule kgClassSchedule);

    /**
     * 从视图查询单个排班信息（包含关联数据）
     * 
     * @param scheduleId 排班ID
     * @return 排班信息
     */
    public java.util.Map<String, Object> selectScheduleViewById(Long scheduleId);

    /**
     * 根据教师ID从视图查询排班
     * 
     * @param teacherId 教师ID
     * @return 排班信息列表
     */
    public List<java.util.Map<String, Object>> selectScheduleViewByTeacherId(Long teacherId);

    /**
     * 根据班级ID从视图查询课表
     * 
     * @param classId 班级ID
     * @return 课表信息列表
     */
    public List<java.util.Map<String, Object>> selectScheduleViewByClassId(Long classId);
    
    /**
     * 根据排班信息查询对应的考勤记录
     * 
     * @param scheduleId 排班ID
     * @param attendanceDate 考勤日期(可选)
     * @return 考勤记录列表
     */
    public List<java.util.Map<String, Object>> getAttendanceBySchedule(Long scheduleId, String attendanceDate);
    
    /**
     * 根据排班信息查询对应的学生名单
     * 
     * @param scheduleId 排班ID
     * @return 学生名单列表
     */
    public List<java.util.Map<String, Object>> getStudentsBySchedule(Long scheduleId);
    
    /**
     * 根据排班生成考勤记录模板
     * 
     * @param scheduleId 排班ID
     * @param attendanceDate 考勤日期
     * @return 生成的记录数
     */
    int generateAttendanceTemplate(Long scheduleId, String attendanceDate);
    
    /**
     * 批量生成考勤记录模板
     * 
     * @param scheduleId 排班ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的记录数
     */
    int batchGenerateAttendanceTemplate(Long scheduleId, String startDate, String endDate);
    
    /**
     * 学生签到
     * 
     * @param attendanceId 考勤ID
     * @param checkInMethod 签到方式
     * @param attendanceStatus 考勤状态
     * @return 结果
     */
    int studentCheckIn(Long attendanceId, String checkInMethod, String attendanceStatus);
    
    /**
     * 管理员确认考勤
     * 
     * @param attendanceId 考勤ID
     * @param confirmedBy 确认人
     * @param finalStatus 最终状态
     * @return 结果
     */
    int confirmAttendance(Long attendanceId, Long confirmedBy, String finalStatus);
    
    /**
     * 批量确认考勤
     * 
     * @param attendanceIds 考勤ID列表
     * @param confirmedBy 确认人
     * @return 确认数量
     */
    int batchConfirmAttendance(List<Long> attendanceIds, Long confirmedBy);
    
    /**
     * 模板记录签到（更新现有记录）
     * 
     * @param attendanceId 考勤ID
     * @param attendanceStatus 考勤状态
     * @param checkInMethod 签到方式
     * @return 结果
     */
    int templateCheckIn(Long attendanceId, String attendanceStatus, String checkInMethod);
}
