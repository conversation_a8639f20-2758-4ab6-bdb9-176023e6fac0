package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.service.IKgClassService;
import com.cl.project.business.service.IDingtalkApiService;
import com.cl.project.business.domain.dto.DingtalkDepartmentRequest;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 班级信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/class")
public class KgClassController extends BaseController
{

    @Autowired
    private IKgClassService kgClassService;
    
    @Autowired
    private IDingtalkApiService dingtalkApiService;
    
    private static final Logger logger = LoggerFactory.getLogger(KgClassController.class);

    @GetMapping("/listByCourseWithSchedule")
    public AjaxResult listByCourseWithSchedule(Long courseId) {
        List<KgClass> classList = kgClassService.selectClassesByCourseWithSchedule(courseId);
        return AjaxResult.success(classList);
    }

    /**
     * 查询班级信息列表
     */
    @SaCheckPermission("kg:student:class:list")
    @GetMapping("/list")
    public TableDataInfo list(KgClass kgClass)
    {
        startPage();
        List<KgClass> list = kgClassService.selectKgClassList(kgClass);
        return getDataTable(list);
    }

    /**
     * 导出班级信息列表
     */
    @SaCheckPermission("kg:student:class:list")
    @Log(title = "班级信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgClass kgClass)
    {
        List<KgClass> list = kgClassService.selectKgClassList(kgClass);
        ExcelUtil<KgClass> util = new ExcelUtil<KgClass>(KgClass.class);
        return util.exportExcel(list, "class");
    }

    /**
     * 获取班级信息详细信息
     */
    @SaCheckPermission("kg:student:class:list")
    @GetMapping(value = "/{classId}")
    public AjaxResult getInfo(@PathVariable("classId") Long classId)
    {
        return AjaxResult.success(kgClassService.selectKgClassById(classId));
    }

    /**
     * 新增班级信息
     */
    @SaCheckPermission("kg:student:class:add")
    @Log(title = "班级信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgClass kgClass)
    {
        try 
        {
            // 先创建本地班级
            int result = kgClassService.insertKgClass(kgClass);
            if (result <= 0) 
            {
                return AjaxResult.error("创建班级失败");
            }
            
            // 再创建钉钉部门
            try 
            {
                DingtalkDepartmentRequest.CreateRequest request = new DingtalkDepartmentRequest.CreateRequest();
                request.setName(kgClass.getClassName());
                request.setParentId(1L); // 上级部门ID = 1
                request.setOrder(kgClass.getClassId().intValue()); // 使用班级ID作为排序
                
                Long deptId = dingtalkApiService.createDepartment(request);
                if (deptId != null) 
                {
                    // 更新本地班级的钉钉部门ID
                    kgClass.setDingtalkDeptId(deptId);
                    kgClassService.updateKgClass(kgClass);
                    logger.info("创建钉钉部门成功: {}, 部门ID: {}", kgClass.getClassName(), deptId);
                } 
                else 
                {
                    logger.warn("创建钉钉部门失败，但本地班级已创建: {}", kgClass.getClassName());
                }
            } 
            catch (Exception e) 
            {
                logger.error("创建钉钉部门异常，但本地班级已创建: {}", kgClass.getClassName(), e);
            }
            
            return AjaxResult.success("创建成功");
        } 
        catch (Exception e) 
        {
            logger.error("创建班级异常", e);
            return AjaxResult.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 修改班级信息
     */
    @SaCheckPermission("kg:student:class:edit")
    @Log(title = "班级信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgClass kgClass)
    {
        try 
        {
            // 获取原始班级信息
            KgClass originalClass = kgClassService.selectKgClassById(kgClass.getClassId());
            if (originalClass == null) 
            {
                return AjaxResult.error("班级不存在");
            }
            
            // 更新本地班级
            int result = kgClassService.updateKgClass(kgClass);
            if (result <= 0) 
            {
                return AjaxResult.error("更新班级失败");
            }
            
            // 更新钉钉部门（如果存在）
            if (originalClass.getDingtalkDeptId() != null) 
            {
                try 
                {
                    DingtalkDepartmentRequest.UpdateRequest request = new DingtalkDepartmentRequest.UpdateRequest();
                    request.setDeptId(originalClass.getDingtalkDeptId());
                    request.setName(kgClass.getClassName());
                    request.setParentId(1L);
                    request.setOrder(kgClass.getClassId().intValue());
                    
                    boolean success = dingtalkApiService.updateDepartment(request);
                    if (success) 
                    {
                        logger.info("更新钉钉部门成功: {}, 部门ID: {}", kgClass.getClassName(), originalClass.getDingtalkDeptId());
                    } 
                    else 
                    {
                        logger.warn("更新钉钉部门失败，但本地班级已更新: {}", kgClass.getClassName());
                    }
                } 
                catch (Exception e) 
                {
                    logger.error("更新钉钉部门异常，但本地班级已更新: {}", kgClass.getClassName(), e);
                }
            } 
            else if (kgClass.getDingtalkDeptId() != null) 
            {
                // 如果原来没有钉钉部门ID，但现在有，则创建新部门
                try 
                {
                    DingtalkDepartmentRequest.CreateRequest request = new DingtalkDepartmentRequest.CreateRequest();
                    request.setName(kgClass.getClassName());
                    request.setParentId(1L);
                    request.setOrder(kgClass.getClassId().intValue());
                    
                    Long deptId = dingtalkApiService.createDepartment(request);
                    if (deptId != null) 
                    {
                        kgClass.setDingtalkDeptId(deptId);
                        kgClassService.updateKgClass(kgClass);
                        logger.info("为班级创建钉钉部门成功: {}, 部门ID: {}", kgClass.getClassName(), deptId);
                    }
                } 
                catch (Exception e) 
                {
                    logger.error("为班级创建钉钉部门异常: {}", kgClass.getClassName(), e);
                }
            }
            
            return AjaxResult.success("更新成功");
        } 
        catch (Exception e) 
        {
            logger.error("更新班级异常", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除班级信息
     */
    @SaCheckPermission("kg:student:class:remove")
    @Log(title = "班级信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{classIds}")
    public AjaxResult remove(@PathVariable Long[] classIds)
    {
        try 
        {
            int successCount = 0;
            for (Long classId : classIds) 
            {
                try 
                {
                    // 获取班级信息
                    KgClass kgClass = kgClassService.selectKgClassById(classId);
                    if (kgClass == null) 
                    {
                        logger.warn("班级不存在: {}", classId);
                        continue;
                    }
                    
                    // 删除钉钉部门（如果存在）
                    if (kgClass.getDingtalkDeptId() != null) 
                    {
                        try 
                        {
                            boolean success = dingtalkApiService.deleteDepartment(kgClass.getDingtalkDeptId());
                            if (success) 
                            {
                                logger.info("删除钉钉部门成功: {}, 部门ID: {}", kgClass.getClassName(), kgClass.getDingtalkDeptId());
                            } 
                            else 
                            {
                                logger.warn("删除钉钉部门失败: {}, 部门ID: {}", kgClass.getClassName(), kgClass.getDingtalkDeptId());
                            }
                        } 
                        catch (Exception e) 
                        {
                            logger.error("删除钉钉部门异常: {}", kgClass.getClassName(), e);
                        }
                    }
                    
                    // 删除本地班级
                    int result = kgClassService.deleteKgClassById(classId);
                    if (result > 0) 
                    {
                        successCount++;
                        logger.info("删除本地班级成功: {}", kgClass.getClassName());
                    } 
                    else 
                    {
                        logger.error("删除本地班级失败: {}", kgClass.getClassName());
                    }
                } 
                catch (Exception e) 
                {
                    logger.error("删除班级异常: {}", classId, e);
                }
            }
            
            if (successCount > 0) 
            {
                return AjaxResult.success("成功删除 " + successCount + " 个班级");
            } 
            else 
            {
                return AjaxResult.error("删除失败");
            }
        } 
        catch (Exception e) 
        {
            logger.error("删除班级异常", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }
    
    // ========================= 钉钉集成相关方法 =========================
    
    /**
     * 同步钉钉部门到本地班级
     */
    @SaCheckPermission("kg:student:class:add")
    @Log(title = "同步钉钉部门", businessType = BusinessType.INSERT)
    @PostMapping("/dingtalk/syncDepartments")
    public AjaxResult syncDingtalkDepartments()
    {
        try 
        {
            int syncCount = dingtalkApiService.syncDepartmentsToClasses(null);
            return AjaxResult.success("同步成功，共同步 " + syncCount + " 个班级");
        } 
        catch (Exception e) 
        {
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有班级列表（用于选择）
     */
    @GetMapping("/listAll")
    public AjaxResult listAll()
    {
        List<KgClass> classList = kgClassService.selectKgClassList(new KgClass());
        return AjaxResult.success(classList);
    }

    /**
     * 同步班级当前人数
     */
    @SaCheckPermission("kg:student:class:edit")
    @Log(title = "同步班级人数", businessType = BusinessType.UPDATE)
    @PostMapping("/syncCurrentCount")
    public AjaxResult syncCurrentCount()
    {
        try
        {
            int updateCount = kgClassService.updateAllClassCurrentCount();
            return AjaxResult.success("同步成功，共更新 " + updateCount + " 个班级的人数");
        }
        catch (Exception e)
        {
            logger.error("同步班级人数异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 同步指定班级当前人数
     */
    @SaCheckPermission("kg:student:class:edit")
    @Log(title = "同步班级人数", businessType = BusinessType.UPDATE)
    @PostMapping("/syncCurrentCount/{classId}")
    public AjaxResult syncCurrentCountByClassId(@PathVariable Long classId)
    {
        try
        {
            int result = kgClassService.updateClassCurrentCount(classId);
            if (result > 0) {
                return AjaxResult.success("同步成功");
            } else {
                return AjaxResult.error("同步失败，班级不存在或无变化");
            }
        }
        catch (Exception e)
        {
            logger.error("同步班级人数异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }
}
