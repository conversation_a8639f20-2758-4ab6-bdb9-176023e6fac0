-- =====================================================
-- 幼儿园管理系统数据库结构设计
-- 版本: 1.0
-- 创建日期: 2024-07-28
-- 说明: 基于现有多租户框架扩展的幼儿园管理系统
-- =====================================================

-- =====================================================
-- 1. 基础信息管理表
-- =====================================================

-- 1.1 幼儿信息表
-- 功能: 存储幼儿的基本信息，包括个人信息、家长信息、入园信息等
-- 关联: 关联班级表(kg_class)、微信用户表(kg_wechat_user)
DROP TABLE IF EXISTS `kg_student`;
CREATE TABLE `kg_student` (
  `student_id` bigint NOT NULL AUTO_INCREMENT COMMENT '幼儿ID',
  `student_code` varchar(50) NOT NULL COMMENT '幼儿编号',
  `student_name` varchar(50) NOT NULL COMMENT '幼儿姓名',
  `gender` char(1) DEFAULT '0' COMMENT '性别（0男 1女）',
  `birth_date` date COMMENT '出生日期',
  `id_card` varchar(18) COMMENT '身份证号',
  `phone` varchar(20) COMMENT '联系电话',
  `parent_name` varchar(50) COMMENT '家长姓名',
  `parent_phone` varchar(20) COMMENT '家长电话',
  `emergency_contact` varchar(50) COMMENT '紧急联系人',
  `emergency_phone` varchar(20) COMMENT '紧急联系电话',
  `address` varchar(200) COMMENT '家庭住址',
  `class_id` bigint COMMENT '班级ID，关联kg_class.class_id',
  `enrollment_date` date COMMENT '入园日期',
  `status` char(1) DEFAULT '0' COMMENT '状态（0在园 1退园 2请假）',
  `face_id` varchar(100) COMMENT '人脸识别ID，用于刷脸打卡',
  `wechat_openid` varchar(100) COMMENT '微信openid，关联kg_wechat_user.openid',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`student_id`),
  UNIQUE KEY `uk_student_code` (`student_code`, `com_id`),
  KEY `idx_class_id` (`class_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_wechat_openid` (`wechat_openid`)
) ENGINE=InnoDB COMMENT='幼儿信息表';

-- 1.2 班级信息表
-- 功能: 存储班级基本信息，包括班级类型、容量、教师配置等
-- 关联: 关联教师表(kg_teacher)作为班主任和副班主任
DROP TABLE IF EXISTS `kg_class`;
CREATE TABLE `kg_class` (
  `class_id` bigint NOT NULL AUTO_INCREMENT COMMENT '班级ID',
  `class_name` varchar(50) NOT NULL COMMENT '班级名称',
  `class_type` varchar(20) COMMENT '班级类型（托班、小班、中班、大班）',
  `capacity` int DEFAULT 30 COMMENT '班级容量',
  `current_count` int DEFAULT 0 COMMENT '当前人数',
  `head_teacher_id` bigint COMMENT '班主任ID，关联kg_teacher.teacher_id',
  `assistant_teacher_id` bigint COMMENT '副班主任ID，关联kg_teacher.teacher_id',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`class_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_head_teacher` (`head_teacher_id`),
  KEY `idx_assistant_teacher` (`assistant_teacher_id`)
) ENGINE=InnoDB COMMENT='班级信息表';

-- 1.3 教师信息表
-- 功能: 存储教师基本信息，包括个人信息、职位信息、工资信息等
-- 关联: 关联系统用户表(sys_user)、微信用户表(kg_wechat_user)
DROP TABLE IF EXISTS `kg_teacher`;
CREATE TABLE `kg_teacher` (
  `teacher_id` bigint NOT NULL AUTO_INCREMENT COMMENT '教师ID',
  `user_id` bigint COMMENT '关联用户ID，关联sys_user.user_id',
  `teacher_code` varchar(50) NOT NULL COMMENT '教师编号',
  `teacher_name` varchar(50) NOT NULL COMMENT '教师姓名',
  `gender` char(1) DEFAULT '0' COMMENT '性别（0男 1女）',
  `phone` varchar(20) COMMENT '联系电话',
  `id_card` varchar(18) COMMENT '身份证号',
  `education` varchar(50) COMMENT '学历',
  `major` varchar(50) COMMENT '专业',
  `hire_date` date COMMENT '入职日期',
  `position` varchar(50) COMMENT '职位',
  `base_salary` decimal(10,2) DEFAULT 0.00 COMMENT '基本工资',
  `face_id` varchar(100) COMMENT '人脸识别ID，用于刷脸打卡',
  `wechat_openid` varchar(100) COMMENT '绑定的微信openid',
  `is_wechat_bound` tinyint DEFAULT 0 COMMENT '是否已绑定微信（0否 1是）',
  `wechat_bind_time` datetime COMMENT '微信绑定时间',
  `status` char(1) DEFAULT '0' COMMENT '状态（0在职 1离职）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`teacher_id`),
  UNIQUE KEY `uk_teacher_code` (`teacher_code`, `com_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_wechat_openid` (`wechat_openid`)
) ENGINE=InnoDB COMMENT='教师信息表';

-- =====================================================
-- 2. 考勤管理表
-- =====================================================

-- 2.1 学生考勤记录表
-- 功能: 记录学生每日考勤情况，支持多种考勤状态和详细信息记录
-- 关联: 关联学生表(kg_student)、班级表(kg_class)、操作员表(kg_teacher)
DROP TABLE IF EXISTS `kg_student_attendance`;
CREATE TABLE `kg_student_attendance` (
  `attendance_id` bigint NOT NULL AUTO_INCREMENT COMMENT '考勤ID',
  `student_id` bigint NOT NULL COMMENT '幼儿ID，关联kg_student.student_id',
  `class_id` bigint NOT NULL COMMENT '班级ID，关联kg_class.class_id',
  `attendance_date` date NOT NULL COMMENT '考勤日期',
  `check_in_time` datetime COMMENT '签到时间',
  `check_out_time` datetime COMMENT '签退时间',
  `attendance_status` varchar(20) DEFAULT 'present' COMMENT '考勤状态（present出勤、absent缺勤、late迟到、early早退、sick病假、personal事假、vacation休假）',
  `absence_reason` varchar(200) COMMENT '缺勤原因',
  `sick_detail` text COMMENT '病假详情（病名、症状、治疗、用药等）',
  `leave_detail` text COMMENT '请假详情（时长、地点等）',
  `check_in_method` varchar(20) DEFAULT 'face' COMMENT '签到方式（face人脸、manual手动）',
  `check_out_method` varchar(20) DEFAULT 'face' COMMENT '签退方式（face人脸、manual手动）',
  `operator_id` bigint COMMENT '操作员ID，关联kg_teacher.teacher_id',
  `is_confirmed` tinyint DEFAULT 0 COMMENT '是否确认（0未确认 1已确认）',
  `confirmed_by` bigint COMMENT '确认人ID，关联kg_teacher.teacher_id',
  `confirmed_time` datetime COMMENT '确认时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`attendance_id`),
  UNIQUE KEY `uk_student_date` (`student_id`, `attendance_date`),
  KEY `idx_class_date` (`class_id`, `attendance_date`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_operator` (`operator_id`)
) ENGINE=InnoDB COMMENT='学生考勤记录表';

-- 2.2 教师考勤记录表
-- 功能: 记录教师每日考勤情况，用于工资计算和管理
-- 关联: 关联教师表(kg_teacher)
DROP TABLE IF EXISTS `kg_teacher_attendance`;
CREATE TABLE `kg_teacher_attendance` (
  `attendance_id` bigint NOT NULL AUTO_INCREMENT COMMENT '考勤ID',
  `teacher_id` bigint NOT NULL COMMENT '教师ID，关联kg_teacher.teacher_id',
  `attendance_date` date NOT NULL COMMENT '考勤日期',
  `check_in_time` datetime COMMENT '签到时间',
  `check_out_time` datetime COMMENT '签退时间',
  `attendance_status` varchar(20) DEFAULT 'present' COMMENT '考勤状态（present出勤、absent缺勤、late迟到、early早退、sick病假、personal事假、vacation休假）',
  `work_hours` decimal(4,2) DEFAULT 0.00 COMMENT '工作时长',
  `check_in_method` varchar(20) DEFAULT 'face' COMMENT '签到方式（face人脸、manual手动）',
  `check_out_method` varchar(20) DEFAULT 'face' COMMENT '签退方式（face人脸、manual手动）',
  `is_confirmed` tinyint DEFAULT 0 COMMENT '是否确认（0未确认 1已确认）',
  `confirmed_by` bigint COMMENT '确认人ID，关联kg_teacher.teacher_id',
  `confirmed_time` datetime COMMENT '确认时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`attendance_id`),
  UNIQUE KEY `uk_teacher_date` (`teacher_id`, `attendance_date`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='教师考勤记录表';

-- =====================================================
-- 3. 托管服务管理表
-- =====================================================

-- 3.1 托管课程表
-- 功能: 定义托管课程的基本信息，包括价格、时长、人数限制等
DROP TABLE IF EXISTS `kg_course`;
CREATE TABLE `kg_course` (
  `course_id` bigint NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `course_name` varchar(50) NOT NULL COMMENT '课程名称',
  `course_type` varchar(20) COMMENT '课程类型（英语、美术、全脑、军警等）',
  `price_per_session` decimal(8,2) DEFAULT 0.00 COMMENT '单节课价格',
  `duration` int DEFAULT 60 COMMENT '课程时长（分钟）',
  `min_students` int DEFAULT 8 COMMENT '最少开班人数',
  `max_students` int DEFAULT 20 COMMENT '最大班级人数',
  `default_teacher_id` bigint DEFAULT NULL COMMENT '默认授课教师ID，关联kg_teacher.teacher_id',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`course_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_course_type` (`course_type`),
  KEY `idx_default_teacher_id` (`default_teacher_id`)
) ENGINE=InnoDB COMMENT='托管课程表';
