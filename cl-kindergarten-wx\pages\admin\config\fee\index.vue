<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">入园费用配置</text>
				</view>
			</view>
		</view>

		<!-- 查询筛选 -->
		<view class="search-section">
			<view class="search-form">
				<view class="form-item">
					<text class="label">班级类型：</text>
					<picker @change="onClassTypeChange" :value="classTypeIndex" :range="classTypeOptions">
						<view class="picker-value">
							{{ classTypeIndex >= 0 ? classTypeOptions[classTypeIndex] : '请选择班级类型' }}
							<u-icon name="arrow-down" size="14" color="#999"></u-icon>
						</view>
					</picker>
				</view>
				<view class="search-buttons">
					<button class="btn-search" @click="handleSearch">查询</button>
					<button class="btn-reset" @click="handleReset">重置</button>
				</view>
			</view>
		</view>

		<!-- 配置列表 -->
		<view class="config-list">
			<view v-if="configList.length === 0" class="empty-state">
				<view class="empty-icon">💰</view>
				<text class="empty-title">暂无费用配置</text>
				<text class="empty-desc">点击右下角+号添加费用配置</text>
			</view>

			<view v-else>
				<view v-for="(item, index) in configList" :key="item.configId" class="config-item">
					<view class="config-header">
						<view class="class-type">{{ item.classType }}</view>
						<view class="status-badge" :class="item.status === '0' ? 'status-active' : 'status-inactive'">
							{{ item.status === '0' ? '正常' : '停用' }}
						</view>
					</view>

					<view class="config-content">
						<view class="fee-row">
							<text class="fee-label">每日餐费：</text>
							<text class="fee-value">¥{{ item.mealFeePerDay }}</text>
						</view>
						<view class="fee-row">
							<text class="fee-label">每月保教费：</text>
							<text class="fee-value">¥{{ item.educationFeePerMonth }}</text>
						</view>
						<view class="fee-row">
							<text class="fee-label">出勤率阈值：</text>
							<text class="fee-value">{{ item.attendanceThreshold ? (item.attendanceThreshold * 100).toFixed(0) + '%' : '未设置' }}</text>
						</view>
						<view class="fee-row">
							<text class="fee-label">半额保教费：</text>
							<text class="fee-value">¥{{ item.halfEducationFee }}</text>
						</view>
						<view class="fee-row">
							<text class="fee-label">生效日期：</text>
							<text class="fee-value">{{ item.effectiveDate }}</text>
						</view>
						<view v-if="item.remark" class="fee-row">
							<text class="fee-label">备注：</text>
							<text class="fee-value remark">{{ item.remark }}</text>
						</view>
					</view>

					<view class="config-actions">
						<button class="btn-edit" @click="handleEdit(item)">编辑</button>
						<button class="btn-toggle" @click="handleToggleStatus(item)">
							{{ item.status === '0' ? '停用' : '启用' }}
						</button>
						<button class="btn-delete" @click="handleDelete(item)">删除</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动新增按钮 -->
		<view class="floating-add-btn" @click="handleAdd">
			<u-icon name="plus" color="#ffffff" size="24"></u-icon>
		</view>
	</view>
</template>

<script>
import {toast} from '@/utils/utils.js'
import {
	listTuitionConfig,
	deleteTuitionConfig,
	changeStatus
} from '@/api/tuitionConfig.js'

export default {
	data() {
		return {
			// 查询参数
			queryParams: {
				classType: ''
			},
			// 配置列表
			configList: [],
			// 班级类型选项
			classTypeOptions: ['全部', '托班', '小班', '中班', '大班'],
			classTypeIndex: 0,
			// 加载状态
			loading: false
		}
	},

	onLoad() {
		this.getList()
	},

	onShow() {
		// 页面显示时刷新列表（从表单页面返回时）
		this.getList()
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 获取配置列表
		async getList() {
			try {
				this.loading = true
				const params = {
					...this.queryParams
				}
				// 如果选择了"全部"，则不传classType参数
				if (this.classTypeIndex === 0) {
					delete params.classType
				}

				const res = await listTuitionConfig(params)
				if (res.code === 200) {
					this.configList = res.rows || []
				} else {
					toast(res.msg || '获取配置列表失败')
				}
			} catch (error) {
				console.error('获取配置列表失败:', error)
				toast('获取配置列表失败')
			} finally {
				this.loading = false
			}
		},

		// 班级类型选择
		onClassTypeChange(e) {
			this.classTypeIndex = e.detail.value
			if (this.classTypeIndex > 0) {
				this.queryParams.classType = this.classTypeOptions[this.classTypeIndex]
			} else {
				this.queryParams.classType = ''
			}
		},

		// 查询
		handleSearch() {
			this.getList()
		},

		// 重置
		handleReset() {
			this.classTypeIndex = 0
			this.queryParams = {
				classType: ''
			}
			this.getList()
		},

		// 新增配置
		handleAdd() {
			uni.navigateTo({
				url: '/pages/admin/config/fee/form?type=add'
			})
		},

		// 编辑配置
		handleEdit(item) {
			uni.navigateTo({
				url: `/pages/admin/config/fee/form?type=edit&configId=${item.configId}`
			})
		},

		// 删除配置
		handleDelete(item) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除"${item.classType}"的费用配置吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await deleteTuitionConfig(item.configId)
							if (result.code === 200) {
								toast('删除成功')
								this.getList()
							} else {
								toast(result.msg || '删除失败')
							}
						} catch (error) {
							console.error('删除失败:', error)
							toast('删除失败')
						}
					}
				}
			})
		},

		// 切换状态
		handleToggleStatus(item) {
			const newStatus = item.status === '0' ? '1' : '0'
			const statusText = newStatus === '0' ? '启用' : '停用'

			uni.showModal({
				title: '确认操作',
				content: `确定要${statusText}"${item.classType}"的费用配置吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await changeStatus(item.configId, newStatus)
							if (result.code === 200) {
								toast(`${statusText}成功`)
								this.getList()
							} else {
								toast(result.msg || `${statusText}失败`)
							}
						} catch (error) {
							console.error(`${statusText}失败:`, error)
							toast(`${statusText}失败`)
						}
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 查询筛选 */
.search-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.search-form {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.form-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.label {
	font-size: 28rpx;
	color: #333333;
	min-width: 140rpx;
}

.picker-value {
	flex: 1;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.search-buttons {
	display: flex;
	gap: 20rpx;
	margin-top: 10rpx;
}

.btn-search, .btn-reset {
	flex: 1;
	height: 70rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
}

.btn-search {
	background: #FF9800;
	color: #ffffff;
}

.btn-reset {
	background: #f8f9fa;
	color: #666666;
}

/* 配置列表 */
.config-list {
	padding: 0 20rpx 40rpx;
}

.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	margin: 20rpx 0;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	display: block;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-desc {
	font-size: 26rpx;
	color: #666666;
	display: block;
	line-height: 1.5;
}

.config-item {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.config-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.class-type {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
}

.status-active {
	background: #e8f5e8;
	color: #52c41a;
}

.status-inactive {
	background: #f5f5f5;
	color: #999999;
}

.config-content {
	margin-bottom: 30rpx;
}

.fee-row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.fee-label {
	font-size: 26rpx;
	color: #666666;
	min-width: 160rpx;
}

.fee-value {
	font-size: 26rpx;
	color: #333333;
	flex: 1;

	&.remark {
		color: #999999;
		font-style: italic;
	}
}

.config-actions {
	display: flex;
	gap: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
}

.btn-edit, .btn-toggle, .btn-delete {
	flex: 1;
	height: 60rpx;
	border-radius: 8rpx;
	font-size: 26rpx;
	border: none;
}

.btn-edit {
	background: #1890ff;
	color: #ffffff;
}

.btn-toggle {
	background: #52c41a;
	color: #ffffff;
}

.btn-delete {
	background: #ff4d4f;
	color: #ffffff;
}

/* 浮动新增按钮 */
.floating-add-btn {
	position: fixed;
	bottom: 120rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.4);
	z-index: 999;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.6);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 50%;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
		pointer-events: none;
	}
}

/* 浮动按钮动画效果 */
@keyframes float {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-8rpx);
	}
}

.floating-add-btn {
	animation: float 3s ease-in-out infinite;
}
</style>
