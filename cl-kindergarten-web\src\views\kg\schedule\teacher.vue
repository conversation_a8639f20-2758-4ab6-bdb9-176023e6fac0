<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>教师排班查看</h2>
      <div class="header-actions">
        <el-button type="success" icon="el-icon-download" @click="handleExport">导出排班表</el-button>
      </div>
    </div>

    <!-- 教师选择 -->
    <el-card class="teacher-select-card">
      <el-form :inline="true">
        <el-form-item label="选择教师：">
          <el-select v-model="selectedTeacherId" placeholder="请选择教师" @change="loadTeacherSchedule">
            <el-option
              v-for="teacher in teacherList"
              :key="teacher.teacherId"
              :label="teacher.teacherName"
              :value="teacher.teacherId">
              <span style="float: left">{{ teacher.teacherName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ teacher.phone }}</span>
            </el-option>
          </el-select>
        </el-form-item>

      </el-form>
    </el-card>

    <!-- 教师信息卡片 -->
    <el-card v-if="selectedTeacher" class="teacher-info-card">
      <div class="teacher-info">
        <div class="teacher-avatar">
          <el-avatar :size="60" :src="selectedTeacher.avatar">
            {{ selectedTeacher.teacherName.charAt(0) }}
          </el-avatar>
        </div>
        <div class="teacher-details">
          <h3>{{ selectedTeacher.teacherName }}</h3>
          <p><i class="el-icon-phone"></i> {{ selectedTeacher.phone }}</p>
          <p><i class="el-icon-message"></i> {{ selectedTeacher && selectedTeacher.email ? selectedTeacher.email : '暂无邮箱' }}</p>
          <p><i class="el-icon-office-building"></i> {{ selectedTeacher && selectedTeacher.department ? selectedTeacher.department : '暂无部门' }}</p>
        </div>
        <div class="teacher-stats">
          <el-statistic title="本周课时" :value="weeklyHours" />
          <el-statistic title="教授班级" :value="teachingClasses" suffix="个" />
          <el-statistic title="负责课程" :value="teachingCourses" suffix="门" />
        </div>
      </div>
    </el-card>

    <!-- 排班日历视图 -->
    <el-card v-if="selectedTeacherId" class="schedule-calendar-card">
      <div slot="header" class="card-header">
        <span>{{ selectedTeacher && selectedTeacher.teacherName ? selectedTeacher.teacherName + '的排班安排' : '教师排班安排' }}</span>
        <div>
          <el-button type="text" @click="prevWeek">
            <i class="el-icon-arrow-left"></i> 上一周
          </el-button>
          <el-button type="text" @click="nextWeek">
            下一周 <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
      </div>

      <div class="calendar-container">
        <table class="teacher-schedule-table">
          <thead>
            <tr>
              <th class="time-header">时间</th>
              <th v-for="day in currentWeekDays" :key="day.date" class="day-header">
                <div class="day-info">
                  <div class="day-name">{{ day.dayName }}</div>
                  <div class="day-date">{{ day.date }}</div>
                  <div class="day-lunar">{{ day.isToday ? '今天' : '' }}</div>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="timeSlot in timeSlots" :key="timeSlot.value">
              <td class="time-cell">
                <div class="time-label">{{ timeSlot.label }}</div>
                <div class="time-period">{{ timeSlot.period }}</div>
              </td>
              <td v-for="day in currentWeekDays" :key="day.date" class="schedule-cell">
                <div
                  v-for="schedule in getTeacherScheduleByDayTime(day.dayOfWeek, timeSlot.value)"
                  :key="schedule.scheduleId"
                  class="teacher-schedule-item"
                  :class="getTeacherScheduleClass(schedule)"
                  @click="viewScheduleDetail(schedule)">

                  <div class="schedule-course">{{ schedule.courseName }}</div>
                  <div class="schedule-class">{{ schedule.className }}</div>
                  <div class="schedule-time">{{ schedule.timeRange }}</div>
                  <div class="schedule-students">
                    <i class="el-icon-user"></i> {{ schedule.studentCount || 0 }}人
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </el-card>

    <!-- 排班统计 -->
    <el-row v-if="selectedTeacherId" :gutter="20" class="schedule-stats">
      <el-col :span="8">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon primary">
              <i class="el-icon-time"></i>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ totalWeeklyHours }}</div>
              <div class="stats-label">周总课时</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon success">
              <i class="el-icon-school"></i>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ totalClasses }}</div>
              <div class="stats-label">教授班级</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon warning">
              <i class="el-icon-reading"></i>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ totalCourses }}</div>
              <div class="stats-label">负责课程</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排班详情列表 -->
    <el-card v-if="selectedTeacherId" class="schedule-list-card">
      <div slot="header">
        <span>排班详情列表</span>
      </div>
      <el-table :data="teacherScheduleList" style="width: 100%">
        <el-table-column prop="dayName" label="星期" width="80" align="center" />
        <el-table-column prop="timeRange" label="时间" width="120" align="center" />
        <el-table-column prop="courseName" label="课程" width="120" />
        <el-table-column prop="className" label="班级" width="100" />
        <el-table-column label="学生人数" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.studentCount || 0 }}人</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="课时长度" width="100" align="center">
          <template slot-scope="scope">
            {{ calculateDuration(scope.row.startTime, scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column label="生效日期" width="110" align="center">
          <template slot-scope="scope">
            {{ scope.row.effectiveDate }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.scheduleType === 'regular'" type="success">常规</el-tag>
            <el-tag v-else-if="scope.row.scheduleType === 'substitute'" type="warning">代课</el-tag>
            <el-tag v-else-if="scope.row.scheduleType === 'makeup'" type="info">补课</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="viewAttendance(scope.row)">查看考勤</el-button>
            <el-button type="text" size="mini" @click="viewStudents(scope.row)">学生名单</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 排班详情对话框 -->
    <el-dialog title="排班详情" :visible.sync="scheduleDetailVisible" width="500px">
      <div v-if="currentScheduleDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课程">{{ currentScheduleDetail.courseName }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ currentScheduleDetail.className }}</el-descriptions-item>
          <el-descriptions-item label="时间">{{ currentScheduleDetail.timeRange }}</el-descriptions-item>
          <el-descriptions-item label="星期">{{ currentScheduleDetail.dayName }}</el-descriptions-item>
          <el-descriptions-item label="学生人数">{{ currentScheduleDetail.studentCount || 0 }}人</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag v-if="currentScheduleDetail.scheduleType === 'regular'" type="success">常规课程</el-tag>
            <el-tag v-else-if="currentScheduleDetail.scheduleType === 'substitute'" type="warning">代课</el-tag>
            <el-tag v-else-if="currentScheduleDetail.scheduleType === 'makeup'" type="info">补课</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="生效日期" :span="2">{{ currentScheduleDetail.effectiveDate }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentScheduleDetail.remark ? currentScheduleDetail.remark : '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    
    <!-- 考勤记录对话框 -->
    <el-dialog title="考勤记录" :visible.sync="attendanceDialogVisible" width="80%" @close="closeAttendanceDialog">
      <div slot="title">
        <span>考勤记录 - {{ currentViewSchedule ? currentViewSchedule.courseName : '' }} ({{ currentViewSchedule ? currentViewSchedule.className : '' }})</span>
      </div>
      
      <!-- 查询条件 -->
      <el-form :model="attendanceQuery" :inline="true" class="mb20">
        <el-form-item label="考勤日期">
          <el-date-picker
            v-model="attendanceQuery.attendanceDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            @change="handleAttendanceDateChange"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadAttendanceData()">查询</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 考勤数据表格 -->
      <el-table :data="attendanceList" border style="width: 100%" max-height="400">
        <el-table-column prop="studentName" label="学生姓名" width="120"/>
        <el-table-column prop="attendanceDate" label="考勤日期" width="120"/>
        <el-table-column label="开始时间" width="100" align="center">
          <template slot-scope="scope">
            {{ formatTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" width="100" align="center">
          <template slot-scope="scope">
            {{ formatTime(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column label="考勤状态" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getAttendanceStatusType(scope.row.attendanceStatus, scope.row.isConfirmed)">
              {{ scope.row.statusDisplay }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="确认时间"   align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.confirmedTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="checkInMethod" label="签到方式" width="100" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.checkInMethod === 'face'" type="success">人脸</el-tag>
            <el-tag v-else-if="scope.row.checkInMethod === 'manual'" type="primary">手动</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="studentPhone" label="联系电话" width="130"/>
        <el-table-column label="确认状态"   align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isConfirmed ? 'success' : 'info'">
              {{ scope.row.isConfirmed ? '已确认' : '待确认' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeAttendanceDialog()">关闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 学生名单对话框 -->
    <el-dialog title="学生名单" :visible.sync="studentsDialogVisible" width="70%" @close="closeStudentsDialog">
      <div slot="title">
        <span>学生名单 - {{ currentViewSchedule ? currentViewSchedule.courseName : '' }} ({{ currentViewSchedule ? currentViewSchedule.className : '' }})</span>
      </div>
      
      <!-- 学生数据表格 -->
      <el-table :data="studentsList" border style="width: 100%" max-height="500">
        <el-table-column type="index" label="序号" width="60" align="center"/>
        <el-table-column prop="studentName" label="学生姓名" width="120"/>
        <el-table-column label="性别" align="center" prop="gender">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.gender"/>
          </template>
        </el-table-column>

        <el-table-column prop="phone" label="学生电话" width="130"/>
        <el-table-column prop="parentName" label="家长姓名" width="120"/>
        <el-table-column prop="parentPhone" label="家长电话" width="130"/>
        <el-table-column prop="enrollmentDate" label="报名日期"  />
        <el-table-column label="课时情况"  >
          <template slot-scope="scope">
            <div>
              <span>总课时：{{ scope.row.totalSessions }}</span><br>
              <span>已用：{{ scope.row.usedSessions }}</span><br>
              <span>剩余：{{ scope.row.remainingSessions }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enrollmentStatus === 'active' ? 'success' : 'danger'">
              {{ scope.row.enrollmentStatus === 'active' ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeStudentsDialog()">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAllTeacher } from "@/api/kg/teacher/info";
import { getScheduleByTeacher, getScheduleStudents, getAttendanceBySchedule, getStudentsBySchedule } from "@/api/kg/schedule";
import { formatDateTime, formatTime } from "@/utils/ruoyi";

export default {
  name: "TeacherSchedule",
  dicts: ['sys_user_sex', 'kg_student_status'],
  data() {
    return {
      // 教师列表
      teacherList: [],
      // 选中的教师ID
      selectedTeacherId: null,
      // 选中的教师信息
      selectedTeacher: null,
      // 教师排班列表
      teacherScheduleList: [],
      // 当前查看周的开始和结束日期
      currentWeekStart: null,
      currentWeekEnd: null,
      // 当前周的日期
      currentWeekDays: [],
      // 排班详情对话框
      scheduleDetailVisible: false,
      // 当前排班详情
      currentScheduleDetail: null,
      // 动态时间段（根据排班数据自动生成）
      timeSlots: [],
      
      // 考勤记录对话框
      attendanceDialogVisible: false,
      // 当前排班的考勤记录
      attendanceList: [],
      // 考勤查询参数
      attendanceQuery: {
        scheduleId: null,
        attendanceDate: ''
      },
      
      // 学生名单对话框
      studentsDialogVisible: false,
      // 当前排班的学生名单
      studentsList: [],
      // 当前查看的排班信息
      currentViewSchedule: null
    };
  },
  created() {
    this.initCurrentWeek();
    this.loadTeacherList();
  },
  computed: {
    // 本周总课时
    weeklyHours() {
      return this.calculateTotalHours(this.teacherScheduleList);
    },
    // 教学班级数
    teachingClasses() {
      const classes = new Set(this.teacherScheduleList.map(s => s.classId));
      return classes.size;
    },
    // 负责课程数
    teachingCourses() {
      const courses = new Set(this.teacherScheduleList.map(s => s.courseId));
      return courses.size;
    },
    // 周总课时
    totalWeeklyHours() {
      return this.calculateTotalHours(this.teacherScheduleList);
    },
    // 总班级数
    totalClasses() {
      const classes = new Set(this.teacherScheduleList.map(s => s.classId));
      return classes.size;
    },
    // 总课程数
    totalCourses() {
      const courses = new Set(this.teacherScheduleList.map(s => s.courseId));
      return courses.size;
    }
  },
  methods: {
    /** 加载教师列表 */
    loadTeacherList() {
      listAllTeacher().then(response => {
        this.teacherList = response.data;
      });
    },
    /** 加载教师排班 */
    loadTeacherSchedule() {
      if (!this.selectedTeacherId) return;

      // 获取教师信息
      this.selectedTeacher = this.teacherList.find(t => t.teacherId === this.selectedTeacherId);

      // 使用原来的API接口，支持日期范围参数
      const params = {
        teacherId: this.selectedTeacherId,
        startDate: this.currentWeekStart,
        endDate: this.currentWeekEnd
      };

      // 使用支持日期范围的API
      import('@/api/kg/schedule').then(module => {
        const { getTeacherSchedule } = module;
        return getTeacherSchedule(params);
      }).then(response => {
        const rawData = response.data || [];
        // 如果是基础数据格式，需要改用视图接口
        if (rawData.length > 0 && !rawData[0].courseName) {
          // 如果没有课程名称，说明是基础数据，改用视图接口
          return import('@/api/kg/schedule').then(module => {
            const { getScheduleByTeacher } = module;
            return getScheduleByTeacher(this.selectedTeacherId);
          }).then(viewResponse => {
            const viewData = viewResponse.data || [];
            // 过滤当前周的数据
            const filteredData = this.filterDataByWeek(viewData);
            this.teacherScheduleList = this.formatScheduleData(filteredData);
            this.generateTimeSlots(filteredData);
          });
        } else {
          this.teacherScheduleList = this.formatScheduleData(rawData);
          this.generateTimeSlots(rawData);
        }
      }).catch(error => {
        console.error('加载教师排班数据失败:', error);
        this.teacherScheduleList = [];
        this.timeSlots = [];
      });
    },
    /** 初始化当前周 */
    initCurrentWeek() {
      const today = new Date();
      this.setWeekByDate(today);
    },
    /** 根据指定日期设置周 */
    setWeekByDate(date) {
      const currentDay = date.getDay() || 7; // 周日为0，转换为7
      const monday = new Date(date);
      monday.setDate(date.getDate() - currentDay + 1);
      
      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      
      // 设置当前周的范围
      this.currentWeekStart = this.formatDate(monday);
      this.currentWeekEnd = this.formatDate(sunday);

      // 生成当前周的日期列表
      this.currentWeekDays = [];
      const today = new Date();
      for (let i = 0; i < 7; i++) {
        const weekDate = new Date(monday);
        weekDate.setDate(monday.getDate() + i);
        this.currentWeekDays.push({
          date: this.formatDate(weekDate),
          dayName: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i],
          dayOfWeek: i + 1,
          isToday: this.isSameDay(weekDate, today)
        });
      }
      
      // 如果已经选了教师，重新加载排班数据
      if (this.selectedTeacherId) {
        this.loadTeacherSchedule();
      }
    },
    /** 上一周 */
    prevWeek() {
      const currentMonday = new Date(this.currentWeekStart);
      currentMonday.setDate(currentMonday.getDate() - 7);
      this.setWeekByDate(currentMonday);
    },
    /** 下一周 */
    nextWeek() {
      const currentMonday = new Date(this.currentWeekStart);
      currentMonday.setDate(currentMonday.getDate() + 7);
      this.setWeekByDate(currentMonday);
    },
    /** 根据星期和时间获取教师排班 */
    getTeacherScheduleByDayTime(dayOfWeek, timeSlot) {
      return this.teacherScheduleList.filter(schedule => {
        // 确保 dayOfWeek 数据类型匹配
        const scheduleDayOfWeek = parseInt(schedule.dayOfWeek || schedule.day_of_week);
        if (scheduleDayOfWeek !== dayOfWeek) return false;
        
        // 获取开始时间，支持多种字段名
        const startTime = schedule.startTime || schedule.start_time;
        if (!startTime) return false;
        
        // 精确匹配时间段（只比较HH:MM）
        const startHourMin = startTime.substring(0, 5);
        return startHourMin === timeSlot;
      });
    },
    /** 获取教师排班样式 */
    getTeacherScheduleClass(schedule) {
      const classes = ['teacher-schedule-item'];
      if (schedule.scheduleType === 'substitute') {
        classes.push('substitute');
      } else if (schedule.scheduleType === 'makeup') {
        classes.push('makeup');
      }
      return classes;
    },
    /** 查看排班详情 */
    viewScheduleDetail(schedule) {
      this.currentScheduleDetail = schedule;
      this.scheduleDetailVisible = true;
    },
    /** 查看考勤 */
    viewAttendance(schedule) {
      this.$router.push({
        path: '/kg/attendance/course',
        query: {
          scheduleId: schedule.scheduleId,
          teacherId: schedule.teacherId,
          courseId: schedule.courseId
        }
      });
    },
    /** 查看学生名单 */
    viewStudents(schedule) {
      getScheduleStudents(schedule.scheduleId).then(response => {
        const students = response.data || [];
        const studentNames = students.map(s => s.studentName).join('、');
        this.$alert(studentNames || '暂无学生', '学生名单', {
          confirmButtonText: '确定'
        });
      });
    },
    /** 计算总课时 */
    calculateTotalHours(schedules) {
      return schedules.reduce((total, schedule) => {
        const duration = this.calculateDuration(schedule.startTime, schedule.endTime);
        return total + (duration / 60);
      }, 0).toFixed(1);
    },
    /** 计算时长 */
    calculateDuration(startTime, endTime) {
      const start = new Date(`2000-01-01 ${startTime}`);
      const end = new Date(`2000-01-01 ${endTime}`);
      return (end - start) / (1000 * 60); // 返回分钟数
    },
    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    /** 判断是否同一天 */
    isSameDay(date1, date2) {
      return date1.toDateString() === date2.toDateString();
    },
    /** 导出排班表 */
    handleExport() {
      if (!this.selectedTeacherId) {
        this.$message.warning('请先选择教师');
        return;
      }
      // 导出逻辑
      this.$message.success('导出功能开发中...');
    },
    /** 格式化排班数据 */
    formatScheduleData(scheduleList) {
      return scheduleList.map(schedule => {
        // 如果是Map格式数据，转换为统一格式
        return {
          scheduleId: schedule.scheduleId || schedule.schedule_id,
          classId: schedule.classId || schedule.class_id,
          courseId: schedule.courseId || schedule.course_id,
          teacherId: schedule.teacherId || schedule.teacher_id,
          dayOfWeek: parseInt(schedule.dayOfWeek || schedule.day_of_week),
          startTime: schedule.startTime || schedule.start_time,
          endTime: schedule.endTime || schedule.end_time,
          effectiveDate: schedule.effectiveDate || schedule.effective_date,
          scheduleType: schedule.scheduleType || schedule.schedule_type || 'regular',
          isActive: schedule.isActive || schedule.is_active,
          // 关联信息字段
          courseName: schedule.courseName || schedule.course_name || '未知课程',
          className: schedule.className || schedule.class_name || '未知班级',
          teacherName: schedule.teacherName || schedule.teacher_name,
          timeRange: schedule.timeRange || schedule.time_range || this.formatTimeRange(schedule.startTime || schedule.start_time, schedule.endTime || schedule.end_time),
          dayName: schedule.dayName || schedule.day_name || this.getDayName(schedule.dayOfWeek || schedule.day_of_week),
          studentCount: schedule.studentCount || schedule.student_count || 0,
          fullDescription: schedule.fullDescription || schedule.full_description,
          remark: schedule.remark
        };
      });
    },
    /** 格式化时间范围 */
    formatTimeRange(startTime, endTime) {
      if (!startTime || !endTime) return '';
      return `${startTime.substring(0, 5)}-${endTime.substring(0, 5)}`;
    },
    /** 获取星期名称 */
    getDayName(dayOfWeek) {
      const dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      return dayNames[parseInt(dayOfWeek)] || '';
    },
    /** 计算总课时 */
    calculateTotalHours(scheduleList) {
      if (!scheduleList || scheduleList.length === 0) return 0;
      
      let totalMinutes = 0;
      
      scheduleList.forEach(schedule => {
        const startTime = schedule.startTime || schedule.start_time;
        const endTime = schedule.endTime || schedule.end_time;
        
        if (startTime && endTime) {
          const duration = this.calculateDurationInMinutes(startTime, endTime);
          totalMinutes += duration;
        }
      });
      
      return this.formatMinutesToDuration(totalMinutes);
    },
    /** 计算时间间隔（智能显示） */
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '0秒';
      
      try {
        // 解析开始时间
        const [startHour, startMin, startSec] = startTime.split(':').map(n => parseInt(n) || 0);
        const startTotalSeconds = startHour * 3600 + startMin * 60 + (startSec || 0);
        
        // 解析结束时间
        const [endHour, endMin, endSec] = endTime.split(':').map(n => parseInt(n) || 0);
        const endTotalSeconds = endHour * 3600 + endMin * 60 + (endSec || 0);
        
        // 计算时间差（秒）
        let durationSeconds = endTotalSeconds - startTotalSeconds;
        
        // 处理跨天情况（如果结束时间小于开始时间）
        if (durationSeconds < 0) {
          durationSeconds += 24 * 3600; // 加上24小时的秒数
        }
        
        return this.formatDuration(Math.max(0, durationSeconds));
        
      } catch (error) {
        console.error('时间计算错误:', { startTime, endTime, error });
        return '0秒';
      }
    },
    
    /** 格式化时间间隔为合适的单位 */
    formatDuration(totalSeconds) {
      if (totalSeconds === 0) return '0秒';
      
      const days = Math.floor(totalSeconds / (24 * 3600));
      const hours = Math.floor((totalSeconds % (24 * 3600)) / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = Math.floor(totalSeconds % 60);
      
      // 智能格式化：只显示有意义的单位，不显示0值
      if (days > 0) {
        // 有天数：显示天 + 小时（如果有）
        return hours > 0 ? `${days}天${hours}小时` : `${days}天`;
      } else if (hours > 0) {
        // 有小时数：显示小时 + 分钟（如果有）
        return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
      } else if (minutes > 0) {
        // 有分钟数：显示分钟 + 秒（如果有）
        return seconds > 0 ? `${minutes}分钟${seconds}秒` : `${minutes}分钟`;
      } else {
        // 只有秒数：只显示秒
        return `${seconds}秒`;
      }
    },
    
    /** 计算时间间隔（分钟数值，兼容旧代码） */
    calculateDurationInMinutes(startTime, endTime) {
      if (!startTime || !endTime) return 0;
      
      try {
        // 解析开始时间
        const [startHour, startMin, startSec] = startTime.split(':').map(n => parseInt(n) || 0);
        const startTotalSeconds = startHour * 3600 + startMin * 60 + (startSec || 0);
        
        // 解析结束时间
        const [endHour, endMin, endSec] = endTime.split(':').map(n => parseInt(n) || 0);
        const endTotalSeconds = endHour * 3600 + endMin * 60 + (endSec || 0);
        
        // 计算时间差（秒）
        let durationSeconds = endTotalSeconds - startTotalSeconds;
        
        // 处理跨天情况
        if (durationSeconds < 0) {
          durationSeconds += 24 * 3600;
        }
        
        // 转换为分钟并四舍五入
        return Math.max(0, Math.round(durationSeconds / 60));
        
      } catch (error) {
        console.error('时间计算错误:', { startTime, endTime, error });
        return 0;
      }
    },
    /** 格式化分钟为时间时长（处理分钟数值） */
    formatMinutesToDuration(minutes) {
      if (minutes === 0) return '0分钟';
      
      const days = Math.floor(minutes / (24 * 60));
      const hours = Math.floor((minutes % (24 * 60)) / 60);
      const remainingMinutes = minutes % 60;
      
      // 智能格式化：不显示0值单位
      if (days > 0) {
        // 有天数：显示天 + 小时（如果有）
        return hours > 0 ? `${days}天${hours}小时` : `${days}天`;
      } else if (hours > 0) {
        // 有小时数：显示小时 + 分钟（如果有）
        return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
      } else {
        // 只有分钟数：只显示分钟
        return `${remainingMinutes}分钟`;
      }
    },
    /** 根据排班数据动态生成时间段 */
    generateTimeSlots(scheduleList) {
      if (!scheduleList || scheduleList.length === 0) {
        this.timeSlots = [];
        return;
      }
      
      // 提取所有开始时间
      const startTimes = [];
      scheduleList.forEach(schedule => {
        const startTime = schedule.startTime || schedule.start_time;
        if (startTime) {
          const timeStr = startTime.substring(0, 5); // 只取HH:MM部分
          if (!startTimes.includes(timeStr)) {
            startTimes.push(timeStr);
          }
        }
      });
      
      // 排序时间
      startTimes.sort();
      
      // 生成时间段列表
      this.timeSlots = startTimes.map(timeStr => {
        const hour = parseInt(timeStr.split(':')[0]);
        let period = '上午';
        if (hour >= 12 && hour < 18) {
          period = '下午';
        } else if (hour >= 18) {
          period = '晚上';
        }
        
        return {
          value: timeStr,
          label: timeStr,
          period: period
        };
      });
    },
    /** 按当前周过滤排班数据 */
    filterDataByWeek(scheduleList) {
      if (!scheduleList || scheduleList.length === 0) return [];
      
      const startDate = new Date(this.currentWeekStart);
      const endDate = new Date(this.currentWeekEnd);
      
      return scheduleList.filter(schedule => {
        // 检查有效日期
        const effectiveDate = schedule.effectiveDate || schedule.effective_date;
        if (!effectiveDate) return true; // 如果没有有效日期，则不过滤
        
        const scheduleDate = new Date(effectiveDate);
        
        // 检查排班日期是否在当前周范围内
        return scheduleDate >= startDate && scheduleDate <= endDate;
      });
    },
    
    /** 查看考勤记录 */
    viewAttendance(schedule) {
      this.currentViewSchedule = schedule;
      this.attendanceQuery.scheduleId = schedule.scheduleId;
      this.attendanceQuery.attendanceDate = '';
      this.loadAttendanceData();
      this.attendanceDialogVisible = true;
    },
    
    /** 查看学生名单 */
    viewStudents(schedule) {
      this.currentViewSchedule = schedule;
      this.loadStudentsData(schedule.scheduleId);
      this.studentsDialogVisible = true;
    },
    
    /** 加载考勤数据 */
    loadAttendanceData() {
      if (!this.attendanceQuery.scheduleId) return;
      
      getAttendanceBySchedule(
        this.attendanceQuery.scheduleId,
        this.attendanceQuery.attendanceDate
      ).then(response => {
        this.attendanceList = response.data;
      }).catch(error => {
        console.error('加载考勤数据失败:', error);
        this.$message.error('加载考勤数据失败');
      });
    },
    
    /** 加载学生名单数据 */
    loadStudentsData(scheduleId) {
      if (!scheduleId) return;
      
      getStudentsBySchedule(scheduleId).then(response => {
        this.studentsList = response.data;
      }).catch(error => {
        console.error('加载学生名单失败:', error);
        this.$message.error('加载学生名单失败');
      });
    },
    
    /** 考勤日期变化 */
    handleAttendanceDateChange() {
      this.loadAttendanceData();
    },
    
    /** 关闭考勤对话框 */
    closeAttendanceDialog() {
      this.attendanceDialogVisible = false;
      this.attendanceList = [];
      this.currentViewSchedule = null;
    },
    
    /** 关闭学生名单对话框 */
    closeStudentsDialog() {
      this.studentsDialogVisible = false;
      this.studentsList = [];
      this.currentViewSchedule = null;
    },
    
    /** 获取考勤状态显示类型 */
    getAttendanceStatusType(status, isConfirmed) {
      if (status === 'present' && isConfirmed === 1) {
        return 'success'; // 已签到 - 绿色
      } else if (status === 'present' && isConfirmed === 0) {
        return 'warning'; // 待确认 - 黄色
      } else if (status === 'absent') {
        return 'danger'; // 缺勤 - 红色
      } else if (status === 'late') {
        return 'warning'; // 迟到 - 黄色
      } else if (status === 'early') {
        return 'info'; // 早退 - 蓝色
      }
      return 'info'; // 默认状态
    },
    
    // 引用公共时间格式化函数
    formatDateTime,
    formatTime
  }
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.teacher-select-card {
  margin-bottom: 20px;
}

.teacher-info-card {
  margin-bottom: 20px;
}

.teacher-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.teacher-details h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.teacher-details p {
  margin: 5px 0;
  color: #606266;
}

.teacher-stats {
  margin-left: auto;
  display: flex;
  gap: 40px;
}

.schedule-calendar-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-container {
  overflow-x: auto;
}

.teacher-schedule-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  border: 1px solid #ebeef5;
}

.teacher-schedule-table th,
.teacher-schedule-table td {
  border: 1px solid #ebeef5;
  padding: 8px;
  text-align: center;
  vertical-align: top;
}

.time-header,
.day-header {
  background-color: #f5f7fa;
  font-weight: bold;
}

.day-info {
  text-align: center;
}

.day-name {
  font-size: 14px;
  font-weight: bold;
}

.day-date {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.day-lunar {
  font-size: 10px;
  color: #67c23a;
  margin-top: 2px;
}

.time-cell {
  background-color: #fafafa;
  width: 80px;
}

.time-label {
  font-weight: bold;
  font-size: 14px;
}

.time-period {
  font-size: 12px;
  color: #909399;
}

.schedule-cell {
  height: 100px;
  width: 140px;
  position: relative;
}

.teacher-schedule-item {
  margin: 2px;
  padding: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  line-height: 1.3;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.teacher-schedule-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.teacher-schedule-item.substitute {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.teacher-schedule-item.makeup {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c5aa0;
}

.schedule-course {
  font-weight: bold;
  margin-bottom: 2px;
}

.schedule-class {
  font-size: 11px;
  opacity: 0.9;
}

.schedule-time {
  font-size: 10px;
  opacity: 0.8;
  margin-top: 2px;
}

.schedule-students {
  font-size: 10px;
  opacity: 0.8;
  margin-top: 2px;
}

.schedule-stats {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stats-icon.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.success {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.schedule-list-card {
  margin-bottom: 20px;
}
</style>
