<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">赠送规则</text>
				</view>
			</view>
		</view>

		<!-- 搜索筛选栏 -->
		<view class="search-filter-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input
					class="search-input"
					placeholder="搜索规则名称"
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>

			<!-- 筛选按钮 -->
			<view class="filter-actions">
				<view class="filter-chip" @click="showFilterPopup = true">
					<text>筛选</text>
				</view>
			</view>
		</view>

		<!-- 规则列表 -->
		<view v-if="giftRuleList.length === 0 && !loading" class="empty-container">
			<view class="empty-illustration">
				<view class="empty-emoji">🎁</view>
			</view>
			<text class="empty-title">暂无赠送规则</text>
			<text class="empty-subtitle">点击右下角+号添加新规则</text>
		</view>

		<view v-if="loading && giftRuleList.length === 0" class="loading-container">
			<u-loading-icon mode="spinner" color="#9C27B0" size="40"></u-loading-icon>
			<text class="loading-text">加载中...</text>
		</view>

		<view v-if="giftRuleList.length > 0" class="content">
			<text class="list-header">赠送规则 ({{ giftRuleList.length }} 条)</text>

			<view
				v-for="item in giftRuleList"
				:key="item.ruleId"
				class="gift-rule-card"
				@click="handleDetail(item)"
			>
				<!-- 卡片头部 -->
				<view class="card-header">
					<view class="left-section">
						<view class="rule-icon">
							<text class="icon-text">{{ getRuleTypeIcon(item.ruleType) }}</text>
						</view>
						<view class="info-section">
							<text class="primary-text">{{ item.ruleName || '未命名规则' }}</text>
							<text class="secondary-text">{{ getRuleTypeText(item.ruleType) }}</text>
						</view>
					</view>
					<view :class="'status-badge ' + getStatusClass(item.status)">
						<text class="badge-text">{{ getStatusText(item.status) }}</text>
					</view>
				</view>

				<!-- 规则详情 -->
				<view class="card-body">
					<view class="rule-details">
						<view v-if="item.description" class="detail-row">
							<text class="detail-label">规则描述：</text>
							<text class="detail-value">{{ item.description }}</text>
						</view>

						<!-- 触发条件 -->
						<view class="detail-row">
							<text class="detail-label">触发条件：</text>
							<text class="detail-value">{{ getTriggerCondition(item) }}</text>
						</view>

						<!-- 赠送内容 -->
						<view class="detail-row">
							<text class="detail-label">赠送内容：</text>
							<text class="detail-value">{{ item.giftCourseName || '未知课程' }} {{ item.giftSessions || 0 }}节</text>
						</view>

						<!-- 有效期 -->
						<view class="detail-row">
							<text class="detail-label">有效期：</text>
							<text class="detail-value">{{ getValidPeriod(item) }}</text>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="card-actions">
					<button class="action-btn secondary" @click.stop="handleEdit(item)">
						<text class="btn-text">编辑</text>
					</button>
					<button class="action-btn" :class="item.status === '1' ? 'danger' : 'primary'" @click.stop="handleStatusChange(item)">
						<text class="btn-text">{{ item.status === '1' ? '禁用' : '启用' }}</text>
					</button>
					<button class="action-btn danger" @click.stop="handleDelete(item)">
						<text class="btn-text">删除</text>
					</button>
				</view>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMore" class="load-more" @click="loadMore">
				<u-loading-icon v-if="loading" mode="spinner" color="#9C27B0" size="24"></u-loading-icon>
				<text class="load-more-text">{{ loading ? '加载中...' : '点击加载更多' }}</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-else-if="giftRuleList.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了</text>
			</view>
		</view>

		<!-- 浮动新增按钮 -->
		<view class="floating-add-btn" @click="showAddDialog">
			<u-icon name="plus" color="#ffffff" size="24"></u-icon>
		</view>

		<!-- 筛选弹窗 -->
		<u-popup v-model="showFilterPopup" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="popup-title">筛选条件</text>
					<view class="popup-close" @click="showFilterPopup = false">
						<u-icon name="close" size="20" color="#666"></u-icon>
					</view>
				</view>

				<view class="filter-content">
					<view class="filter-group">
						<text class="filter-label">规则类型</text>
						<view class="filter-options">
							<view
								:class="['filter-option radio-option', { active: filterForm.ruleType === '' }]"
								@click="selectRuleType('')"
							>
								<view class="radio-icon">
									<view v-if="filterForm.ruleType === ''" class="radio-dot"></view>
								</view>
								<text class="option-text">全部</text>
							</view>
							<view
								v-for="type in ruleTypeOptions"
								:key="type.value"
								:class="['filter-option radio-option', { active: filterForm.ruleType === type.value }]"
								@click="selectRuleType(type.value)"
							>
								<view class="radio-icon">
									<view v-if="filterForm.ruleType === type.value" class="radio-dot"></view>
								</view>
								<text class="option-text">{{ type.label }}</text>
							</view>
						</view>
					</view>

					<view class="filter-group">
						<text class="filter-label">状态</text>
						<view class="filter-options">
							<view
								v-for="status in statusOptions"
								:key="status.value"
								:class="['filter-option', { active: filterForm.status === status.value }]"
								@click="selectStatus(status.value)"
							>
								<text class="option-text">{{ status.label }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="filter-actions">
					<button class="filter-btn reset" @click="resetFilter">重置</button>
					<button class="filter-btn confirm" @click="applyFilter">确定</button>
				</view>
			</view>
		</u-popup>

		<!-- 新增/编辑规则弹窗 -->
		<u-popup v-model="showFormDialog" mode="center" width="90%" border-radius="20">
			<view class="form-dialog">
				<view class="dialog-header">
					<text class="dialog-title">{{ isEdit ? '编辑规则' : '新增规则' }}</text>
					<view class="dialog-close" @click="closeFormDialog">
						<u-icon name="close" size="20" color="#666"></u-icon>
					</view>
				</view>

				<view class="dialog-content">
					<view class="form-group">
						<text class="form-label">规则名称 <text class="required">*</text></text>
						<u-input
							v-model="formData.ruleName"
							placeholder="请输入规则名称"
							:border="true"
							:clearable="true"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">规则类型 <text class="required">*</text></text>
						<view class="radio-group">
							<view
								v-for="type in ruleTypeOptions"
								:key="type.value"
								:class="['radio-item', { active: formData.ruleType === type.value }]"
								@click="selectFormRuleType(type.value)"
							>
								<view class="radio-circle">
									<view v-if="formData.ruleType === type.value" class="radio-dot"></view>
								</view>
								<text class="radio-label">{{ type.label }}</text>
							</view>
						</view>
					</view>

					<view class="form-group">
						<text class="form-label">规则描述</text>
						<u-input
							v-model="formData.description"
							placeholder="请输入规则描述"
							type="textarea"
							:border="true"
							:clearable="true"
							height="80"
						/>
					</view>

					<!-- 触发条件 -->
					<view v-if="formData.ruleType === 'amount_based'" class="form-group">
						<text class="form-label">触发金额 <text class="required">*</text></text>
						<u-input
							v-model="formData.triggerAmount"
							placeholder="请输入触发金额"
							type="number"
							:border="true"
							:clearable="true"
						/>
					</view>

					<view v-if="formData.ruleType === 'attendance_based'" class="form-group">
						<text class="form-label">触发出勤率 <text class="required">*</text></text>
						<u-input
							v-model="formData.triggerAttendanceRate"
							placeholder="请输入出勤率(0-100)"
							type="number"
							:border="true"
							:clearable="true"
						/>
					</view>

					<!-- 赠送内容 -->
					<view class="form-group">
						<text class="form-label">赠送课程 <text class="required">*</text></text>
						<view class="course-select-container">
							<view class="course-select-header" @click="toggleCourseDropdown">
								<text class="course-select-text">{{ formData.giftCourseName || '请选择赠送课程' }}</text>
								<u-icon name="arrow-down" size="16" color="#999" :class="{ 'rotate': showCourseDropdown }"></u-icon>
							</view>
							<view v-if="showCourseDropdown" class="course-dropdown">
								<view class="course-search">
									<input
										class="course-search-input"
										placeholder="搜索课程名称"
										v-model="courseSearchKeyword"
										@input="filterCourses"
									/>
								</view>
								<scroll-view class="course-list" scroll-y="true">
									<view
										v-for="course in filteredCourseOptions"
										:key="course.courseId"
										class="course-item"
										:class="{ active: formData.giftCourseId === course.courseId }"
										@click="selectCourse(course)"
									>
										<view class="course-info">
											<text class="course-name">{{ course.courseName }}</text>
											<text class="course-desc">{{ course.description || '暂无描述' }}</text>
										</view>
										<view v-if="formData.giftCourseId === course.courseId" class="course-selected">
											<u-icon name="checkmark" size="20" color="#9C27B0"></u-icon>
										</view>
									</view>
								</scroll-view>
							</view>
						</view>
					</view>

					<view class="form-group">
						<text class="form-label">赠送课时数 <text class="required">*</text></text>
						<u-input
							v-model="formData.giftSessions"
							placeholder="请输入赠送课时数"
							type="number"
							:border="true"
							:clearable="true"
						/>
					</view>

					<!-- 有效期设置 -->
					<view class="form-group">
						<text class="form-label">生效日期</text>
						<view class="picker-input" @click="showEffectiveDatePicker = true">
							<text class="picker-text">{{ formData.effectiveDate || '请选择生效日期' }}</text>
							<u-icon name="arrow-down" size="16" color="#999"></u-icon>
						</view>
					</view>

					<view class="form-group">
						<text class="form-label">失效日期</text>
						<view class="picker-input" @click="showExpiryDatePicker = true">
							<text class="picker-text">{{ formData.expiryDate || '请选择失效日期' }}</text>
							<u-icon name="arrow-down" size="16" color="#999"></u-icon>
						</view>
					</view>

					<!-- 限制条件 -->
					<view class="form-group">
						<text class="form-label">每人最大享受次数</text>
						<u-input
							v-model="formData.maxTimesPerStudent"
							placeholder="不限制请留空"
							type="number"
							:border="true"
							:clearable="true"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">每人每月最大次数</text>
						<u-input
							v-model="formData.maxTimesPerMonth"
							placeholder="不限制请留空"
							type="number"
							:border="true"
							:clearable="true"
						/>
					</view>
				</view>

				<view class="dialog-actions">
					<button class="dialog-btn cancel" @click="closeFormDialog">取消</button>
					<button class="dialog-btn confirm" @click="handleSubmit">{{ isEdit ? '更新' : '创建' }}</button>
				</view>

				<!-- 生效日期选择器 -->
				<view v-if="showEffectiveDatePicker" class="date-picker-overlay" @click="onEffectiveDateCancel">
					<view class="date-picker-container" @click.stop="">
						<view class="date-picker-header">
							<text class="picker-cancel" @click="onEffectiveDateCancel">取消</text>
							<text class="picker-title">选择生效日期</text>
							<text class="picker-confirm" @click="confirmEffectiveDate">确定</text>
						</view>
						<picker-view class="date-picker-view" :value="effectiveDatePickerValue" @change="onEffectiveDateChange">
							<picker-view-column>
								<view v-for="year in yearOptions" :key="year" class="picker-item">
									{{ year }}年
								</view>
							</picker-view-column>
							<picker-view-column>
								<view v-for="month in monthOptions" :key="month" class="picker-item">
									{{ month }}月
								</view>
							</picker-view-column>
							<picker-view-column>
								<view v-for="day in dayOptions" :key="day" class="picker-item">
									{{ day }}日
								</view>
							</picker-view-column>
						</picker-view>
					</view>
				</view>

				<!-- 失效日期选择器 -->
				<view v-if="showExpiryDatePicker" class="date-picker-overlay" @click="onExpiryDateCancel">
					<view class="date-picker-container" @click.stop="">
						<view class="date-picker-header">
							<text class="picker-cancel" @click="onExpiryDateCancel">取消</text>
							<text class="picker-title">选择失效日期</text>
							<text class="picker-confirm" @click="confirmExpiryDate">确定</text>
						</view>
						<picker-view class="date-picker-view" :value="expiryDatePickerValue" @change="onExpiryDateChange">
							<picker-view-column>
								<view v-for="year in yearOptions" :key="year" class="picker-item">
									{{ year }}年
								</view>
							</picker-view-column>
							<picker-view-column>
								<view v-for="month in monthOptions" :key="month" class="picker-item">
									{{ month }}月
								</view>
							</picker-view-column>
							<picker-view-column>
								<view v-for="day in dayOptions" :key="day" class="picker-item">
									{{ day }}日
								</view>
							</picker-view-column>
						</picker-view>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import {
	getGiftRuleList,
	getGiftRuleDetail,
	addGiftRule,
	updateGiftRule,
	deleteGiftRule,
	changeGiftRuleStatus,
	getCourseList
} from '@/api/api.js'
export default {

	data() {
		return {
			// 列表数据
			giftRuleList: [],
			loading: false,
			hasMore: true,

			// 搜索筛选
			searchKeyword: '',
			showFilterPopup: false,
			filterForm: {
				ruleType: '',
				status: ''
			},

			// 表单弹窗
			showFormDialog: false,
			isEdit: false,
			formData: {
				ruleId: null,
				ruleName: '',
				ruleType: '',
				description: '',
				triggerAmount: '',
				triggerAttendanceRate: '',
				giftCourseId: null,
				giftCourseName: '',
				giftSessions: '',
				effectiveDate: '',
				expiryDate: '',
				maxTimesPerStudent: '',
				maxTimesPerMonth: '',
				status: '1'
			},

			// 选择器
			showRuleTypePicker: false,
			showCoursePicker: false,
			showCourseDropdown: false,
			showEffectiveDatePicker: false,
			showExpiryDatePicker: false,

			// 日期选择器的值
			effectiveDatePickerValue: [0, 0, 0],
			expiryDatePickerValue: [0, 0, 0],

			// 日期选择器选项
			yearOptions: [],
			monthOptions: [],
			dayOptions: [],

			// 临时日期值
			tempEffectiveDate: [0, 0, 0],
			tempExpiryDate: [0, 0, 0],

			// 选项数据
			ruleTypeOptions: [
				{ label: '金额型', value: 'amount_based' },
				{ label: '出勤型', value: 'attendance_based' }
			],
			statusOptions: [
				{ label: '全部', value: '' },
				{ label: '启用', value: '1' },
				{ label: '禁用', value: '0' }
			],
			courseOptions: [],
			filteredCourseOptions: [],
			courseSearchKeyword: '',

			// 分页
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				ruleName: null,
				ruleType: null,
				status: null
			}
		}
	},

	onLoad() {
		this.loadGiftRuleList()
		this.loadCourseOptions()
		this.initDatePicker()
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 加载赠送规则列表
		async loadGiftRuleList(refresh = false) {
			if (this.loading) return

			this.loading = true

			if (refresh) {
				this.queryParams.pageNum = 1
				this.giftRuleList = []
				this.hasMore = true
			}

			try {
				// 过滤掉null值的参数，只传递有值的参数
				const params = this.filterParams(this.queryParams)

				const response = await getGiftRuleList(params)

				if (response.code === 200) {
					const newList = response.rows || []

					if (refresh) {
						this.giftRuleList = newList
					} else {
						this.giftRuleList = [...this.giftRuleList, ...newList]
					}

					// 判断是否还有更多数据
					this.hasMore = newList.length >= this.queryParams.pageSize
				} else {
					toast(response.msg || '加载失败')
				}
			} catch (error) {
				console.error('加载赠送规则列表失败:', error)
				toast('加载失败，请重试')
			} finally {
				this.loading = false
			}
		},

		// 加载课程选项
		async loadCourseOptions() {
			try {
				const response = await getCourseList({ pageNum: 1, pageSize: 1000 })
				if (response.code === 200) {
					this.courseOptions = response.rows || []
					this.filteredCourseOptions = [...this.courseOptions]
				}
			} catch (error) {
				console.error('加载课程列表失败:', error)
			}
		},

		// 初始化日期选择器
		initDatePicker() {
			const currentYear = new Date().getFullYear()

			// 生成年份选项（当前年份前后10年）
			this.yearOptions = []
			for (let i = currentYear - 10; i <= currentYear + 10; i++) {
				this.yearOptions.push(i)
			}

			// 生成月份选项
			this.monthOptions = []
			for (let i = 1; i <= 12; i++) {
				this.monthOptions.push(i)
			}

			// 初始化日期选择器值为当前日期
			const now = new Date()
			const yearIndex = this.yearOptions.indexOf(now.getFullYear())
			const monthIndex = now.getMonth()
			const dayIndex = now.getDate() - 1

			this.effectiveDatePickerValue = [yearIndex, monthIndex, dayIndex]
			this.expiryDatePickerValue = [yearIndex, monthIndex, dayIndex]

			// 更新日期选项
			this.updateDayOptions(now.getFullYear(), now.getMonth() + 1)
		},

		// 更新日期选项
		updateDayOptions(year, month) {
			const daysInMonth = new Date(year, month, 0).getDate()
			this.dayOptions = []
			for (let i = 1; i <= daysInMonth; i++) {
				this.dayOptions.push(i)
			}
		},

		// 搜索输入
		onSearchInput() {
			// 防抖处理
			clearTimeout(this.searchTimer)
			this.searchTimer = setTimeout(() => {
				this.handleSearch()
			}, 500)
		},

		// 执行搜索
		handleSearch() {
			this.queryParams.ruleName = this.searchKeyword.trim() || null
			this.loadGiftRuleList(true)
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
			this.queryParams.ruleName = null
			this.loadGiftRuleList(true)
		},

		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return
			this.queryParams.pageNum++
			this.loadGiftRuleList()
		},

		// 筛选相关方法
		selectRuleType(value) {
			// 单选逻辑：直接设置选中的值
			this.filterForm.ruleType = value
		},

		selectStatus(value) {
			this.filterForm.status = this.filterForm.status === value ? '' : value
		},

		resetFilter() {
			this.filterForm = {
				ruleType: '',
				status: ''
			}
		},

		applyFilter() {
			this.queryParams.ruleType = this.filterForm.ruleType || null
			this.queryParams.status = this.filterForm.status || null
			this.showFilterPopup = false
			this.loadGiftRuleList(true)
		},

		// 表单相关方法
		showAddDialog() {
			this.isEdit = false
			this.resetFormData()
			this.showFormDialog = true
		},

		closeFormDialog() {
			this.showFormDialog = false
			this.showCourseDropdown = false
			this.resetFormData()
		},

		resetFormData() {
			this.formData = {
				ruleId: null,
				ruleName: '',
				ruleType: '',
				description: '',
				triggerAmount: '',
				triggerAttendanceRate: '',
				giftCourseId: null,
				giftCourseName: '',
				giftSessions: '',
				effectiveDate: '',
				expiryDate: '',
				maxTimesPerStudent: '',
				maxTimesPerMonth: '',
				status: '1'
			}
			// 重置日期选择器的值
			const now = new Date()
			const yearIndex = this.yearOptions.indexOf(now.getFullYear())
			const monthIndex = now.getMonth()
			const dayIndex = now.getDate() - 1

			this.effectiveDatePickerValue = [yearIndex, monthIndex, dayIndex]
			this.expiryDatePickerValue = [yearIndex, monthIndex, dayIndex]
			this.tempEffectiveDate = [yearIndex, monthIndex, dayIndex]
			this.tempExpiryDate = [yearIndex, monthIndex, dayIndex]

			// 重置课程下拉框
			this.showCourseDropdown = false
			this.courseSearchKeyword = ''
			this.filteredCourseOptions = [...this.courseOptions]
		},

		// 表单中规则类型选择
		selectFormRuleType(value) {
			this.formData.ruleType = value
		},

		// 选择器确认方法
		onRuleTypeConfirm(e) {
			const selected = this.ruleTypeOptions[e.index]
			this.formData.ruleType = selected.value
			this.showRuleTypePicker = false
		},

		// 课程下拉框相关方法
		toggleCourseDropdown() {
			this.showCourseDropdown = !this.showCourseDropdown
			if (this.showCourseDropdown) {
				this.courseSearchKeyword = ''
				this.filteredCourseOptions = [...this.courseOptions]
			}
		},

		selectCourse(course) {
			this.formData.giftCourseId = course.courseId
			this.formData.giftCourseName = course.courseName
			this.showCourseDropdown = false
			this.courseSearchKeyword = ''
		},

		filterCourses() {
			if (!this.courseSearchKeyword.trim()) {
				this.filteredCourseOptions = [...this.courseOptions]
			} else {
				const keyword = this.courseSearchKeyword.toLowerCase()
				this.filteredCourseOptions = this.courseOptions.filter(course =>
					course.courseName.toLowerCase().includes(keyword) ||
					(course.description && course.description.toLowerCase().includes(keyword))
				)
			}
		},

		onCourseConfirm(e) {
			const selected = this.courseOptions[e.index]
			this.formData.giftCourseId = selected.courseId
			this.formData.giftCourseName = selected.courseName
			this.showCoursePicker = false
		},

		// 生效日期选择器事件
		onEffectiveDateChange(e) {
			this.tempEffectiveDate = e.detail.value

			// 当年份或月份变化时，更新日期选项
			const year = this.yearOptions[this.tempEffectiveDate[0]]
			const month = this.monthOptions[this.tempEffectiveDate[1]]
			this.updateDayOptions(year, month)

			// 如果当前选中的日期超出了新月份的天数，调整到最后一天
			if (this.tempEffectiveDate[2] >= this.dayOptions.length) {
				this.tempEffectiveDate[2] = this.dayOptions.length - 1
			}
		},

		confirmEffectiveDate() {
			const year = this.yearOptions[this.tempEffectiveDate[0]]
			const month = this.monthOptions[this.tempEffectiveDate[1]]
			const day = this.dayOptions[this.tempEffectiveDate[2]]

			this.formData.effectiveDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
			this.effectiveDatePickerValue = [...this.tempEffectiveDate]
			this.showEffectiveDatePicker = false
		},

		onEffectiveDateCancel() {
			this.tempEffectiveDate = [...this.effectiveDatePickerValue]
			this.showEffectiveDatePicker = false
		},

		// 失效日期选择器事件
		onExpiryDateChange(e) {
			this.tempExpiryDate = e.detail.value

			// 当年份或月份变化时，更新日期选项
			const year = this.yearOptions[this.tempExpiryDate[0]]
			const month = this.monthOptions[this.tempExpiryDate[1]]
			this.updateDayOptions(year, month)

			// 如果当前选中的日期超出了新月份的天数，调整到最后一天
			if (this.tempExpiryDate[2] >= this.dayOptions.length) {
				this.tempExpiryDate[2] = this.dayOptions.length - 1
			}
		},

		confirmExpiryDate() {
			const year = this.yearOptions[this.tempExpiryDate[0]]
			const month = this.monthOptions[this.tempExpiryDate[1]]
			const day = this.dayOptions[this.tempExpiryDate[2]]

			this.formData.expiryDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
			this.expiryDatePickerValue = [...this.tempExpiryDate]
			this.showExpiryDatePicker = false
		},

		onExpiryDateCancel() {
			this.tempExpiryDate = [...this.expiryDatePickerValue]
			this.showExpiryDatePicker = false
		},

		// 表单提交
		async handleSubmit() {
			// 表单验证
			if (!this.formData.ruleName) {
				toast('请输入规则名称')
				return
			}

			if (!this.formData.ruleType) {
				toast('请选择规则类型')
				return
			}

			if (this.formData.ruleType === 'amount_based' && !this.formData.triggerAmount) {
				toast('请输入触发金额')
				return
			}

			if (this.formData.ruleType === 'attendance_based' && !this.formData.triggerAttendanceRate) {
				toast('请输入触发出勤率')
				return
			}

			if (!this.formData.giftCourseId) {
				toast('请选择赠送课程')
				return
			}

			if (!this.formData.giftSessions) {
				toast('请输入赠送课时数')
				return
			}

			try {
				const submitData = { ...this.formData }

				// 数据类型转换
				if (submitData.triggerAmount) {
					submitData.triggerAmount = parseFloat(submitData.triggerAmount)
				}
				if (submitData.triggerAttendanceRate) {
					submitData.triggerAttendanceRate = parseFloat(submitData.triggerAttendanceRate)
				}
				if (submitData.giftSessions) {
					submitData.giftSessions = parseInt(submitData.giftSessions)
				}
				if (submitData.maxTimesPerStudent) {
					submitData.maxTimesPerStudent = parseInt(submitData.maxTimesPerStudent)
				}
				if (submitData.maxTimesPerMonth) {
					submitData.maxTimesPerMonth = parseInt(submitData.maxTimesPerMonth)
				}

				let response
				if (this.isEdit) {
					response = await updateGiftRule(submitData)
				} else {
					response = await addGiftRule(submitData)
				}

				if (response.code === 200) {
					toast(this.isEdit ? '更新成功' : '创建成功')
					this.closeFormDialog()
					this.loadGiftRuleList(true)
				} else {
					toast(response.msg || '操作失败')
				}
			} catch (error) {
				console.error('提交失败:', error)
				toast('操作失败，请重试')
			}
		},

		// 列表操作方法
		handleDetail(item) {
			// 可以跳转到详情页面或显示详情弹窗
			console.log('查看详情:', item)
		},

		async handleEdit(item) {
			try {
				const response = await getGiftRuleDetail(item.ruleId)
				if (response.code === 200) {
					this.formData = { ...response.data }

					// 设置日期选择器的值
					if (this.formData.effectiveDate) {
						const effectiveDate = new Date(this.formData.effectiveDate)
						const yearIndex = this.yearOptions.indexOf(effectiveDate.getFullYear())
						const monthIndex = effectiveDate.getMonth()
						const dayIndex = effectiveDate.getDate() - 1
						this.effectiveDatePickerValue = [yearIndex, monthIndex, dayIndex]
						this.tempEffectiveDate = [yearIndex, monthIndex, dayIndex]
					}
					if (this.formData.expiryDate) {
						const expiryDate = new Date(this.formData.expiryDate)
						const yearIndex = this.yearOptions.indexOf(expiryDate.getFullYear())
						const monthIndex = expiryDate.getMonth()
						const dayIndex = expiryDate.getDate() - 1
						this.expiryDatePickerValue = [yearIndex, monthIndex, dayIndex]
						this.tempExpiryDate = [yearIndex, monthIndex, dayIndex]
					}

					this.isEdit = true
					this.showFormDialog = true
				} else {
					toast(response.msg || '获取详情失败')
				}
			} catch (error) {
				console.error('获取详情失败:', error)
				toast('获取详情失败，请重试')
			}
		},

		async handleStatusChange(item) {
			const action = item.status === '1' ? '禁用' : '启用'

			uni.showModal({
				title: '确认操作',
				content: `确定要${action}规则"${item.ruleName}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const newStatus = item.status === '1' ? '0' : '1'
							const response = await changeGiftRuleStatus({
								ruleId: item.ruleId,
								status: newStatus
							})

							if (response.code === 200) {
								toast(`${action}成功`)
								this.loadGiftRuleList(true)
							} else {
								toast(response.msg || `${action}失败`)
							}
						} catch (error) {
							console.error(`${action}失败:`, error)
							toast(`${action}失败，请重试`)
						}
					}
				}
			})
		},

		async handleDelete(item) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除规则"${item.ruleName}"吗？删除后不可恢复。`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await deleteGiftRule(item.ruleId)

							if (response.code === 200) {
								toast('删除成功')
								this.loadGiftRuleList(true)
							} else {
								toast(response.msg || '删除失败')
							}
						} catch (error) {
							console.error('删除失败:', error)
							toast('删除失败，请重试')
						}
					}
				}
			})
		},

		// 工具方法
		// 过滤参数，移除null、undefined和空字符串
		filterParams(params) {
			const filteredParams = {}
			Object.keys(params).forEach(key => {
				const value = params[key]
				if (value !== null && value !== undefined && value !== '') {
					filteredParams[key] = value
				}
			})
			return filteredParams
		},

		getRuleTypeIcon(ruleType) {
			switch (ruleType) {
				case 'amount_based':
					return '💰'
				case 'attendance_based':
					return '📅'
				default:
					return '⚙️'
			}
		},

		getRuleTypeText(ruleType) {
			switch (ruleType) {
				case 'amount_based':
					return '金额型'
				case 'attendance_based':
					return '出勤型'
				default:
					return '未知类型'
			}
		},

		getRuleTypeLabel(value) {
			const option = this.ruleTypeOptions.find(item => item.value === value)
			return option ? option.label : ''
		},

		getStatusClass(status) {
			return status === '1' ? 'active' : 'inactive'
		},

		getStatusText(status) {
			return status === '1' ? '启用' : '禁用'
		},

		getTriggerCondition(item) {
			if (item.ruleType === 'amount_based') {
				return `消费满${item.triggerAmount || 0}元`
			} else if (item.ruleType === 'attendance_based') {
				return `出勤率达到${item.triggerAttendanceRate || 0}%`
			}
			return '未设置'
		},

		getValidPeriod(item) {
			if (item.effectiveDate && item.expiryDate) {
				return `${item.effectiveDate} 至 ${item.expiryDate}`
			} else if (item.effectiveDate) {
				return `${item.effectiveDate} 起生效`
			} else if (item.expiryDate) {
				return `${item.expiryDate} 前有效`
			}
			return '长期有效'
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(156, 39, 176, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 搜索筛选栏 */
.search-filter-section {
	padding: 24rpx 30rpx;
	background: #ffffff;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.search-box {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 24rpx;
	padding: 16rpx 24rpx;
	position: relative;
}

.search-icon {
	font-size: 28rpx;
	margin-right: 16rpx;
	color: #999;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
}

.clear-icon {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #ccc;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	color: #fff;
	margin-left: 16rpx;
}

.filter-actions {
	display: flex;
	gap: 16rpx;
}

.filter-chip {
	padding: 12rpx 24rpx;
	background: #9C27B0;
	color: #ffffff;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
}

/* 空状态和加载状态 */
.empty-container, .loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-illustration {
	margin-bottom: 32rpx;
}

.empty-emoji {
	font-size: 120rpx;
	display: block;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.empty-subtitle {
	font-size: 28rpx;
	color: #666;
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	margin-top: 16rpx;
}

/* 页面内容 */
.content {
	padding: 24rpx 30rpx;
}

.list-header {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 24rpx;
	display: block;
}

/* 规则卡片 */
.gift-rule-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.left-section {
	display: flex;
	align-items: center;
	flex: 1;
}

.rule-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.icon-text {
	font-size: 32rpx;
}

.info-section {
	flex: 1;
}

.primary-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.secondary-text {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 500;

	&.active {
		background: #e8f5e8;
		color: #52c41a;
	}

	&.inactive {
		background: #fff2f0;
		color: #ff4d4f;
	}
}

.badge-text {
	font-size: 22rpx;
}

.card-body {
	padding: 24rpx;
}

.rule-details {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.detail-row {
	display: flex;
	align-items: flex-start;
	gap: 16rpx;
}

.detail-label {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
	min-width: 140rpx;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
	line-height: 1.4;
}

.card-actions {
	display: flex;
	padding: 20rpx 24rpx;
	gap: 16rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #fafafa;
}

.action-btn {
	flex: 1;
	padding: 16rpx 24rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	font-weight: 500;
	border: none;
	transition: all 0.3s ease;

	&.primary {
		background: #9C27B0;
		color: #ffffff;
	}

	&.secondary {
		background: #f0f0f0;
		color: #666;
	}

	&.danger {
		background: #ff4d4f;
		color: #ffffff;
	}

	&:active {
		transform: scale(0.95);
	}
}

.btn-text {
	font-size: 26rpx;
}

/* 加载更多和没有更多 */
.load-more, .no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	text-align: center;
}

.load-more-text, .no-more-text {
	font-size: 28rpx;
	color: #666;
	margin-left: 16rpx;
}

/* 筛选弹窗 */
.filter-popup {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	overflow: hidden;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.popup-close {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.filter-content {
	padding: 40rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.filter-group {
	margin-bottom: 40rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.filter-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.filter-options {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.filter-option {
	padding: 16rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 24rpx;
	background: #ffffff;
	transition: all 0.3s ease;

	&.active {
		border-color: #9C27B0;
		background: #9C27B0;
		color: #ffffff;
	}

	&.radio-option {
		display: flex;
		align-items: center;
		gap: 12rpx;
		padding: 12rpx 24rpx;
		border-radius: 32rpx;

		&.active {
			background: rgba(156, 39, 176, 0.1);
			border-color: #9C27B0;
			color: #9C27B0;
		}
	}
}

.radio-icon {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	.radio-option.active & {
		border-color: #9C27B0;
	}
}

.radio-dot {
	width: 16rpx;
	height: 16rpx;
	background: #9C27B0;
	border-radius: 50%;
}

.option-text {
	font-size: 26rpx;
	font-weight: 500;
}

.filter-actions {
	display: flex;
	padding: 32rpx 40rpx;
	gap: 24rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #fafafa;
}

.filter-btn {
	flex: 1;
	padding: 24rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 600;
	border: none;
	transition: all 0.3s ease;

	&.reset {
		background: #f0f0f0;
		color: #666;
	}

	&.confirm {
		background: #9C27B0;
		color: #ffffff;
	}

	&:active {
		transform: scale(0.95);
	}
}

/* 表单弹窗 */
.form-dialog {
	background: #ffffff;
	border-radius: 20rpx;
	max-height: 90vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.dialog-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: #fafafa;
}

.dialog-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.dialog-close {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.dialog-content {
	flex: 1;
	padding: 40rpx;
	max-height: 70vh;
	overflow-y: auto;
}

.form-group {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.required {
	color: #ff4d4f;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #ffffff;
	min-height: 80rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;

	&:empty::before {
		content: attr(placeholder);
		color: #999;
	}
}

/* 表单中的单选框样式 */
.radio-group {
	display: flex;
	gap: 32rpx;
	margin-top: 16rpx;
}

.radio-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 24rpx;
	background: #ffffff;
	transition: all 0.3s ease;
	cursor: pointer;

	&:active {
		transform: scale(0.98);
	}

	&.active {
		border-color: #9C27B0;
		background: rgba(156, 39, 176, 0.05);
	}
}

.radio-circle {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	.radio-item.active & {
		border-color: #9C27B0;
	}
}

.radio-dot {
	width: 16rpx;
	height: 16rpx;
	background: #9C27B0;
	border-radius: 50%;
}

.radio-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;

	.radio-item.active & {
		color: #9C27B0;
	}
}

.dialog-actions {
	display: flex;
	padding: 32rpx 40rpx;
	gap: 24rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #fafafa;
}

.dialog-btn {
	flex: 1;
	padding: 24rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 600;
	border: none;
	transition: all 0.3s ease;

	&.cancel {
		background: #f0f0f0;
		color: #666;
	}

	&.confirm {
		background: #9C27B0;
		color: #ffffff;
	}

	&:active {
		transform: scale(0.95);
	}
}

/* 日期选择器样式 */
.date-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 9999;
}

.date-picker-container {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	width: 100%;
	max-height: 60vh;
	overflow: hidden;
}

.date-picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: #fafafa;
}

.picker-cancel, .picker-confirm {
	font-size: 28rpx;
	font-weight: 500;
	color: #9C27B0;
	padding: 16rpx 24rpx;
	border-radius: 8rpx;
	transition: all 0.3s ease;

	&:active {
		background: rgba(156, 39, 176, 0.1);
	}
}

.picker-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.date-picker-view {
	height: 400rpx;
	background: #ffffff;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
}

/* 课程下拉框样式 */
.course-select-container {
	position: relative;
}

.course-select-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #ffffff;
	min-height: 80rpx;
	transition: all 0.3s ease;

	&:active {
		border-color: #9C27B0;
	}
}

.course-select-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;

	&:empty::before {
		content: '请选择赠送课程';
		color: #999;
	}
}

.rotate {
	transform: rotate(180deg);
	transition: transform 0.3s ease;
}

.course-dropdown {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: #ffffff;
	border: 2rpx solid #e0e0e0;
	border-top: none;
	border-radius: 0 0 12rpx 12rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	z-index: 1000;
	max-height: 600rpx;
	overflow: hidden;
}

.course-search {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.course-search-input {
	width: 100%;
	padding: 16rpx 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 26rpx;
	background: #f8f9fa;
}

.course-list {
	max-height: 400rpx;
}

.course-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.3s ease;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background: #f8f9fa;
	}

	&.active {
		background: rgba(156, 39, 176, 0.05);
		border-color: #9C27B0;
	}
}

.course-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.course-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.course-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.course-selected {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 浮动新增按钮 */
.floating-add-btn {
	position: fixed;
	bottom: 120rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(156, 39, 176, 0.4);
	z-index: 999;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		box-shadow: 0 4rpx 16rpx rgba(156, 39, 176, 0.6);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 50%;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
		pointer-events: none;
	}
}

/* 浮动按钮动画效果 */
@keyframes float {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-8rpx);
	}
}

.floating-add-btn {
	animation: float 3s ease-in-out infinite;
}
</style>
