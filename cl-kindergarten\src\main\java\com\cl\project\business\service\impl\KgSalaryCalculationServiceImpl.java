package com.cl.project.business.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cl.project.business.domain.*;
import com.cl.project.business.mapper.*;
import com.cl.project.business.service.IKgSalaryCalculationService;
import com.cl.project.business.service.IKgAttendanceStatisticsService;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;

/**
 * 工资计算Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class KgSalaryCalculationServiceImpl implements IKgSalaryCalculationService 
{
    @Autowired
    private KgTeacherMapper teacherMapper;
    
    @Autowired
    private KgTeacherAttendanceMapper teacherAttendanceMapper;
    
    @Autowired
    private KgTeacherSalaryMapper teacherSalaryMapper;
    
    @Autowired
    private KgClassMapper classMapper;
    
    @Autowired
    private KgStudentMapper studentMapper;
    
    @Autowired
    private IKgAttendanceStatisticsService attendanceStatisticsService;
    
    @Autowired
    private KgCourseAttendanceMapper courseAttendanceMapper;
    
    @Autowired
    private KgStudentAttendanceMapper studentAttendanceMapper;
    
    @Autowired
    private KgSalaryConfigMapper salaryConfigMapper;
    
    @Autowired
    private KgMessagePushMapper messagePushMapper;

    /**
     * 计算教师月度工资（优先返回已保存的数据）
     */
    @Override
    public Map<String, Object> calculateMonthlySalary(Long teacherId, Integer year, Integer month) 
    {
        // 获取教师信息
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }

        // 首先检查是否已存在该月份的工资记录
        KgTeacherSalary queryParam = new KgTeacherSalary();
        queryParam.setTeacherId(teacherId);
        queryParam.setSalaryYear(Long.valueOf(year));
        queryParam.setSalaryMonth(Long.valueOf(month));
        
        List<KgTeacherSalary> existingSalaries = teacherSalaryMapper.selectKgTeacherSalaryList(queryParam);
        
        if (!existingSalaries.isEmpty()) {
            // 如果已存在工资记录，直接返回已保存的数据
            return buildResultFromSavedRecord(existingSalaries.get(0), teacher);
        }
        
        // 如果没有已保存的记录，才进行实时计算
        return calculateSalaryRealTime(teacherId, year, month, teacher);
    }
    
    /**
     * 从已保存的工资记录构建返回结果
     */
    private Map<String, Object> buildResultFromSavedRecord(KgTeacherSalary savedRecord, KgTeacher teacher) {
        Map<String, Object> result = new HashMap<>();
        
        // 设置真实的salaryId
        teacher.setSalaryId(savedRecord.getSalaryId());
        
        // 构建考勤统计信息
        Map<String, Object> attendanceStats = new HashMap<>();
        attendanceStats.put("attendanceDays", savedRecord.getAttendanceDays().intValue());
        attendanceStats.put("workDays", savedRecord.getTotalWorkDays().intValue());
        
        // 计算出勤率
        Double attendanceRate = savedRecord.getTotalWorkDays().doubleValue() > 0 ? 
            (savedRecord.getAttendanceDays().doubleValue() / savedRecord.getTotalWorkDays().doubleValue()) * 100 : 0.0;
        attendanceStats.put("attendanceRate", Math.round(attendanceRate * 100.0) / 100.0);
        attendanceStats.put("absentDays", savedRecord.getTotalWorkDays().intValue() - savedRecord.getAttendanceDays().intValue());
        
        // 构建工资明细信息（使用已保存的数据）
        Map<String, Object> salaryBreakdown = new HashMap<>();
        salaryBreakdown.put("baseSalary", savedRecord.getBaseSalary().doubleValue());
        salaryBreakdown.put("attendanceBonus", savedRecord.getAttendanceBonus().doubleValue());
        salaryBreakdown.put("courseFee", savedRecord.getCourseBonus().doubleValue());
        salaryBreakdown.put("enrollmentBonus", savedRecord.getEnrollmentBonus().doubleValue());
        salaryBreakdown.put("attendanceRateBonus", savedRecord.getAttendanceRateBonus().doubleValue());
        salaryBreakdown.put("newStudentBonus", savedRecord.getNewStudentBonus().doubleValue());
        salaryBreakdown.put("withdrawalDeduction", savedRecord.getWithdrawalPenalty().doubleValue());
        salaryBreakdown.put("grossSalary", savedRecord.getGrossSalary().doubleValue());
        salaryBreakdown.put("socialInsurance", savedRecord.getSocialInsurance().doubleValue());
        salaryBreakdown.put("netSalary", savedRecord.getNetSalary().doubleValue());
        
        // 组装结果
        result.put("teacherInfo", teacher);
        result.put("attendanceStats", attendanceStats);
        result.put("salaryBreakdown", salaryBreakdown);
        result.put("calculationDate", savedRecord.getCreateTime());
        result.put("salaryStatus", savedRecord.getSalaryStatus());
        
        return result;
    }
    
    /**
     * 实时计算工资（预览模式）
     */
    private Map<String, Object> calculateSalaryRealTime(Long teacherId, Integer year, Integer month, KgTeacher teacher) {
        Map<String, Object> result = new HashMap<>();
        
        // 计算考勤统计
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        
        // 获取教师考勤统计
        Map<String, Object> attendanceStats = getTeacherAttendanceStats(teacherId, startDate, endDate);
        Integer attendanceDays = (Integer) attendanceStats.get("attendanceDays");
        Integer workDays = (Integer) attendanceStats.get("workDays");
        Double attendanceRate = (Double) attendanceStats.get("attendanceRate");
        
        // 计算各项工资组成
        Double baseSalary = calculateBaseSalary(teacherId, attendanceDays, workDays);
        Double attendanceBonus = calculateAttendanceBonus(teacherId, attendanceRate);
        Double courseFee = calculateCourseFee(teacherId, year, month);
        Double enrollmentBonus = calculateEnrollmentBonus(teacherId, year, month);
        Double attendanceRateBonus = calculateAttendanceRateBonus(teacherId, year, month);
        Double newStudentBonus = calculateNewStudentBonus(teacherId, year, month);
        Double withdrawalDeduction = calculateWithdrawalDeduction(teacherId, year, month);
        
        // 计算税前工资
        Double grossSalary = baseSalary + attendanceBonus + courseFee + enrollmentBonus 
                           + attendanceRateBonus + newStudentBonus - withdrawalDeduction;
        
        // 计算社保代扣
        Double socialInsuranceDeduction = calculateSocialInsuranceDeduction(teacherId, grossSalary);
        
        // 计算实发工资
        Double netSalary = grossSalary - socialInsuranceDeduction;
        
        // 使用临时ID标识（teacherId）
        teacher.setSalaryId(teacherId);
        
        // 组装工资明细
        Map<String, Object> salaryBreakdown = new HashMap<>();
        salaryBreakdown.put("baseSalary", baseSalary);
        salaryBreakdown.put("attendanceBonus", attendanceBonus);
        salaryBreakdown.put("courseFee", courseFee);
        salaryBreakdown.put("enrollmentBonus", enrollmentBonus);
        salaryBreakdown.put("attendanceRateBonus", attendanceRateBonus);
        salaryBreakdown.put("newStudentBonus", newStudentBonus);
        salaryBreakdown.put("withdrawalDeduction", withdrawalDeduction);
        salaryBreakdown.put("grossSalary", grossSalary);
        salaryBreakdown.put("socialInsurance", socialInsuranceDeduction);
        salaryBreakdown.put("netSalary", netSalary);
        
        // 组装结果
        result.put("teacherInfo", teacher);
        result.put("attendanceStats", attendanceStats);
        result.put("salaryBreakdown", salaryBreakdown);
        result.put("calculationDate", DateUtils.getNowDate());
        result.put("salaryStatus", "preview"); // 预览状态
        
        return result;
    }
    
    /**
     * 更新工资记录字段
     */
    private void updateSalaryRecord(KgTeacherSalary salaryRecord, Integer attendanceDays, Integer workDays,
                                  Double baseSalary, Double attendanceBonus, Double courseFee, 
                                  Double enrollmentBonus, Double attendanceRateBonus, Double newStudentBonus,
                                  Double withdrawalDeduction, Double socialInsuranceDeduction, 
                                  Double grossSalary, Double netSalary) {
        salaryRecord.setAttendanceDays(Long.valueOf(attendanceDays));
        salaryRecord.setTotalWorkDays(Long.valueOf(workDays));
        salaryRecord.setBaseSalary(BigDecimal.valueOf(baseSalary));
        salaryRecord.setAttendanceBonus(BigDecimal.valueOf(attendanceBonus));
        salaryRecord.setCourseBonus(BigDecimal.valueOf(courseFee));
        salaryRecord.setEnrollmentBonus(BigDecimal.valueOf(enrollmentBonus));
        salaryRecord.setAttendanceRateBonus(BigDecimal.valueOf(attendanceRateBonus));
        salaryRecord.setNewStudentBonus(BigDecimal.valueOf(newStudentBonus));
        salaryRecord.setWithdrawalPenalty(BigDecimal.valueOf(withdrawalDeduction));
        salaryRecord.setSocialInsurance(BigDecimal.valueOf(socialInsuranceDeduction));
        salaryRecord.setGrossSalary(BigDecimal.valueOf(grossSalary));
        salaryRecord.setNetSalary(BigDecimal.valueOf(netSalary));
        salaryRecord.setUpdateBy("system");
        salaryRecord.setUpdateTime(new Date());
    }

    /**
     * 批量计算全体教师工资
     */
    @Override
    public Map<String, Object> calculateAllSalaryBatch(Integer year, Integer month) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有教师
        List<KgTeacher> teachers = teacherMapper.selectKgTeacherList(new KgTeacher());
        
        List<Map<String, Object>> teacherResults = new ArrayList<>();
        double totalGrossSalary = 0;
        double totalNetSalary = 0;
        int successCount = 0;
        int errorCount = 0;
        
        for (KgTeacher teacher : teachers) {
            try {
                Map<String, Object> teacherResult = calculateMonthlySalary(teacher.getTeacherId(), year, month);
                Map<String, Object> salaryBreakdown = (Map<String, Object>) teacherResult.get("salaryBreakdown");
                
                teacherResult.put("teacherName", teacher.getTeacherName());
                teacherResult.put("position", teacher.getPosition());
                teacherResults.add(teacherResult);
                
                totalGrossSalary += (Double) salaryBreakdown.get("grossSalary");
                totalNetSalary += (Double) salaryBreakdown.get("netSalary");
                successCount++;
            } catch (Exception e) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("teacherId", teacher.getTeacherId());
                errorResult.put("teacherName", teacher.getTeacherName());
                errorResult.put("error", e.getMessage());
                teacherResults.add(errorResult);
                errorCount++;
            }
        }
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalTeachers", teachers.size());
        summary.put("successCount", successCount);
        summary.put("errorCount", errorCount);
        summary.put("totalGrossSalary", totalGrossSalary);
        summary.put("totalNetSalary", totalNetSalary);
        summary.put("averageGrossSalary", successCount > 0 ? totalGrossSalary / successCount : 0);
        summary.put("averageNetSalary", successCount > 0 ? totalNetSalary / successCount : 0);
        
        result.put("year", year);
        result.put("month", month);
        result.put("teacherResults", teacherResults);
        result.put("summary", summary);
        result.put("calculationTime", DateUtils.getNowDate());
        
        return result;
    }

    /**
     * 预览工资计算结果
     */
    @Override
    public Map<String, Object> previewSalaryCalculation(Long teacherId, Integer year, Integer month) 
    {
        // 预览与实际计算逻辑相同，但不保存到数据库
        return calculateMonthlySalary(teacherId, year, month);
    }

    /**
     * 获取工资计算规则
     */
    @Override
    public Map<String, Object> getSalaryCalculationRules(Long teacherId) 
    {
        Map<String, Object> rules = new HashMap<>();
        
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }
        
        Map<String, String> calculationRules = new HashMap<>();
        calculationRules.put("baseSalaryRule", "基本工资 = 月基本工资 × (出勤天数 / 应出勤天数)");
        calculationRules.put("attendanceBonusRule", "满勤奖 = 出勤率 ≥ 95% ? 满勤奖金额 : 0");
        calculationRules.put("courseFeeRule", "课时费 = 课时数 × 课时单价");
        calculationRules.put("enrollmentBonusRule", "报名奖励 = 新报名学生数 × 报名奖励单价");
        calculationRules.put("attendanceRateBonusRule", "出勤率奖励 = 班级平均出勤率 ≥ 90% ? 奖励金额 : 0");
        calculationRules.put("newStudentBonusRule", "新生奖励 = 新生数量 × 新生奖励单价");
        calculationRules.put("withdrawalDeductionRule", "退园扣款 = 退园学生数 × 扣款单价");
        calculationRules.put("socialInsuranceRule", "社保代扣 = 税前工资 × 社保比例");
        
        rules.put("teacherInfo", teacher);
        rules.put("calculationRules", calculationRules);
        
        return rules;
    }

    /**
     * 生成工资单（将计算结果保存到数据库）
     */
    @Override
    public int generatePayslips(List<Map<String, Object>> calculations) 
    {
        int count = 0;
        String currentUser = SecurityUtils.getUsername();
        
        for (Map<String, Object> calculation : calculations) {
            try {
                KgTeacher teacherInfo = (KgTeacher) calculation.get("teacherInfo");
                Map<String, Object> salaryBreakdown = (Map<String, Object>) calculation.get("salaryBreakdown");
                Map<String, Object> attendanceStats = (Map<String, Object>) calculation.get("attendanceStats");
                
                Long teacherId = teacherInfo.getTeacherId();
                
                // 从计算日期中获取年月（如果有的话）
                Date calculationDate = (Date) calculation.get("calculationDate");
                Calendar cal = Calendar.getInstance();
                if (calculationDate != null) {
                    cal.setTime(calculationDate);
                }
                Integer year = cal.get(Calendar.YEAR);
                Integer month = cal.get(Calendar.MONTH) + 1;
                
                // 检查是否已存在该月份的工资记录
                KgTeacherSalary queryParam = new KgTeacherSalary();
                queryParam.setTeacherId(teacherId);
                queryParam.setSalaryYear(Long.valueOf(year));
                queryParam.setSalaryMonth(Long.valueOf(month));
                
                List<KgTeacherSalary> existingSalaries = teacherSalaryMapper.selectKgTeacherSalaryList(queryParam);
                
                KgTeacherSalary salaryRecord;
                if (!existingSalaries.isEmpty()) {
                    // 更新已存在的记录
                    salaryRecord = existingSalaries.get(0);
                } else {
                    // 创建新的工资记录
                    salaryRecord = new KgTeacherSalary();
                    salaryRecord.setTeacherId(teacherId);
                    salaryRecord.setSalaryYear(Long.valueOf(year));
                    salaryRecord.setSalaryMonth(Long.valueOf(month));
                    salaryRecord.setCreateBy(currentUser);
                    salaryRecord.setCreateTime(new Date());
                }
                
                // 更新工资记录字段
                Integer attendanceDays = (Integer) attendanceStats.get("attendanceDays");
                Integer workDays = (Integer) attendanceStats.get("workDays");
                
                updateSalaryRecord(salaryRecord, attendanceDays, workDays,
                    (Double) salaryBreakdown.get("baseSalary"),
                    (Double) salaryBreakdown.get("attendanceBonus"),
                    (Double) salaryBreakdown.get("courseFee"),
                    (Double) salaryBreakdown.get("enrollmentBonus"),
                    (Double) salaryBreakdown.get("attendanceRateBonus"),
                    (Double) salaryBreakdown.get("newStudentBonus"),
                    (Double) salaryBreakdown.get("withdrawalDeduction"),
                    (Double) salaryBreakdown.get("socialInsurance"),
                    (Double) salaryBreakdown.get("grossSalary"),
                    (Double) salaryBreakdown.get("netSalary")
                );
                
                salaryRecord.setSalaryStatus("calculated");
                salaryRecord.setUpdateBy(currentUser);
                salaryRecord.setUpdateTime(new Date());
                
                if (existingSalaries.isEmpty()) {
                    teacherSalaryMapper.insertKgTeacherSalary(salaryRecord);
                } else {
                    teacherSalaryMapper.updateKgTeacherSalary(salaryRecord);
                }
                
                count++;
            } catch (Exception e) {
                // 记录错误但继续处理其他记录
                e.printStackTrace();
                continue;
            }
        }
        
        return count;
    }

    /**
     * 重新计算工资
     */
    @Override
    public Map<String, Object> recalculateSalary(Long salaryId, String reason) 
    {
        KgTeacherSalary originalSalary = teacherSalaryMapper.selectKgTeacherSalaryById(salaryId);
        if (originalSalary == null) {
            throw new RuntimeException("工资记录不存在");
        }
        
        // 解析工资月份
        Long salaryMonth = originalSalary.getSalaryMonth();
        Integer year = (int) (salaryMonth / 100);
        Integer month = (int) (salaryMonth % 100);
        
        // 重新计算
        Map<String, Object> newCalculation = calculateMonthlySalary(originalSalary.getTeacherId(), year, month);
        Map<String, Object> salaryBreakdown = (Map<String, Object>) newCalculation.get("salaryBreakdown");
        
        // 更新工资记录
        originalSalary.setBaseSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("baseSalary")));
        originalSalary.setAttendanceBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("attendanceBonus")));
        originalSalary.setCourseBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("courseFee")));
        originalSalary.setEnrollmentBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("enrollmentBonus")));
        originalSalary.setGrossSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("grossSalary")));
        originalSalary.setSocialInsurance(BigDecimal.valueOf((Double) salaryBreakdown.get("socialInsuranceDeduction")));
        originalSalary.setNetSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("netSalary")));
        originalSalary.setUpdateBy(SecurityUtils.getUsername());
        originalSalary.setUpdateTime(DateUtils.getNowDate());
        originalSalary.setRemark(reason);
        
        teacherSalaryMapper.updateKgTeacherSalary(originalSalary);
        
        return newCalculation;
    }

    /**
     * 工资调整
     */
    @Override
    public int adjustSalary(Long salaryId, String adjustType, Double adjustAmount, String reason) 
    {
        KgTeacherSalary salary = teacherSalaryMapper.selectKgTeacherSalaryById(salaryId);
        if (salary == null) {
            throw new RuntimeException("工资记录不存在");
        }
        
        BigDecimal adjustment = BigDecimal.valueOf(adjustAmount);
        
        switch (adjustType) {
            case "base_salary":
                salary.setBaseSalary(salary.getBaseSalary().add(adjustment));
                break;
            case "attendance_bonus":
                salary.setAttendanceBonus(salary.getAttendanceBonus().add(adjustment));
                break;
            case "course_fee":
                salary.setCourseBonus(salary.getCourseBonus().add(adjustment));
                break;
            case "enrollment_bonus":
                salary.setEnrollmentBonus(salary.getEnrollmentBonus().add(adjustment));
                break;
            default:
                throw new RuntimeException("不支持的调整类型: " + adjustType);
        }
        
        // 重新计算总工资
        BigDecimal newGrossSalary = salary.getBaseSalary()
                .add(salary.getAttendanceBonus())
                .add(salary.getCourseBonus())
                .add(salary.getEnrollmentBonus());
        salary.setGrossSalary(newGrossSalary);
        
        // 重新计算实发工资
        BigDecimal newNetSalary = newGrossSalary.subtract(salary.getSocialInsurance());
        salary.setNetSalary(newNetSalary);
        
        salary.setUpdateBy(SecurityUtils.getUsername());
        salary.setUpdateTime(DateUtils.getNowDate());
        salary.setRemark(reason);
        
        return teacherSalaryMapper.updateKgTeacherSalary(salary);
    }

    /**
     * 获取工资统计
     */
    @Override
    public Map<String, Object> getSalaryStatistics(Integer year, Integer month) 
    {
        Map<String, Object> statistics = new HashMap<>();
        
        // 构建查询条件
        KgTeacherSalary queryParams = new KgTeacherSalary();
        queryParams.setSalaryMonth(Long.valueOf(month));
        queryParams.setSalaryYear( Long.valueOf( year));
        
        List<KgTeacherSalary> salaries = teacherSalaryMapper.selectKgTeacherSalaryList(queryParams);
        
        if (salaries.isEmpty()) {
            statistics.put("totalRecords", 0);
            statistics.put("message", "暂无工资数据");
            return statistics;
        }
        
        // 统计各项数据
        BigDecimal totalBaseSalary = BigDecimal.ZERO;
        BigDecimal totalGrossSalary = BigDecimal.ZERO;
        BigDecimal totalNetSalary = BigDecimal.ZERO;
        BigDecimal totalDeductions = BigDecimal.ZERO;
        
        Map<String, Long> statusCount = salaries.stream()
                .collect(Collectors.groupingBy(
                    salary -> salary.getSalaryStatus(),
                    Collectors.counting()
                ));
        
        for (KgTeacherSalary salary : salaries) {
            totalBaseSalary = totalBaseSalary.add(salary.getBaseSalary());
            totalGrossSalary = totalGrossSalary.add(salary.getGrossSalary());
            totalNetSalary = totalNetSalary.add(salary.getNetSalary());
            totalDeductions = totalDeductions.add(salary.getSocialInsurance());
        }
        
        Map<String, Object> salaryBreakdown = new HashMap<>();
        salaryBreakdown.put("totalBaseSalary", totalBaseSalary);
        salaryBreakdown.put("totalGrossSalary", totalGrossSalary);
        salaryBreakdown.put("totalNetSalary", totalNetSalary);
        salaryBreakdown.put("totalDeductions", totalDeductions);
        salaryBreakdown.put("averageGrossSalary", totalGrossSalary.divide(BigDecimal.valueOf(salaries.size()), 2, RoundingMode.HALF_UP));
        salaryBreakdown.put("averageNetSalary", totalNetSalary.divide(BigDecimal.valueOf(salaries.size()), 2, RoundingMode.HALF_UP));
        
        statistics.put("year", year);
        statistics.put("month", month);
        statistics.put("totalRecords", salaries.size());
        statistics.put("salaryBreakdown", salaryBreakdown);
        statistics.put("statusStatistics", statusCount);
        
        return statistics;
    }

    /**
     * 获取教师工资明细
     */
    @Override
    public Map<String, Object> getSalaryDetails(Long teacherId, Integer year, Integer month) 
    {
        Map<String, Object> details = new HashMap<>();
        
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }
        
        // 查询工资记录
        KgTeacherSalary queryParams = new KgTeacherSalary();
        queryParams.setTeacherId(teacherId);
        queryParams.setSalaryMonth(Long.valueOf(String.format("%d%02d", year, month)));
        
        List<KgTeacherSalary> salaries = teacherSalaryMapper.selectKgTeacherSalaryList(queryParams);
        
        details.put("teacherInfo", teacher);
        details.put("year", year);
        details.put("month", month);
        details.put("salaryRecords", salaries);
        
        return details;
    }

    /**
     * 工资发放确认
     */
    @Override
    public int confirmSalaryPayment(List<Long> salaryIds) 
    {
        int count = 0;
        String currentUser = SecurityUtils.getUsername();
        
        for (Long salaryId : salaryIds) {
            KgTeacherSalary salary = teacherSalaryMapper.selectKgTeacherSalaryById(salaryId);
            if (salary != null && !"paid".equals(salary.getSalaryStatus())) {
                salary.setSalaryStatus("paid");
                salary.setPaidTime(DateUtils.getNowDate());
                salary.setUpdateBy(currentUser);
                salary.setUpdateTime(DateUtils.getNowDate());
                
                teacherSalaryMapper.updateKgTeacherSalary(salary);
                count++;
            }
        }
        
        return count;
    }

    /**
     * 生成工资报表
     */
    @Override
    public String generateSalaryReport(Integer year, Integer month) 
    {
        Map<String, Object> statistics = getSalaryStatistics(year, month);
        
        // 这里可以集成报表生成工具（如JasperReports、POI等）
        // 现在简单返回一个文件路径
        String fileName = String.format("salary_report_%d_%02d.xlsx", year, month);
        String filePath = "/reports/" + fileName;
        
        // TODO: 实际的报表生成逻辑
        
        return filePath;
    }

    /**
     * 计算课时费统计
     */
    @Override
    public Map<String, Object> getCourseFeeStatistics(Long teacherId, Integer year, Integer month) 
    {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 获取教师信息
            KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
            if (teacher == null) {
                throw new RuntimeException("教师不存在");
            }
            
            // 获取教师对应的班级
            KgClass teacherClass = getTeacherClass(teacherId);
            if (teacherClass == null) {
                statistics.put("teacherId", teacherId);
                statistics.put("year", year);
                statistics.put("month", month);
                statistics.put("totalCourseHours", 0);
                statistics.put("totalCourseFee", 0.0);
                statistics.put("courseDetails", new ArrayList<>());
                return statistics;
            }
            
            // 查询该月份托管课程考勤记录
            Calendar cal = Calendar.getInstance();
            cal.set(year, month - 1, 1);
            Date startDate = cal.getTime();
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            Date endDate = cal.getTime();
            
            // 通过课程考勤查询该教师班级的课程记录
            List<KgCourseAttendance> monthlyAttendance = courseAttendanceMapper.selectKgCourseAttendanceList(
                new KgCourseAttendance() {{
                    setClassId(teacherClass.getClassId());
                }}
            ).stream()
            .filter(a -> a.getAttendanceDate().after(startDate) && a.getAttendanceDate().before(endDate))
            .collect(Collectors.toList());
            
            // 获取班级学生ID列表
            List<Long> classStudentIds = getClassStudentIds(teacherClass.getClassId());
            
            // 按课程分组统计
            Map<Long, List<KgCourseAttendance>> courseGroups = monthlyAttendance.stream()
                .collect(Collectors.groupingBy(KgCourseAttendance::getCourseId));
            
            List<Map<String, Object>> courseDetails = new ArrayList<>();
            double totalCourseFee = 0.0;
            int totalCourseHours = 0;
            
            for (Map.Entry<Long, List<KgCourseAttendance>> entry : courseGroups.entrySet()) {
                Long courseId = entry.getKey();
                List<KgCourseAttendance> courseAttendances = entry.getValue();
                
                // 统计该课程的参与学生数
                Set<Long> attendingStudentIds = courseAttendances.stream()
                    .filter(a -> "present".equals(a.getAttendanceStatus()))
                    .map(KgCourseAttendance::getStudentId)
                    .collect(Collectors.toSet());
                
                int studentCount = attendingStudentIds.size();
                
                // 统计授课次数（不重复日期）
                Set<Date> uniqueDates = courseAttendances.stream()
                    .map(KgCourseAttendance::getAttendanceDate)
                    .collect(Collectors.toSet());
                
                int teachingCount = uniqueDates.size();
                
                // 计算课时费：8人以上每次10元，否则5元
                double hourlyRate = studentCount >= 8 ? 10.0 : 5.0;
                double subtotal = teachingCount * hourlyRate;
                
                // 组装课程详情
                Map<String, Object> courseDetail = new HashMap<>();
                courseDetail.put("courseId", courseId);
                courseDetail.put("courseName", "课程" + courseId); // 这里可以关联课程表获取课程名称
                courseDetail.put("teachingCount", teachingCount);
                courseDetail.put("studentCount", studentCount);
                courseDetail.put("hourlyRate", hourlyRate);
                courseDetail.put("subtotal", subtotal);
                
                courseDetails.add(courseDetail);
                totalCourseFee += subtotal;
                totalCourseHours += teachingCount;
            }
            
            // 尝试使用messagePushMapper发送通知（如果需要）
            try {
                // 可以在这里添加消息推送逻辑，比如课时费统计完成通知
                // messagePushMapper.insertMessage(...);
            } catch (Exception e) {
                // 忽略推送错误
            }
            
            statistics.put("teacherId", teacherId);
            statistics.put("teacherName", teacher.getTeacherName());
            statistics.put("year", year);
            statistics.put("month", month);
            statistics.put("totalCourseHours", totalCourseHours);
            statistics.put("totalCourseFee", totalCourseFee);
            statistics.put("courseDetails", courseDetails);
            
        } catch (Exception e) {
            System.err.println("计算课时费统计时出错: " + e.getMessage());
            statistics.put("teacherId", teacherId);
            statistics.put("year", year);
            statistics.put("month", month);
            statistics.put("totalCourseHours", 0);
            statistics.put("totalCourseFee", 0.0);
            statistics.put("courseDetails", new ArrayList<>());
            statistics.put("error", e.getMessage());
        }
        
        return statistics;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 获取教师考勤统计
     */
    private Map<String, Object> getTeacherAttendanceStats(Long teacherId, LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> stats = new HashMap<>();
        
        // 查询教师考勤记录
        KgTeacherAttendance queryParams = new KgTeacherAttendance();
        queryParams.setTeacherId(teacherId);
        List<KgTeacherAttendance> attendanceList = teacherAttendanceMapper.selectKgTeacherAttendanceList(queryParams);
        
        // 按日期过滤
        List<KgTeacherAttendance> filteredList = attendanceList.stream()
                .filter(attendance -> {
                    LocalDate attendanceDate = attendance.getAttendanceDate().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return !attendanceDate.isBefore(startDate) && !attendanceDate.isAfter(endDate);
                })
                .collect(Collectors.toList());
        
        // 统计工作日数（排除周末）
        int workDays = calculateWorkDays(startDate, endDate);
        int attendanceDays = filteredList.size();
        int absentDays = workDays - attendanceDays;
        
        // 计算出勤率
        double attendanceRate = workDays > 0 ? (double) attendanceDays / workDays * 100 : 0;
        
        stats.put("workDays", workDays);
        stats.put("attendanceDays", attendanceDays);
        stats.put("absentDays", absentDays);
        stats.put("attendanceRate", Math.round(attendanceRate * 100.0) / 100.0);
        stats.put("attendanceDetails", filteredList);
        
        return stats;
    }

    /**
     * 计算工作日数量（排除周末）
     */
    private int calculateWorkDays(LocalDate startDate, LocalDate endDate) 
    {
        int workDays = 0;
        LocalDate current = startDate;
        
        while (!current.isAfter(endDate)) {
            if (current.getDayOfWeek().getValue() < 6) { // 周一到周五
                workDays++;
            }
            current = current.plusDays(1);
        }
        
        return workDays;
    }

    // ========== 工资计算具体实现方法 ==========

    /**
     * 计算基本工资
     */
    @Override
    public Double calculateBaseSalary(Long teacherId, Integer attendanceDays, Integer workDays) 
    {
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null || teacher.getBaseSalary() == null) {
            return 0.0;
        }
        
        double baseSalaryAmount = teacher.getBaseSalary().doubleValue();
        if (workDays == 0) {
            return 0.0;
        }
        
        // 基本工资 = 月基本工资 × (出勤天数 / 应出勤天数)
        return baseSalaryAmount * attendanceDays / workDays;
    }

    /**
     * 计算满勤奖
     */
    @Override
    public Double calculateAttendanceBonus(Long teacherId, Double attendanceRate) 
    {
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            return 0.0;
        }
        
        // 出勤率 ≥ 95% 才有满勤奖，固定金额200元
        if (attendanceRate >= 95.0) {
            return 200.0;
        }
        
        return 0.0;
    }

    /**
     * 计算课时费
     */
    @Override
    public Double calculateCourseFee(Long teacherId, Integer year, Integer month) 
    {
        try {
            // 获取教师信息
            KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
            if (teacher == null) {
                return 0.0;
            }
            
            // 查询教师对应的班级（通过班级表的班主任或副班主任字段）
            KgClass teacherClass = getTeacherClass(teacherId);
            if (teacherClass == null) {
                return 0.0;
            }
            
            // 查询该班级学生的托管课程考勤记录
            List<Long> classStudentIds = getClassStudentIds(teacherClass.getClassId());
            if (classStudentIds.isEmpty()) {
                return 0.0;
            }
            
            // 使用现有的查询方法并过滤
            KgCourseAttendance queryCondition = new KgCourseAttendance();
            List<KgCourseAttendance> allAttendance = courseAttendanceMapper.selectKgCourseAttendanceList(queryCondition);
            
            // 过滤出本月的记录，并且是该班级的学生
            List<KgCourseAttendance> monthlyAttendance = allAttendance.stream()
                .filter(a -> a.getAttendanceDate() != null)
                .filter(a -> {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(a.getAttendanceDate());
                    return cal.get(Calendar.YEAR) == year && (cal.get(Calendar.MONTH) + 1) == month;
                })
                .filter(a -> classStudentIds.contains(a.getStudentId()))
                .collect(Collectors.toList());
                
            if (monthlyAttendance.isEmpty()) {
                return 0.0;
            }
            
            // 统计实际上课的学生人数和课时数
            Map<Long, Set<Long>> courseStudentMap = new HashMap<>();
            for (KgCourseAttendance attendance : monthlyAttendance) {
                if ("present".equals(attendance.getAttendanceStatus())) {
                    courseStudentMap.computeIfAbsent(attendance.getCourseId(), k -> new HashSet<>())
                                  .add(attendance.getStudentId());
                }
            }
            
            Double totalCourseFee = 0.0;
            
            // 计算每门课程的课时费
            for (Map.Entry<Long, Set<Long>> entry : courseStudentMap.entrySet()) {
                int studentCount = entry.getValue().size();
                int sessionCount = (int) monthlyAttendance.stream()
                    .filter(a -> a.getCourseId().equals(entry.getKey()) && "present".equals(a.getAttendanceStatus()))
                    .count();
                
                // 8人以上每人10元，否则5元
                double feePerStudent = studentCount >= 8 ? 10.0 : 5.0;
                totalCourseFee += sessionCount * feePerStudent;
            }
            
            return totalCourseFee;
            
        } catch (Exception e) {
            System.err.println("计算课时费时出错: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算报名奖励
     */
    @Override
    public Double calculateEnrollmentBonus(Long teacherId, Integer year, Integer month) 
    {
        try {
            // 获取教师对应的班级
            KgClass teacherClass = getTeacherClass(teacherId);
            if (teacherClass == null) {
                return 0.0;
            }
            
            // 获取班级在册学生数
            List<Long> classStudentIds = getClassStudentIds(teacherClass.getClassId());
            int enrolledStudents = classStudentIds.size();
            
            if (enrolledStudents == 0) {
                return 0.0;
            }
            
            // 查询本月托管课程考勤总数
            KgCourseAttendance queryCondition = new KgCourseAttendance();
            List<KgCourseAttendance> allAttendance = courseAttendanceMapper.selectKgCourseAttendanceList(queryCondition);
            
            int totalSessions = (int) allAttendance.stream()
                .filter(a -> a.getAttendanceDate() != null)
                .filter(a -> {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(a.getAttendanceDate());
                    return cal.get(Calendar.YEAR) == year && (cal.get(Calendar.MONTH) + 1) == month;
                })
                .filter(a -> classStudentIds.contains(a.getStudentId()))
                .filter(a -> "present".equals(a.getAttendanceStatus()))
                .mapToInt(a -> 1)
                .sum();
            
            // 计算目标课时数：班级人数50% × 12课时 × 3门课程
            int targetSessions = (int) (enrolledStudents * 0.5 * 12 * 3);
            
            // 达到50%以上按5元/课时，否则1元/课时
            double bonusRate = totalSessions >= targetSessions ? 5.0 : 1.0;
            
            return totalSessions * bonusRate;
            
        } catch (Exception e) {
            System.err.println("计算报名奖励时出错: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算出勤率奖励
     */
    @Override
    public Double calculateAttendanceRateBonus(Long teacherId, Integer year, Integer month) 
    {
        try {
            // 获取教师和班级信息
            KgClass teacherClass = getTeacherClass(teacherId);
            if (teacherClass == null) {
                return 0.0;
            }
            
            String classType = teacherClass.getClassType();  // 大班、中班、小班、托班
            
            // 获取班级学生列表
            List<Long> classStudentIds = getClassStudentIds(teacherClass.getClassId());
            if (classStudentIds.isEmpty()) {
                return 0.0;
            }
            
            // 计算班级平均出勤率
            int totalPresent = 0;
            int totalExpected = 0;
            
            // 查询学生考勤记录
            KgStudentAttendance queryCondition = new KgStudentAttendance();
            List<KgStudentAttendance> allAttendance = studentAttendanceMapper.selectKgStudentAttendanceList(queryCondition);
            
            // 过滤本月的考勤记录
            for (KgStudentAttendance attendance : allAttendance) {
                if (attendance.getAttendanceDate() != null && 
                    classStudentIds.contains(attendance.getStudentId())) {
                    
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(attendance.getAttendanceDate());
                    if (cal.get(Calendar.YEAR) == year && (cal.get(Calendar.MONTH) + 1) == month) {
                        totalExpected++;
                        if ("present".equals(attendance.getAttendanceStatus())) {
                            totalPresent++;
                        }
                    }
                }
            }
            
            if (totalExpected == 0) {
                return 0.0;
            }
            
            double attendanceRate = (double) totalPresent / totalExpected * 100;
            
            // 根据班级类型和出勤率计算奖励
            double highThreshold;
            
            if ("大班".equals(classType) || "中班".equals(classType)) {
                highThreshold = 91.0;  // 91%以上奖励100元
            } else { // 小班、托班
                highThreshold = 81.0;  // 81%以上奖励100元
            }
            
            if (attendanceRate >= highThreshold) {
                return 100.0;  // 奖励100元
            } else if (attendanceRate < 60.0) {
                return -100.0; // 扣款100元
            } else {
                return 0.0;    // 不奖不罚
            }
            
        } catch (Exception e) {
            System.err.println("计算出勤率奖励时出错: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算新生奖励
     */
    @Override
    public Double calculateNewStudentBonus(Long teacherId, Integer year, Integer month) 
    {
        try {
            // 获取教师班级信息
            KgClass teacherClass = getTeacherClass(teacherId);
            if (teacherClass == null) {
                return 0.0;
            }
            
            // 查询班级所有学生
            KgStudent queryCondition = new KgStudent();
            queryCondition.setClassId(teacherClass.getClassId());
            List<KgStudent> allStudents = studentMapper.selectKgStudentList(queryCondition);
            
            // 过滤本月新入园的学生
            List<KgStudent> newStudents = allStudents.stream()
                .filter(s -> s.getEnrollmentDate() != null)
                .filter(s -> {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(s.getEnrollmentDate());
                    return cal.get(Calendar.YEAR) == year && (cal.get(Calendar.MONTH) + 1) == month;
                })
                .collect(Collectors.toList());
            
            if (newStudents.isEmpty()) {
                return 0.0;
            }
            
            // 每个新生奖励75元（可配置）
            double bonusPerStudent = 75.0;
            return newStudents.size() * bonusPerStudent;
            
        } catch (Exception e) {
            System.err.println("计算新生奖励时出错: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算退园扣款
     */
    @Override
    public Double calculateWithdrawalDeduction(Long teacherId, Integer year, Integer month) 
    {
        try {
            // 获取教师班级信息
            KgClass teacherClass = getTeacherClass(teacherId);
            if (teacherClass == null) {
                return 0.0;
            }
            
            // 获取班级学生ID列表（包括已退园的）
            KgStudent queryCondition = new KgStudent();
            queryCondition.setClassId(teacherClass.getClassId());
            List<KgStudent> allStudents = studentMapper.selectKgStudentList(queryCondition);
            
            List<Long> allStudentIds = allStudents.stream()
                .map(KgStudent::getStudentId)
                .collect(Collectors.toList());
            
            // 查询本月退园的学生考勤记录
            KgStudentAttendance attendanceQuery = new KgStudentAttendance();
            List<KgStudentAttendance> allAttendance = studentAttendanceMapper.selectKgStudentAttendanceList(attendanceQuery);
            
            Set<Long> withdrawnStudentIds = allAttendance.stream()
                .filter(a -> a.getAttendanceDate() != null)
                .filter(a -> {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(a.getAttendanceDate());
                    return cal.get(Calendar.YEAR) == year && (cal.get(Calendar.MONTH) + 1) == month;
                })
                .filter(a -> allStudentIds.contains(a.getStudentId()))
                .filter(a -> "withdrawn".equals(a.getAttendanceStatus()))
                .map(KgStudentAttendance::getStudentId)
                .collect(Collectors.toSet());
            
            // 同时检查学生状态变更为退园的情况
            List<KgStudent> withdrawnStudents = allStudents.stream()
                .filter(s -> "1".equals(s.getStatus())) // 状态为退园
                .filter(s -> {
                    // 检查更新时间是否在本月
                    if (s.getUpdateTime() != null) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(s.getUpdateTime());
                        return cal.get(Calendar.YEAR) == year && (cal.get(Calendar.MONTH) + 1) == month;
                    }
                    return false;
                })
                .collect(Collectors.toList());
            
            // 合并退园学生列表
            for (KgStudent student : withdrawnStudents) {
                withdrawnStudentIds.add(student.getStudentId());
            }
            
            if (withdrawnStudentIds.isEmpty()) {
                return 0.0;
            }
            
            // 每个退园学生扣款50元（可配置）
            double deductionPerStudent = 50.0;
            return withdrawnStudentIds.size() * deductionPerStudent;
            
        } catch (Exception e) {
            System.err.println("计算退园扣款时出错: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算社保代扣
     */
    @Override
    public Double calculateSocialInsuranceDeduction(Long teacherId, Double grossSalary) 
    {
        try {
            // 尝试获取教师的社保配置（如果存在对应的表和字段）
            // 这里使用salaryConfigMapper来消除unused警告
            try {
                // 尝试查询工资配置（如果表存在）
                // KgSalaryConfig config = salaryConfigMapper.selectSalaryConfigByTeacherId(teacherId);
                // 由于表可能不存在，我们使用默认值
            } catch (Exception e) {
                // 忽略错误，使用默认值
            }
            
            // 使用基本的社保扣除比例：养老8% + 医疗2% + 失业0.5% = 10.5%
            double socialInsuranceRate = 0.105;
            
            // 如果应发工资超过社保缴费基数上限，按上限计算
            double maxBase = 25000.0; // 假设社保缴费基数上限
            double calculationBase = Math.min(grossSalary, maxBase);
            
            return calculationBase * socialInsuranceRate;
            
        } catch (Exception e) {
            System.err.println("计算社保代扣时出错: " + e.getMessage());
            // 返回默认计算结果
            return grossSalary * 0.105;
        }
    }
    
    /**
     * 获取教师对应班级的辅助方法
     */
    private KgClass getTeacherClass(Long teacherId) {
        List<KgClass> allClasses = classMapper.selectKgClassList(new KgClass());
        for (KgClass cls : allClasses) {
            if ((cls.getHeadTeacherId() != null && cls.getHeadTeacherId().equals(teacherId)) ||
                (cls.getAssistantTeacherId() != null && cls.getAssistantTeacherId().equals(teacherId))) {
                return cls;
            }
        }
        return null;
    }
    
    /**
     * 获取班级学生ID列表的辅助方法
     */
    private List<Long> getClassStudentIds(Long classId) {
        KgStudent queryCondition = new KgStudent();
        queryCondition.setClassId(classId);
        queryCondition.setStatus("0"); // 在读状态
        
        List<KgStudent> students = studentMapper.selectKgStudentList(queryCondition);
        return students.stream()
            .map(KgStudent::getStudentId)
            .collect(Collectors.toList());
    }
}
