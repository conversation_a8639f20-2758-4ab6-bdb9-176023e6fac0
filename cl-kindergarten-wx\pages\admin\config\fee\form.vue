<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">{{ isEdit ? '编辑' : '新增' }}费用配置</text>
				</view>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<view class="form-section">
				<view class="section-title">基本信息</view>
				
				<view class="form-item">
					<text class="label required">班级类型</text>
					<picker @change="onClassTypeChange" :value="classTypeIndex" :range="classTypeOptions">
						<view class="picker-value">
							{{ classTypeIndex >= 0 ? classTypeOptions[classTypeIndex] : '请选择班级类型' }}
							<u-icon name="arrow-down" size="14" color="#999"></u-icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="label required">每日餐费</text>
					<view class="input-with-unit">
						<input
							class="input"
							type="digit"
							v-model="form.mealFeePerDay"
							placeholder="请输入每日餐费"
							@blur="validateField('mealFeePerDay')"
						/>
						<text class="unit">元</text>
					</view>
				</view>

				<view class="form-item">
					<text class="label required">每月保教费</text>
					<view class="input-with-unit">
						<input
							class="input"
							type="digit"
							v-model="form.educationFeePerMonth"
							placeholder="请输入每月保教费"
							@blur="validateField('educationFeePerMonth')"
						/>
						<text class="unit">元</text>
					</view>
				</view>

				<view class="form-item">
					<text class="label required">出勤率阈值</text>
					<view class="input-with-unit">
						<input
							class="input"
							type="digit"
							v-model="attendanceThresholdPercent"
							placeholder="请输入出勤率阈值"
							@blur="validateField('attendanceThreshold')"
						/>
						<text class="unit">%</text>
					</view>
				</view>

				<view class="form-item">
					<text class="label required">半额保教费</text>
					<view class="input-with-unit">
						<input
							class="input"
							type="digit"
							v-model="form.halfEducationFee"
							placeholder="请输入半额保教费"
							@blur="validateField('halfEducationFee')"
						/>
						<text class="unit">元</text>
					</view>
				</view>
				
				<view class="form-item">
					<text class="label required">生效日期</text>
					<picker mode="date" @change="onDateChange" :value="form.effectiveDate">
						<view class="picker-value">
							{{ form.effectiveDate || '请选择生效日期' }}
							<u-icon name="arrow-down" size="14" color="#999"></u-icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="label">状态</text>
					<view class="radio-group">
						<label class="radio-item" @click="form.status = '0'">
							<radio :checked="form.status === '0'" color="#FF9800" />
							<text>正常</text>
						</label>
						<label class="radio-item" @click="form.status = '1'">
							<radio :checked="form.status === '1'" color="#FF9800" />
							<text>停用</text>
						</label>
					</view>
				</view>
				
				<view class="form-item">
					<text class="label">备注</text>
					<textarea 
						class="textarea" 
						v-model="form.remark" 
						placeholder="请输入备注信息"
						maxlength="200"
					></textarea>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<button class="btn-cancel" @click="goBack">取消</button>
			<button class="btn-submit" @click="handleSubmit" :disabled="submitting">
				{{ submitting ? '提交中...' : '保存' }}
			</button>
		</view>
	</view>
</template>

<script>
import {toast} from '@/utils/utils.js'
import {
	getTuitionConfig,
	addTuitionConfig,
	updateTuitionConfig
} from '@/api/tuitionConfig.js'

export default {
	data() {
		return {
			// 页面类型：add新增，edit编辑
			type: 'add',
			// 配置ID（编辑时使用）
			configId: null,
			// 是否编辑模式
			isEdit: false,
			// 表单数据
			form: {
				classType: '',
				mealFeePerDay: '',
				educationFeePerMonth: '',
				attendanceThreshold: '',
				halfEducationFee: '',
				effectiveDate: '',
				status: '0',
				remark: ''
			},
			// 出勤率阈值百分比显示
			attendanceThresholdPercent: '',
			// 班级类型选项
			classTypeOptions: ['托班', '小班', '中班', '大班'],
			classTypeIndex: -1,
			// 提交状态
			submitting: false,
			// 表单验证错误
			errors: {}
		}
	},
	
	onLoad(options) {
		this.type = options.type || 'add'
		this.isEdit = this.type === 'edit'
		this.configId = options.configId
		
		if (this.isEdit && this.configId) {
			this.getConfigDetail()
		}
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 获取配置详情（编辑时）
		async getConfigDetail() {
			try {
				const res = await getTuitionConfig(this.configId)
				if (res.code === 200) {
					this.form = { ...res.data }
					// 设置班级类型选择器
					const index = this.classTypeOptions.indexOf(this.form.classType)
					this.classTypeIndex = index >= 0 ? index : -1
					// 设置出勤率百分比显示
					this.attendanceThresholdPercent = this.form.attendanceThreshold ? 
						(this.form.attendanceThreshold * 100).toString() : ''
				} else {
					toast(res.msg || '获取配置详情失败')
					this.goBack()
				}
			} catch (error) {
				console.error('获取配置详情失败:', error)
				toast('获取配置详情失败')
				this.goBack()
			}
		},
		
		// 班级类型选择
		onClassTypeChange(e) {
			this.classTypeIndex = e.detail.value
			this.form.classType = this.classTypeOptions[this.classTypeIndex]
			this.validateField('classType')
		},
		
		// 日期选择
		onDateChange(e) {
			this.form.effectiveDate = e.detail.value
			this.validateField('effectiveDate')
		},
		
		// 字段验证
		validateField(field) {
			const errors = { ...this.errors }
			
			switch (field) {
				case 'classType':
					if (!this.form.classType) {
						errors.classType = '请选择班级类型'
					} else {
						delete errors.classType
					}
					break
				case 'mealFeePerDay':
					if (!this.form.mealFeePerDay) {
						errors.mealFeePerDay = '请输入每日餐费'
					} else if (isNaN(this.form.mealFeePerDay) || Number(this.form.mealFeePerDay) < 0) {
						errors.mealFeePerDay = '请输入有效的金额'
					} else {
						delete errors.mealFeePerDay
					}
					break
				case 'educationFeePerMonth':
					if (!this.form.educationFeePerMonth) {
						errors.educationFeePerMonth = '请输入每月保教费'
					} else if (isNaN(this.form.educationFeePerMonth) || Number(this.form.educationFeePerMonth) < 0) {
						errors.educationFeePerMonth = '请输入有效的金额'
					} else {
						delete errors.educationFeePerMonth
					}
					break
				case 'attendanceThreshold':
					if (!this.attendanceThresholdPercent) {
						errors.attendanceThreshold = '请输入出勤率阈值'
					} else if (isNaN(this.attendanceThresholdPercent) || 
							   Number(this.attendanceThresholdPercent) < 0 || 
							   Number(this.attendanceThresholdPercent) > 100) {
						errors.attendanceThreshold = '请输入0-100之间的数值'
					} else {
						this.form.attendanceThreshold = (Number(this.attendanceThresholdPercent) / 100).toString()
						delete errors.attendanceThreshold
					}
					break
				case 'halfEducationFee':
					if (!this.form.halfEducationFee) {
						errors.halfEducationFee = '请输入半额保教费'
					} else if (isNaN(this.form.halfEducationFee) || Number(this.form.halfEducationFee) < 0) {
						errors.halfEducationFee = '请输入有效的金额'
					} else {
						delete errors.halfEducationFee
					}
					break
				case 'effectiveDate':
					if (!this.form.effectiveDate) {
						errors.effectiveDate = '请选择生效日期'
					} else {
						delete errors.effectiveDate
					}
					break
			}
			
			this.errors = errors
		},
		
		// 表单验证
		validateForm() {
			// 验证所有必填字段
			this.validateField('classType')
			this.validateField('mealFeePerDay')
			this.validateField('educationFeePerMonth')
			this.validateField('attendanceThreshold')
			this.validateField('halfEducationFee')
			this.validateField('effectiveDate')
			
			// 检查是否有错误
			const errorKeys = Object.keys(this.errors)
			if (errorKeys.length > 0) {
				toast(this.errors[errorKeys[0]])
				return false
			}
			
			return true
		},
		
		// 提交表单
		async handleSubmit() {
			if (!this.validateForm()) {
				return
			}
			
			try {
				this.submitting = true
				
				const api = this.isEdit ? updateTuitionConfig : addTuitionConfig
				const res = await api(this.form)
				
				if (res.code === 200) {
					toast(this.isEdit ? '编辑成功' : '新增成功')
					// 延迟返回，让用户看到成功提示
					setTimeout(() => {
						this.goBack()
					}, 1500)
				} else {
					toast(res.msg || (this.isEdit ? '编辑失败' : '新增失败'))
				}
			} catch (error) {
				console.error('提交失败:', error)
				toast(this.isEdit ? '编辑失败' : '新增失败')
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 表单容器 */
.form-container {
	padding: 20rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.form-item {
	margin-bottom: 30rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.label {
	display: block;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;

	&.required::after {
		content: '*';
		color: #ff4d4f;
		margin-left: 4rpx;
	}
}

.input-with-unit {
	position: relative;
	display: flex;
	align-items: center;
}

.input {
	flex: 1;
	height: 80rpx;
	padding: 0 60rpx 0 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	box-sizing: border-box;

	&:focus {
		border-color: #FF9800;
		background: #ffffff;
	}
}

.textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	box-sizing: border-box;

	&:focus {
		border-color: #FF9800;
		background: #ffffff;
	}
}

.picker-value {
	height: 80rpx;
	padding: 0 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	border: 1rpx solid #e8e8e8;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.unit {
	position: absolute;
	right: 20rpx;
	font-size: 26rpx;
	color: #999999;
	pointer-events: none;
}

.radio-group {
	display: flex;
	gap: 40rpx;
}

.radio-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	font-size: 28rpx;
	color: #333333;
}

/* 底部按钮 */
.footer-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	display: flex;
	gap: 20rpx;
}

.btn-cancel, .btn-submit {
	flex: 1;
	height: 80rpx;
	border-radius: 8rpx;
	font-size: 32rpx;
	border: none;
}

.btn-cancel {
	background: #f8f9fa;
	color: #666666;
}

.btn-submit {
	background: #FF9800;
	color: #ffffff;

	&:disabled {
		background: #cccccc;
		color: #999999;
	}
}
</style>
