package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgCourseMapper;
import com.cl.project.business.domain.KgCourse;
import com.cl.project.business.service.IKgCourseService;

/**
 * 托管课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseServiceImpl implements IKgCourseService 
{
    @Autowired
    private KgCourseMapper kgCourseMapper;

    /**
     * 查询托管课程
     * 
     * @param courseId 托管课程ID
     * @return 托管课程
     */
    @Override
    public KgCourse selectKgCourseById(Long courseId)
    {
        return kgCourseMapper.selectKgCourseById(courseId);
    }

    /**
     * 查询托管课程列表
     * 
     * @param kgCourse 托管课程
     * @return 托管课程
     */
    @Override
    public List<KgCourse> selectKgCourseList(KgCourse kgCourse)
    {
        return kgCourseMapper.selectKgCourseList(kgCourse);
    }

    /**
     * 新增托管课程
     * 
     * @param kgCourse 托管课程
     * @return 结果
     */
    @Override
    public int insertKgCourse(KgCourse kgCourse)
    {
        kgCourse.setCreateTime(DateUtils.getNowDate());
        return kgCourseMapper.insertKgCourse(kgCourse);
    }

    /**
     * 修改托管课程
     * 
     * @param kgCourse 托管课程
     * @return 结果
     */
    @Override
    public int updateKgCourse(KgCourse kgCourse)
    {
        kgCourse.setUpdateTime(DateUtils.getNowDate());
        return kgCourseMapper.updateKgCourse(kgCourse);
    }

    /**
     * 批量删除托管课程
     * 
     * @param courseIds 需要删除的托管课程ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseByIds(Long[] courseIds)
    {
        return kgCourseMapper.deleteKgCourseByIds(courseIds);
    }

    /**
     * 删除托管课程信息
     * 
     * @param courseId 托管课程ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseById(Long courseId)
    {
        return kgCourseMapper.deleteKgCourseById(courseId);
    }

    @Override
    public List<KgCourse> selectCoursesWithSchedule() {
        return kgCourseMapper.selectCoursesWithSchedule();
    }

}
