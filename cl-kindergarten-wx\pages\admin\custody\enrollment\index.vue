<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">学生报名管理</text>
				</view>
			</view>
		</view>

		<!-- 搜索筛选栏 -->
		<view class="search-filter-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input
					class="search-input"
					type="text"
					placeholder="请输入学生姓名搜索"
					v-model="searchKeyword"
					@input="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>
			
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="{ active: statusFilter === 'all' }" 
					@click="setStatusFilter('all')"
				>
					<text class="tab-text">全部</text>
				</view>
				<view 
					class="filter-tab" 
					:class="{ active: statusFilter === 'active' }" 
					@click="setStatusFilter('active')"
				>
					<text class="tab-text">活跃</text>
				</view>
				<view 
					class="filter-tab" 
					:class="{ active: statusFilter === 'suspended' }" 
					@click="setStatusFilter('suspended')"
				>
					<text class="tab-text">暂停</text>
				</view>
				<view 
					class="filter-tab" 
					:class="{ active: statusFilter === 'completed' }" 
					@click="setStatusFilter('completed')"
				>
					<text class="tab-text">完成</text>
				</view>
			</view>
			
		</view>

		<!-- 调试信息 -->
		<view class="debug-info" style="background: #f0f0f0; padding: 20rpx; margin: 20rpx; display: none;">
			<text>Loading: {{ loading }}</text><br/>
			<text>EnrollmentList Length: {{ enrollmentList.length }}</text><br/>
			<text>Total: {{ total }}</text>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="!loading && enrollmentList.length === 0" class="empty-state">
			<view class="empty-icon">📚</view>
			<text class="empty-text">暂无报名记录</text>
			<view class="empty-action" @click="handleAdd">
				<text class="action-text">点击新增报名</text>
			</view>
		</view>

		<!-- 简单测试列表 -->
		<view style="background: white; margin: 20rpx; padding: 20rpx; display: none;">
			<text>测试数据显示:</text>
			<view v-for="(item, index) in enrollmentList" :key="index" style="border: 1px solid #ccc; margin: 10rpx; padding: 10rpx;">
				<text>学生: {{ item.studentName }}</text><br/>
				<text>课程: {{ item.courseName }}</text><br/>
				<text>状态: {{ item.status }}</text>
			</view>
		</view>

		<!-- 报名记录列表 - 简化条件 -->
		<!-- 使用内联样式测试 -->
		<view v-if="enrollmentList.length > 0" style="padding: 0 30rpx 150rpx;">
			<text style="padding: 20rpx; display: block;">报名列表 ({{ enrollmentList.length }} 条)</text>
			
			<view 
				v-for="item in enrollmentList" 
				:key="item.enrollmentId" 
				style="background: #ffffff; border-radius: 20rpx; margin-bottom: 20rpx; box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08); overflow: hidden;"
				@click="handleCardClick(item)"
			>
				<!-- 卡片头部 -->
				<view style="display: flex; align-items: center; justify-content: space-between; padding: 32rpx; border-bottom: 1rpx solid #f0f0f0;">
					<view style="display: flex; align-items: center; gap: 24rpx;">
						<view style="width: 80rpx; height: 80rpx; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center;">
							<text style="font-size: 32rpx; font-weight: 600; color: #ffffff;">{{ (item.studentName || 'N').charAt(0).toUpperCase() }}</text>
						</view>
						<view style="display: flex; flex-direction: column; gap: 8rpx;">
							<text style="font-size: 32rpx; font-weight: 600; color: #333333;">{{ item.studentName || '未知学生' }}</text>
							<text style="font-size: 24rpx; color: #999999;">{{ item.className || '暂无班级' }}</text>
						</view>
					</view>
					<view style="padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 24rpx; font-weight: 500; background: rgba(76, 175, 80, 0.1); color: #4caf50;">
						<text>{{ getStatusText(item.status) }}</text>
					</view>
				</view>

				<!-- 课程信息 -->
				<view style="padding: 0 32rpx 24rpx;">
					<view style="display: flex; align-items: center; gap: 16rpx; margin-bottom: 12rpx;">
						<text style="font-size: 30rpx; font-weight: 600; color: #333333;">{{ item.courseName || '未知课程' }}</text>
						<text style="font-size: 24rpx; color: #667eea; background: rgba(102, 126, 234, 0.1); padding: 4rpx 12rpx; border-radius: 12rpx;">{{ getCourseTypeText(item.courseType) }}</text>
					</view>
					<text style="font-size: 26rpx; color: #999999;">报名时间：{{ formatDate(item.enrollmentDate) }}</text>
				</view>

				<!-- 课时进度 -->
				<view style="padding: 0 32rpx 24rpx;">
					<view style="display: flex; justify-content: space-between; margin-bottom: 12rpx;">
						<text style="font-size: 28rpx; font-weight: 500; color: #333333;">课时进度</text>
						<text style="font-size: 28rpx; font-weight: 600; color: #667eea;">{{ item.usedSessions || 0 }}/{{ item.totalSessions || 0 }}节</text>
					</view>
					<view style="height: 8rpx; background: #f0f0f0; border-radius: 4rpx; overflow: hidden; margin-bottom: 12rpx;">
						<view style="height: 100%; border-radius: 4rpx; background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);" :style="{ width: getUsagePercentage(item) + '%' }"></view>
					</view>
					<view style="display: flex; justify-content: space-between;">
						<text style="font-size: 24rpx; color: #666666;">剩余：{{ item.remainingSessions || 0 }}节</text>
						<text v-if="(item.giftSessions || 0) > 0" style="font-size: 24rpx; color: #4caf50;">🎁 {{ item.giftSessions }}节</text>
					</view>
				</view>

				<!-- 费用信息 -->
				<view style="display: flex; justify-content: space-between; background: #f8f9fa; margin: 0 32rpx; border-radius: 12rpx; padding: 20rpx 24rpx;">
					<view style="display: flex; flex-direction: column; align-items: center; gap: 8rpx;">
						<text style="font-size: 24rpx; color: #666666;">总费用：</text>
						<text style="font-size: 28rpx; font-weight: 600; color: #333333;">￥{{ item.totalAmount || 0 }}</text>
					</view>
					<view style="display: flex; flex-direction: column; align-items: center; gap: 8rpx;">
						<text style="font-size: 24rpx; color: #666666;">已付：</text>
						<text style="font-size: 28rpx; font-weight: 600; color: #4caf50;">￥{{ item.paidAmount || 0 }}</text>
					</view>
					<view style="display: flex; flex-direction: column; align-items: center; gap: 8rpx;">
						<text style="font-size: 24rpx; color: #666666;">待付：</text>
						<text style="font-size: 28rpx; font-weight: 600; color: #f44336;">￥{{ getRemainingAmount(item) }}</text>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view style="display: flex; gap: 16rpx; padding: 24rpx 32rpx; border-top: 1rpx solid #f0f0f0;">
					<view style="flex: 1; padding: 16rpx 24rpx; border-radius: 12rpx; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2rpx solid #e9ecef; color: #667eea;" @click.stop="handleAttendance(item)">
						<text style="font-size: 26rpx; font-weight: 500;">考勤记录</text>
					</view>
					<view style="flex: 1; padding: 16rpx 24rpx; border-radius: 12rpx; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff;" @click.stop="handleEdit(item)">
						<text style="font-size: 26rpx; font-weight: 500;">编辑</text>
					</view>
					<view style="flex: 1; padding: 16rpx 24rpx; border-radius: 12rpx; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #f44336 0%, #ef5350 100%); color: #ffffff;" @click.stop="handleDelete(item)">
						<text style="font-size: 26rpx; font-weight: 500;">删除</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动新增按钮 -->
		<view class="floating-add-btn" @click="handleAdd">
			<u-icon name="plus" color="#ffffff" size="24"></u-icon>
		</view>

		<!-- 报名表单弹窗 -->
		<view v-if="showEnrollmentDialog" class="dialog-overlay" @click="closeEnrollmentDialog">
			<view class="dialog-container enrollment-dialog" @click.stop>
				<view class="dialog-header">
					<text class="dialog-title">{{ isEdit ? '编辑报名' : '新增报名' }}</text>
					<view class="dialog-close" @click="closeEnrollmentDialog">
						<u-icon name="close" color="#999999" size="20"></u-icon>
					</view>
				</view>
				
				<view class="dialog-content">
					<!-- 学生选择 -->
					<view class="form-item">
						<text class="form-label required">选择学生</text>
						<view class="form-control">
							<picker 
								:range="studentPickerOptions" 
								:value="selectedStudentIndex"
								@change="handleStudentChange"
								class="student-picker"
							>
								<view class="picker-display">
									<text class="picker-text">
										{{ selectedStudentIndex >= 0 ? studentPickerOptions[selectedStudentIndex] : '请选择学生' }}
									</text>
									<u-icon name="arrow-down" color="#999999" size="14"></u-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- 课程选择 -->
					<view class="form-item">
						<text class="form-label required">选择课程</text>
						<view class="form-control">
							<picker 
								:range="coursePickerOptions" 
								:value="selectedCourseIndex"
								@change="handleCourseChange"
								class="course-picker"
							>
								<view class="picker-display">
									<text class="picker-text">
										{{ selectedCourseIndex >= 0 ? coursePickerOptions[selectedCourseIndex] : '请选择课程' }}
									</text>
									<u-icon name="arrow-down" color="#999999" size="14"></u-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- 课程信息显示 -->
					<view v-if="enrollmentForm.courseId" class="course-info-card">
						<view class="info-row">
							<text class="info-label">课程名称:</text>
							<text class="info-value">{{ selectedCourse.courseName }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">单节价格:</text>
							<text class="info-value">￥{{ selectedCourse.pricePerSession }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">课程时长:</text>
							<text class="info-value">{{ selectedCourse.duration }}分钟</text>
						</view>
						<view v-if="selectedCourse.defaultTeacherName" class="info-row">
							<text class="info-label">授课老师:</text>
							<text class="info-value">{{ selectedCourse.defaultTeacherName }}</text>
						</view>
					</view>

					<!-- 总课时数 -->
					<view class="form-item">
						<text class="form-label required">总课时数</text>
						<view class="form-control">
							<view class="number-input">
								<view class="number-btn" @click="decreaseNumber('totalSessions')">
									<text>-</text>
								</view>
								<input 
									class="number-value" 
									type="number" 
									v-model.number="enrollmentForm.totalSessions"
									@input="calculateAmount"
								/>
								<view class="number-btn" @click="increaseNumber('totalSessions')">
									<text>+</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 赠送课时 -->
					<view class="form-item">
						<text class="form-label">赠送课时</text>
						<view class="form-control">
							<view class="number-input">
								<view class="number-btn" @click="decreaseNumber('giftSessions')">
									<text>-</text>
								</view>
								<input 
									class="number-value" 
									type="number" 
									v-model.number="enrollmentForm.giftSessions"
								/>
								<view class="number-btn" @click="increaseNumber('giftSessions')">
									<text>+</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 费用信息 -->
					<view class="payment-form-section">
						<view class="form-item">
							<text class="form-label">总金额</text>
							<view class="form-control">
								<view class="amount-display">
									<text class="amount-text">￥{{ enrollmentForm.totalAmount }}</text>
								</view>
							</view>
						</view>

						<view class="form-item">
							<text class="form-label">已付金额</text>
							<view class="form-control">
								<input 
									class="amount-input" 
									type="digit" 
									v-model.number="enrollmentForm.paidAmount"
									placeholder="请输入已付金额"
								/>
							</view>
						</view>
					</view>

					<!-- 状态选择 -->
					<view class="form-item">
						<text class="form-label">报名状态</text>
						<view class="form-control">
							<picker 
								:range="statusPickerOptions" 
								:value="selectedStatusIndex"
								@change="handleStatusChange"
								class="status-picker"
							>
								<view class="picker-display">
									<text class="picker-text">
										{{ statusPickerOptions[selectedStatusIndex] }}
									</text>
									<u-icon name="arrow-down" color="#999999" size="14"></u-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- 备注 -->
					<view class="form-item">
						<text class="form-label">备注信息</text>
						<view class="form-control">
							<textarea 
								v-model="enrollmentForm.remark"
								placeholder="请输入备注信息"
								class="remark-textarea"
								maxlength="200"
							></textarea>
						</view>
					</view>
				</view>

				<view class="dialog-footer">
					<view class="dialog-btn cancel" @click="closeEnrollmentDialog">
						<text class="btn-text">取消</text>
					</view>
					<view class="dialog-btn confirm" @click="submitEnrollment">
						<text class="btn-text">{{ isEdit ? '保存' : '新增' }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 考勤记录弹窗 -->
	<view v-if="showAttendanceDialog" class="dialog-overlay" @click="closeAttendanceDialog">
		<view class="attendance-dialog" @click.stop>
			<view class="dialog-header">
				<text class="dialog-title">考勤记录</text>
				<view class="close-btn" @click="closeAttendanceDialog">
					<u-icon name="close" size="20" color="#999"></u-icon>
				</view>
			</view>

			<!-- 学生信息 -->
			<view v-if="selectedStudentForAttendance" class="student-info">
				<view class="info-row">
					<text class="info-label">学生姓名：</text>
					<text class="info-value">{{ selectedStudentForAttendance.studentName }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">班级：</text>
					<text class="info-value">{{ selectedStudentForAttendance.className || '-' }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">课程名称：</text>
					<text class="info-value">{{ selectedStudentForAttendance.courseName }}</text>
				</view>
			</view>

			<!-- 考勤记录列表 -->
			<view class="attendance-content">
				<view v-if="attendanceLoading" class="loading-state">
					<text>加载中...</text>
				</view>

				<view v-else-if="attendanceList.length === 0" class="empty-state">
					<text class="empty-icon">📅</text>
					<text class="empty-text">暂无考勤记录</text>
				</view>

				<view v-else class="attendance-list">
					<view v-for="(record, index) in attendanceList" :key="index" class="attendance-item">
						<view class="attendance-header">
							<text class="attendance-date">{{ record.attendanceDate }}</text>
							<view class="status-badge" :class="getAttendanceStatusType(record.attendanceStatus)">
								<text class="status-text">{{ getAttendanceStatusText(record.attendanceStatus) }}</text>
							</view>
						</view>

						<view class="attendance-details">
							<view class="detail-row">
								<text class="detail-label">签到方式：</text>
								<text class="detail-value">
									<text v-if="record.checkInMethod === 'face'">😊 人脸</text>
									<text v-else-if="record.checkInMethod === 'manual'">✋ 手动</text>
									<text v-else>-</text>
								</text>
							</view>
							<view class="detail-row">
								<text class="detail-label">确认状态：</text>
								<view class="confirm-badge" :class="record.isConfirmed ? 'confirmed' : 'unconfirmed'">
									<text class="confirm-text">{{ record.isConfirmed ? '已确认' : '未确认' }}</text>
								</view>
							</view>
							<view v-if="record.remark" class="detail-row">
								<text class="detail-label">备注：</text>
								<text class="detail-value remark">{{ record.remark }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="dialog-footer">
				<view class="dialog-btn cancel" @click="closeAttendanceDialog">
					<text class="btn-text">关闭</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import {
	listEnrollment,
	getEnrollment,
	addEnrollment,
	updateEnrollment,
	delEnrollment,
	listAllStudent,
	listAllCourse
} from '@/api/enrollment.js'
import {
	listCourseAttendance
} from '@/api/custodyAttendance.js'

export default {
	data() {
		return {
			loading: false,
			searchKeyword: '',
			statusFilter: 'all',
			
			// 报名记录列表
			enrollmentList: [],
			
			// 选项数据
			studentOptions: [],
			courseOptions: [],
			
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 20,
				studentName: null,
				courseId: null,
				status: null
			},
			
			// 表单弹窗
			showEnrollmentDialog: false,
			isEdit: false,
			enrollmentForm: {
				enrollmentId: null,
				studentId: null,
				courseId: null,
				classId: null,
				enrollmentDate: null,
				totalSessions: 1,
				usedSessions: 0,
				remainingSessions: 0,
				giftSessions: 0,
				totalAmount: 0,
				paidAmount: 0,
				status: "active",
				remark: null
			},
			
			// 选择器相关
			selectedStudentIndex: -1,
			selectedCourseIndex: -1,
			selectedStatusIndex: 0,
			selectedCourse: null,
			selectedStudent: null,
			
			total: 0,

			// 搜索防抖定时器
			searchTimer: null,

			// 考勤记录弹窗
			showAttendanceDialog: false,
			attendanceLoading: false,
			attendanceList: [],
			selectedStudentForAttendance: null
		}
	},

	computed: {
		// 过滤后的报名列表
		filteredEnrollmentList() {
			// 直接返回enrollmentList，所有筛选通过API实现
			return this.enrollmentList || [];
		},
		
		// 学生选择器选项
		studentPickerOptions() {
			return (this.studentOptions || []).map(item => 
				`${item.studentName || ''} (${item.className || '暂无班级'})`
			);
		},
		
		// 课程选择器选项
		coursePickerOptions() {
			return (this.courseOptions || []).map(item => 
				`${item.courseName || ''} (￥${item.pricePerSession || 0}/节)`
			);
		},
		
		// 状态选择器选项
		statusPickerOptions() {
			return ['活跃', '暂停', '完成', '取消'];
		}
	},

	onLoad() {
		console.log('enrollment页面onLoad被调用');
		this.getList();
		this.loadOptions();
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.queryParams.pageNum = 1;
		this.getList().finally(() => {
			uni.stopPullDownRefresh();
		});
	},

	// 上拉加载更多
	onReachBottom() {
		if (this.enrollmentList.length < this.total && !this.loading) {
			this.queryParams.pageNum++;
			this.loadMore();
		}
	},

	methods: {
		/** 获取报名列表 */
		async getList() {
			try {
				this.loading = true;
				
				// 简化查询参数，与web端保持一致
				const params = {
					pageNum: this.queryParams.pageNum,
					pageSize: this.queryParams.pageSize
				};
				
				// 只有在有值时才添加筛选参数
				if (this.queryParams.studentName) {
					params.studentName = this.queryParams.studentName;
				}
				if (this.queryParams.courseId) {
					params.courseId = this.queryParams.courseId;
				}
				if (this.queryParams.status) {
					params.status = this.queryParams.status;
				}
				
				const response = await listEnrollment(params);
				
				if (response && response.code === 200) {
					// 使用真实接口数据
					const newList = response.rows || [];
					this.enrollmentList = newList;
					this.total = response.total || 0;
					console.log('获取到报名列表:', this.enrollmentList.length, '条记录');
					console.log('enrollmentList数据:', this.enrollmentList);
					// 验证数据结构
					if (this.enrollmentList.length > 0) {
						console.log('第一条记录样例:', this.enrollmentList[0]);
					}
				} else {
					console.error('API响应错误:', response);
					toast(response?.msg || '获取数据失败');
					this.enrollmentList = [];
					this.total = 0;
				}
				
			} catch (error) {
				console.error('获取报名列表失败:', error);
				toast('获取报名列表失败');
				// 确保列表为空数组，避免渲染错误
				this.enrollmentList = [];
				this.total = 0;
			} finally {
				this.loading = false;
			}
		},

		/** 加载更多数据 */
		async loadMore() {
			try {
				// 调用API获取更多报名列表
				const response = await listEnrollment(this.queryParams);
				
				if (response && response.code === 200) {
					// 直接使用接口返回的数据
					const newData = response.rows || [];
					this.enrollmentList = this.enrollmentList.concat(newData);
				} else {
					toast(response?.msg || '加载更多失败');
					// 恢复页码
					this.queryParams.pageNum--;
				}
			} catch (error) {
				console.error('加载更多失败:', error);
				toast('加载更多失败，请重试');
				// 恢复页码
				this.queryParams.pageNum--;
			}
		},

		/** 加载选项数据 */
		async loadOptions() {
			try {
				// 加载真实数据
				const [studentResponse, courseResponse] = await Promise.all([
					listAllStudent(),
					listAllCourse()
				]);
				
				// 设置学生选项数据，处理不同的响应结构
				if (studentResponse && studentResponse.code === 200) {
					const studentData = studentResponse.data || studentResponse.rows || [];
					this.studentOptions = studentData.map(item => ({
						studentId: item.studentId,
						studentName: item.studentName,
						classId: item.classId,
						className: item.className || '暂无班级'
					}));
				}
				
				// 设置课程选项数据，处理不同的响应结构
				if (courseResponse && courseResponse.code === 200) {
					const courseData = courseResponse.data || courseResponse.rows || [];
					this.courseOptions = courseData.map(item => ({
						courseId: item.courseId,
						courseName: item.courseName,
						courseType: item.courseType || 'regular',
						pricePerSession: parseFloat(item.pricePerSession || 0),
						duration: item.duration || 0,
						defaultTeacherName: item.defaultTeacherName || ''
					}));
				}
			} catch (error) {
				toast('加载选项数据失败');
				// 确保总是有初始化的空数组
				this.studentOptions = this.studentOptions || [];
				this.courseOptions = this.courseOptions || [];
			}
		},

		/** 搜索处理 */
		handleSearch() {
			// 防抖处理
			clearTimeout(this.searchTimer);
			this.searchTimer = setTimeout(() => {
				this.queryParams.studentName = this.searchKeyword || null;
				this.queryParams.pageNum = 1;
				this.getList();
			}, 300);
		},

		/** 清除搜索 */
		clearSearch() {
			this.searchKeyword = '';
			this.queryParams.studentName = null;
			this.queryParams.pageNum = 1;
			this.getList();
		},

		/** 设置状态过滤 */
		setStatusFilter(status) {
			this.statusFilter = status;
			this.queryParams.status = status === 'all' ? null : status;
			this.queryParams.pageNum = 1;
			this.getList();
		},

		/** 卡片点击 */
		handleCardClick(item) {
			// 可以跳转到详情页或显示操作菜单
		},

		/** 新增报名 */
		handleAdd() {
			this.isEdit = false;
			this.resetEnrollmentForm();
			this.showEnrollmentDialog = true;
		},

		/** 编辑报名 */
		async handleEdit(item) {
			try {
				this.isEdit = true;
				uni.showLoading({ title: '加载中...' });
				
				// 调用API获取详细信息
				const response = await getEnrollment(item.enrollmentId);
				
				if (response.code === 200) {
					this.loadEnrollmentForm(response.data || item);
				} else {
					// 如果API调用失败，直接使用列表中的数据
					this.loadEnrollmentForm(item);
				}
				
				this.showEnrollmentDialog = true;
			} catch (error) {
				console.error('获取报名详情失败:', error);
				// 如果网络错误，直接使用列表中的数据
				this.loadEnrollmentForm(item);
				this.showEnrollmentDialog = true;
			} finally {
				uni.hideLoading();
			}
		},

		/** 考勤记录 */
		handleAttendance(item) {
			this.selectedStudentForAttendance = item;
			this.showAttendanceDialog = true;
			this.loadAttendanceRecords(item.enrollmentId);
		},

		/** 删除报名 */
		handleDelete(item) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除${item.studentName}的报名记录吗？`,
				confirmText: '删除',
				confirmColor: '#f44336',
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '删除中...' });
							
							const response = await delEnrollment(item.enrollmentId);
							
							if (response.code === 200) {
								toast('删除成功');
								this.getList();
							} else {
								toast(response.msg || '删除失败');
							}
						} catch (error) {
							console.error('删除报名失败:', error);
							toast('删除失败，请重试');
						} finally {
							uni.hideLoading();
						}
					}
				}
			});
		},

		/** 重置报名表单 */
		resetEnrollmentForm() {
			this.enrollmentForm = {
				enrollmentId: null,
				studentId: null,
				courseId: null,
				classId: null,
				enrollmentDate: new Date().toISOString(),
				totalSessions: 1,
				usedSessions: 0,
				remainingSessions: 1,
				giftSessions: 0,
				totalAmount: 0,
				paidAmount: 0,
				status: "active",
				remark: null
			};
			this.selectedStudentIndex = -1;
			this.selectedCourseIndex = -1;
			this.selectedStatusIndex = 0;
			this.selectedCourse = null;
			this.selectedStudent = null;
		},

		/** 加载报名表单 */
		loadEnrollmentForm(item) {
			this.enrollmentForm = { ...item };
			
			// 设置选择器索引
			this.selectedStudentIndex = this.studentOptions.findIndex(s => s.studentId === item.studentId);
			this.selectedCourseIndex = this.courseOptions.findIndex(c => c.courseId === item.courseId);
			
			const statusMap = ['active', 'suspended', 'completed', 'cancelled'];
			this.selectedStatusIndex = statusMap.indexOf(item.status);
			
			// 设置选中的课程和学生
			this.selectedCourse = this.courseOptions.find(c => c.courseId === item.courseId);
			this.selectedStudent = this.studentOptions.find(s => s.studentId === item.studentId);
		},

		/** 关闭报名表单弹窗 */
		closeEnrollmentDialog() {
			this.showEnrollmentDialog = false;
		},

		/** 处理学生选择变化 */
		handleStudentChange(e) {
			const index = e.detail.value;
			this.selectedStudentIndex = index;
			
			if (index >= 0 && index < this.studentOptions.length) {
				const student = this.studentOptions[index];
				this.selectedStudent = student;
				this.enrollmentForm.studentId = student.studentId;
				this.enrollmentForm.classId = student.classId;
			}
		},

		/** 处理课程选择变化 */
		handleCourseChange(e) {
			const index = e.detail.value;
			this.selectedCourseIndex = index;
			
			if (index >= 0 && index < this.courseOptions.length) {
				const course = this.courseOptions[index];
				this.selectedCourse = course;
				this.enrollmentForm.courseId = course.courseId;
				this.calculateAmount();
			}
		},

		/** 处理状态选择变化 */
		handleStatusChange(e) {
			const index = e.detail.value;
			this.selectedStatusIndex = index;
			
			const statusMap = ['active', 'suspended', 'completed', 'cancelled'];
			this.enrollmentForm.status = statusMap[index];
		},

		/** 计算金额 */
		calculateAmount() {
			if (this.selectedCourse && this.enrollmentForm.totalSessions) {
				this.enrollmentForm.totalAmount = (this.selectedCourse.pricePerSession * this.enrollmentForm.totalSessions).toFixed(2);
				this.enrollmentForm.remainingSessions = this.enrollmentForm.totalSessions - this.enrollmentForm.usedSessions;
			}
		},

		/** 数字输入增加 */
		increaseNumber(field) {
			if (field === 'totalSessions' && this.enrollmentForm[field] < 200) {
				this.enrollmentForm[field]++;
			} else if (field === 'giftSessions' && this.enrollmentForm[field] < 50) {
				this.enrollmentForm[field]++;
			}
			if (field === 'totalSessions') {
				this.calculateAmount();
			}
		},

		/** 数字输入减少 */
		decreaseNumber(field) {
			const minValue = field === 'totalSessions' ? 1 : 0;
			if (this.enrollmentForm[field] > minValue) {
				this.enrollmentForm[field]--;
			}
			if (field === 'totalSessions') {
				this.calculateAmount();
			}
		},

		/** 提交报名 */
		async submitEnrollment() {
			// 表单验证
			if (!this.enrollmentForm.studentId) {
				toast('请选择学生');
				return;
			}
			if (!this.enrollmentForm.courseId) {
				toast('请选择课程');
				return;
			}
			if (!this.enrollmentForm.totalSessions || this.enrollmentForm.totalSessions < 1) {
				toast('总课时数不能为空且不能小于1');
				return;
			}

			try {
				uni.showLoading({ title: this.isEdit ? '保存中...' : '新增中...' });
				
				// 准备提交数据 - 只传递必要字段，参考web端
				const submitData = {
					enrollmentId: this.enrollmentForm.enrollmentId,
					studentId: this.enrollmentForm.studentId,
					courseId: this.enrollmentForm.courseId,
					enrollmentDate: this.enrollmentForm.enrollmentDate,
					totalSessions: this.enrollmentForm.totalSessions,
					giftSessions: this.enrollmentForm.giftSessions || 0,
					totalAmount: this.enrollmentForm.totalAmount,
					paidAmount: this.enrollmentForm.paidAmount || 0,
					status: this.enrollmentForm.status,
					remark: this.enrollmentForm.remark
				};
				
				let response;
				if (this.isEdit) {
					response = await updateEnrollment(submitData);
				} else {
					response = await addEnrollment(submitData);
				}
				
				if (response.code === 200) {
					toast(this.isEdit ? '保存成功' : '新增成功');
					this.closeEnrollmentDialog();
					this.getList();
				} else {
					toast(response.msg || '提交失败');
				}
			} catch (error) {
				console.error('提交失败:', error);
				toast('提交失败，请重试');
			} finally {
				uni.hideLoading();
			}
		},

		/** 返回上一页 */
		goBack() {
			uni.navigateBack();
		},

		/** 获取使用百分比 */
		getUsagePercentage(item) {
			if (!item || item.totalSessions === 0 || !item.totalSessions) return 0;
			return Math.round((item.usedSessions / item.totalSessions) * 100);
		},

		/** 获取进度状态 */
		getProgressStatus(item) {
			const percentage = this.getUsagePercentage(item);
			if (percentage >= 90) return 'danger';
			if (percentage >= 70) return 'warning';
			return 'success';
		},

		/** 获取状态文本 */
		getStatusText(status) {
			const statusMap = {
				'active': '活跃',
				'suspended': '暂停',
				'completed': '完成',
				'cancelled': '取消'
			};
			return statusMap[status] || '未知';
		},

		/** 获取课程类型文本 */
		getCourseTypeText(courseType) {
			// 处理数字和字符串两种格式
			const typeMap = {
				// 字符串格式
				'regular': '常规课程',
				'special': '特色课程',
				'trial': '试听课程',
				// 数字格式（兼容后端返回）
				'1': '常规课程',
				'2': '特色课程', 
				'3': '试听课程',
				1: '常规课程',
				2: '特色课程',
				3: '试听课程'
			};
			return typeMap[courseType] || '常规课程';
		},

		/** 格式化日期 */
		formatDate(dateString) {
			if (!dateString) return '暂无';
			try {
				const date = new Date(dateString);
				if (isNaN(date.getTime())) return '暂无';
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			} catch (error) {
				return '暂无';
			}
		},

		/** 计算剩余金额 */
		getRemainingAmount(item) {
			const total = parseFloat(item.totalAmount || 0);
			const paid = parseFloat(item.paidAmount || 0);
			return (total - paid).toFixed(2);
		},

		/** 加载考勤记录 */
		async loadAttendanceRecords(enrollmentId) {
			try {
				this.attendanceLoading = true;
				const queryParams = {
					enrollmentId: enrollmentId,
					studentId: this.selectedStudentForAttendance.studentId,
					courseId: this.selectedStudentForAttendance.courseId
				};

				const response = await listCourseAttendance(queryParams);
				if (response.code === 200) {
					this.attendanceList = response.rows || response.data || [];
				} else {
					this.attendanceList = [];
					toast(response.msg || '获取考勤记录失败');
				}
			} catch (error) {
				console.error('获取考勤记录失败:', error);
				this.attendanceList = [];
				toast('获取考勤记录失败，请重试');
			} finally {
				this.attendanceLoading = false;
			}
		},

		/** 获取考勤状态类型 */
		getAttendanceStatusType(status) {
			const statusMap = {
				'present': 'success',
				'absent': 'danger',
				'late': 'warning',
				'early': 'info'
			};
			return statusMap[status] || 'info';
		},

		/** 获取考勤状态文本 */
		getAttendanceStatusText(status) {
			const statusMap = {
				'present': '出勤',
				'absent': '缺勤',
				'late': '迟到',
				'early': '早退'
			};
			return statusMap[status] || '未知';
		},

		/** 关闭考勤弹窗 */
		closeAttendanceDialog() {
			this.showAttendanceDialog = false;
			this.selectedStudentForAttendance = null;
			this.attendanceList = [];
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	position: relative;
}

.nav-left {
	position: absolute;
	left: 32rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

.header-title {
	flex: 1;
	display: flex;
	justify-content: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 搜索筛选栏 */
.search-filter-section {
	background: #ffffff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.search-box {
	background: #f8f9fa;
	border-radius: 24rpx;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.search-icon {
	font-size: 32rpx;
	color: #999999;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	border: none;
	outline: none;
	background: transparent;
}

.clear-icon {
	font-size: 32rpx;
	color: #999999;
	margin-left: 16rpx;
	cursor: pointer;
}

/* 筛选标签 */
.filter-tabs {
	display: flex;
	gap: 16rpx;
	overflow-x: auto;
}

.filter-tab {
	flex-shrink: 0;
	padding: 16rpx 32rpx;
	background: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;

	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-color: #667eea;
		transform: scale(1.05);
	}

	&:active {
		transform: scale(0.95);
	}
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;

	.filter-tab.active & {
		color: #ffffff;
	}
}


/* 加载状态 */
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #999999;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	gap: 32rpx;
}

.empty-icon {
	font-size: 120rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #999999;
	text-align: center;
}

.empty-action {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 40rpx;
	color: #ffffff;
}

/* 报名记录列表 */
.enrollment-list {
	padding: 0 30rpx 150rpx;
}

.enrollment-card {
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
	}
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.student-info {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.student-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.student-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.class-name {
	font-size: 24rpx;
	color: #999999;
}

.enrollment-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;

	&.active {
		background: rgba(76, 175, 80, 0.1);
		color: #4caf50;
	}

	&.suspended {
		background: rgba(255, 152, 0, 0.1);
		color: #ff9800;
	}

	&.completed {
		background: rgba(96, 125, 139, 0.1);
		color: #607d8b;
	}

	&.cancelled {
		background: rgba(244, 67, 54, 0.1);
		color: #f44336;
	}
}

/* 课程信息 */
.course-info {
	padding: 0 32rpx 24rpx;
}

.course-title {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.course-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333333;
}

.course-type {
	font-size: 24rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.enrollment-date {
	font-size: 26rpx;
	color: #999999;
}

/* 课时进度 */
.sessions-progress {
	padding: 0 32rpx 24rpx;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.progress-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
}

.progress-stats {
	font-size: 28rpx;
	font-weight: 600;
	color: #667eea;
}

.progress-bar {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
	margin-bottom: 12rpx;
}

.progress-fill {
	height: 100%;
	border-radius: 4rpx;
	transition: all 0.3s ease;

	&.success {
		background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
	}

	&.warning {
		background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%);
	}

	&.danger {
		background: linear-gradient(90deg, #f44336 0%, #ef5350 100%);
	}
}

.sessions-detail {
	display: flex;
	justify-content: space-between;
}

.remaining {
	font-size: 24rpx;
	color: #666666;
}

.gift {
	font-size: 24rpx;
	color: #4caf50;
}

/* 费用信息 */
.payment-info {
	display: flex;
	justify-content: space-between;
	padding: 0 32rpx 24rpx;
	background: #f8f9fa;
	margin: 0 32rpx;
	border-radius: 12rpx;
	padding: 20rpx 24rpx;
}

.payment-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.payment-label {
	font-size: 24rpx;
	color: #666666;
}

.payment-amount {
	font-size: 28rpx;
	font-weight: 600;

	&.total {
		color: #333333;
	}

	&.paid {
		color: #4caf50;
	}

	&.unpaid {
		color: #f44336;
	}
}

/* 操作按钮 */
.card-actions {
	display: flex;
	gap: 16rpx;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
}

.action-btn {
	flex: 1;
	padding: 16rpx 24rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	font-size: 26rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&.primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
	}

	&.secondary {
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		color: #667eea;
	}

	&.danger {
		background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
		color: #ffffff;
	}
}

.btn-text {
	font-size: 26rpx;
	font-weight: 500;
}

/* 浮动新增按钮 */
.floating-add-btn {
	position: fixed;
	bottom: 120rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s ease;
	z-index: 1000;

	&:active {
		transform: scale(0.9);
	}
}

/* 报名表单弹窗样式 */
.dialog-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
	padding: 40rpx;
	backdrop-filter: blur(4rpx);
}

.enrollment-dialog {
	max-height: 90vh;
	width: 100%;
	max-width: 680rpx;
}

.dialog-container {
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	animation: dialogScale 0.3s ease-out;
}

@keyframes dialogScale {
	0% {
		opacity: 0;
		transform: scale(0.8) translateY(40rpx);
	}
	100% {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.dialog-header {
	padding: 48rpx 48rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f2f5;
	background: linear-gradient(135deg, #f8f9fb 0%, #ffffff 100%);
}

.dialog-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #1a1a1a;
	display: flex;
	align-items: center;
	
	&::before {
		content: '';
		width: 6rpx;
		height: 36rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 3rpx;
		margin-right: 16rpx;
	}
}

.dialog-close {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background: #f5f5f7;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	
	&:active {
		background: #e5e5e7;
		transform: scale(0.9);
	}
}

.dialog-content {
	padding: 40rpx 48rpx;
	max-height: 70vh;
	overflow-y: auto;
}

/* 表单样式 */
.form-item {
	margin-bottom: 48rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	display: block;
	font-size: 30rpx;
	font-weight: 600;
	color: #1a1a1a;
	margin-bottom: 20rpx;
	
	&.required::after {
		content: '*';
		color: #ff4757;
		margin-left: 8rpx;
	}
}

.form-control {
	position: relative;
}

.picker-display {
	background: #f8f9fb;
	border: 2rpx solid #e4e6ea;
	border-radius: 16rpx;
	padding: 28rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.3s ease;
	
	&:active {
		background: #f0f2f6;
		border-color: #667eea;
	}
}

.picker-text {
	font-size: 30rpx;
	color: #1a1a1a;
	flex: 1;
	font-weight: 500;
}

/* 课程信息卡片 */
.course-info-card {
	background: linear-gradient(135deg, #f8f9fb 0%, #e9ecf3 100%);
	border: 2rpx solid #e4e6ea;
	border-radius: 20rpx;
	padding: 32rpx;
	margin: 24rpx 0 40rpx;
	position: relative;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 6rpx;
		height: 100%;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}
}

.info-row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.info-label {
	font-size: 28rpx;
	color: #6c757d;
	min-width: 140rpx;
	font-weight: 500;
}

.info-value {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 600;
}

/* 数字输入 */
.number-input {
	display: flex;
	align-items: center;
	background: #f8f9fb;
	border: 2rpx solid #e4e6ea;
	border-radius: 16rpx;
	overflow: hidden;
}

.number-btn {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #667eea;
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
	transition: all 0.3s ease;
	
	&:active {
		background: #5a67d8;
	}
	
	&:first-child {
		border-radius: 0;
	}
	
	&:last-child {
		border-radius: 0;
	}
}

.number-value {
	flex: 1;
	height: 80rpx;
	text-align: center;
	font-size: 30rpx;
	font-weight: 600;
	color: #1a1a1a;
	border: none;
	outline: none;
	background: transparent;
}

/* 费用表单区域 */
.payment-form-section {
	background: #f8f9fb;
	border-radius: 16rpx;
	padding: 32rpx;
	margin: 24rpx 0;
}

.amount-display {
	background: #ffffff;
	border: 2rpx solid #e4e6ea;
	border-radius: 16rpx;
	padding: 28rpx 24rpx;
	display: flex;
	align-items: center;
}

.amount-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #667eea;
}

.amount-input {
	background: #ffffff;
	border: 2rpx solid #e4e6ea;
	border-radius: 16rpx;
	padding: 28rpx 24rpx;
	font-size: 30rpx;
	color: #1a1a1a;
	width: 100%;
	
	&:focus {
		border-color: #667eea;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}
}

/* 备注文本域 */
.remark-textarea {
	background: #f8f9fb;
	border: 2rpx solid #e4e6ea;
	border-radius: 16rpx;
	padding: 24rpx;
	font-size: 30rpx;
	color: #1a1a1a;
	width: 100%;
	min-height: 140rpx;
	resize: none;
	transition: all 0.3s ease;
	font-family: inherit;
	
	&:focus {
		border-color: #667eea;
		background: #ffffff;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}
	
	&::placeholder {
		color: #9ca3af;
	}
}

/* 弹窗底部 */
.dialog-footer {
	padding: 24rpx 48rpx 48rpx;
	display: flex;
	gap: 24rpx;
	justify-content: flex-end;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fb 100%);
}

.dialog-btn {
	padding: 24rpx 48rpx;
	border-radius: 16rpx;
	font-size: 30rpx;
	font-weight: 600;
	text-align: center;
	min-width: 160rpx;
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.95);
	}
	
	&.cancel {
		background: #f8f9fb;
		color: #6c757d;
		border: 2rpx solid #e4e6ea;
	}
	
	&.confirm {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	}
}

/* 考勤记录弹窗样式 */
.attendance-dialog {
	width: 90%;
	max-width: 700rpx;
	max-height: 80vh;
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: #e9ecef;
		transform: scale(0.9);
	}
}

.student-info {
	padding: 30rpx;
	background: #f8f9fa;
	border-bottom: 1rpx solid #e9ecef;
}

.info-row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.info-label {
	font-size: 26rpx;
	color: #666666;
	min-width: 140rpx;
}

.info-value {
	font-size: 26rpx;
	color: #333333;
	flex: 1;
}

.attendance-content {
	max-height: 60vh;
	overflow-y: auto;
}

.loading-state, .empty-state {
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
}

.attendance-list {
	padding: 20rpx;
}

.attendance-item {
	background: #ffffff;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	border: 1rpx solid #f0f0f0;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

	&:last-child {
		margin-bottom: 0;
	}
}

.attendance-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.attendance-date {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;

	&.success {
		background: #e8f5e8;
		color: #52c41a;
	}

	&.danger {
		background: #ffebee;
		color: #f44336;
	}

	&.warning {
		background: #fff3e0;
		color: #ff9800;
	}

	&.info {
		background: #e3f2fd;
		color: #2196f3;
	}
}

.status-text {
	font-weight: 500;
}

.attendance-details {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
}

.detail-row {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.detail-label {
	font-size: 24rpx;
	color: #666666;
	min-width: 140rpx;
}

.detail-value {
	font-size: 24rpx;
	color: #333333;
	flex: 1;

	&.remark {
		color: #999999;
		font-style: italic;
	}
}

.confirm-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;

	&.confirmed {
		background: #e8f5e8;
		color: #52c41a;
	}

	&.unconfirmed {
		background: #fff3e0;
		color: #ff9800;
	}
}

.confirm-text {
	font-weight: 500;
}
</style>