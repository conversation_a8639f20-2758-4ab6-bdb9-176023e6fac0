import request from '@/utils/request.js'

/**
 * 课程管理相关API接口
 */

// 查询课程列表
export const listCourse = (params) => {
  return request.get('/business/course/list', params)
}

// 查询课程详情
export const getCourse = (courseId) => {
  return request.get(`/business/course/${courseId}`)
}

// 新增课程
export const addCourse = (data) => {
  return request.post('/business/course', data)
}

// 编辑课程
export const updateCourse = (data) => {
  return request.put('/business/course', data)
}

// 删除课程
export const deleteCourse = (courseId) => {
  return request.delete(`/business/course/${courseId}`)
}

// 批量删除课程
export const deleteCourses = (courseIds) => {
  return request.delete(`/business/course/${courseIds.join(',')}`)
}

// 获取所有课程列表（用于选择）
export const listAllCourse = () => {
  return request.get('/business/course/listAll')
}

// 获取有排班的课程列表
export const listWithSchedule = () => {
  return request.get('/business/course/listWithSchedule')
}

// 启用/停用课程
export const changeStatus = (courseId, status) => {
  return request.put('/business/course/changeStatus', {
    courseId,
    status
  })
}

// 导出课程
export const exportCourse = (params) => {
  return request.get('/business/course/export', params)
}
